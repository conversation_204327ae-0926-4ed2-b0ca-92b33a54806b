"""
Simplified multi-timeframe analysis that works reliably.
"""

import pandas as pd
import numpy as np
from typing import Dict
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.client import BinanceDataClient
from indicators.ta_wrappers import add_moving_averages, add_rsi


def simple_mtf_analysis(symbol: str = "BTCUSDT") -> str:
    """Simple multi-timeframe analysis that works."""
    client = BinanceDataClient()
    
    timeframes = {
        '1h': {'interval': '1h', 'limit': 100},
        '4h': {'interval': '4h', 'limit': 100},
        '1d': {'interval': '1d', 'limit': 60}
    }
    
    report = f"""
🎯 SIMPLE MULTI-TIMEFRAME ANALYSIS
{'='*50}

Symbol: {symbol}
Timestamp: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
    
    analysis_data = {}
    
    for tf_name, tf_config in timeframes.items():
        try:
            print(f"📊 Analyzing {tf_name}...")
            
            # Fetch data
            df = client.fetch_historical_klines(
                symbol, tf_config['interval'], tf_config['limit']
            )
            
            if df.empty:
                continue
            
            # Add basic indicators
            df = add_moving_averages(df, short_window=10, long_window=50)
            df = add_rsi(df, window=14)
            
            # Get latest values
            latest = df.iloc[-1]
            
            # Simple trend analysis
            price = latest['close']
            ma_short = latest.get('MA_Short', price)
            ma_long = latest.get('MA_Long', price)
            rsi = latest.get('RSI', 50)
            
            # Determine trend
            if pd.notna(ma_short) and pd.notna(ma_long):
                if ma_short > ma_long:
                    trend = "Bullish"
                    trend_score = 1
                else:
                    trend = "Bearish"
                    trend_score = -1
            else:
                trend = "Neutral"
                trend_score = 0
            
            # RSI condition
            if pd.notna(rsi):
                if rsi > 70:
                    rsi_condition = "Overbought"
                elif rsi < 30:
                    rsi_condition = "Oversold"
                else:
                    rsi_condition = "Neutral"
            else:
                rsi_condition = "N/A"
                rsi = 0
            
            # Price change
            if len(df) > 1:
                price_change = ((price - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100
            else:
                price_change = 0
            
            analysis_data[tf_name] = {
                'price': price,
                'trend': trend,
                'trend_score': trend_score,
                'rsi': rsi,
                'rsi_condition': rsi_condition,
                'price_change': price_change
            }
            
            report += f"""
📈 {tf_name.upper()} TIMEFRAME:
  Price: ${price:,.2f} ({price_change:+.2f}%)
  Trend: {trend} (MA: {ma_short:.0f} vs {ma_long:.0f})
  RSI: {rsi:.1f} ({rsi_condition})
"""
            
        except Exception as e:
            print(f"❌ Error analyzing {tf_name}: {e}")
            continue
    
    # Overall analysis
    if analysis_data:
        total_score = sum(data['trend_score'] for data in analysis_data.values())
        num_timeframes = len(analysis_data)
        
        if total_score > 0:
            overall_trend = "Bullish"
        elif total_score < 0:
            overall_trend = "Bearish"
        else:
            overall_trend = "Neutral"
        
        alignment = abs(total_score) / num_timeframes if num_timeframes > 0 else 0
        
        report += f"""

🎯 OVERALL ANALYSIS:
{'='*30}
Overall Trend: {overall_trend}
Alignment Score: {alignment:.1f}/1.0
Bullish Timeframes: {sum(1 for d in analysis_data.values() if d['trend_score'] > 0)}
Bearish Timeframes: {sum(1 for d in analysis_data.values() if d['trend_score'] < 0)}

📊 TRADING SIGNAL:
"""
        
        if alignment >= 0.7:
            if overall_trend == "Bullish":
                signal = "🟢 STRONG BUY"
                confidence = "High"
            else:
                signal = "🔴 STRONG SELL"
                confidence = "High"
        elif alignment >= 0.3:
            if overall_trend == "Bullish":
                signal = "🟡 WEAK BUY"
                confidence = "Medium"
            else:
                signal = "🟡 WEAK SELL"
                confidence = "Medium"
        else:
            signal = "⚪ HOLD"
            confidence = "Low"
        
        report += f"""
Signal: {signal}
Confidence: {confidence}
Alignment: {alignment:.1%}

⚠️ DISCLAIMER: This is for educational purposes only.
Not financial advice. Always do your own research.
"""
    
    return report


def main():
    """Run simple multi-timeframe analysis."""
    try:
        report = simple_mtf_analysis("BTCUSDT")
        print(report)
        
        # Save to file
        filename = f"simple_mtf_analysis_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")


if __name__ == "__main__":
    main()
