-- Bitcoin trading data schema
CREATE TABLE IF NOT EXISTS candles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    open_time INTEGER NOT NULL,
    close_time INTEGER NOT NULL,
    open_price REAL NOT NULL,
    high_price REAL NOT NULL,
    low_price REAL NOT NULL,
    close_price REAL NOT NULL,
    volume REAL NOT NULL,
    quote_asset_volume REAL,
    number_of_trades INTEGER,
    taker_buy_base_asset_volume REAL,
    taker_buy_quote_asset_volume REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, open_time)
);

-- Index for fast queries
CREATE INDEX IF NOT EXISTS idx_candles_symbol_time ON candles(symbol, open_time);
CREATE INDEX IF NOT EXISTS idx_candles_close_time ON candles(close_time);

-- Table for storing trading signals
CREATE TABLE IF NOT EXISTS signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    signal_type TEXT NOT NULL, -- 'BUY', 'SELL', 'HOLD'
    confidence REAL,
    price REAL,
    indicators TEXT, -- JSON string with indicator values
    model_prediction INTEGER, -- 0 or 1
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for backtest results
CREATE TABLE IF NOT EXISTS backtest_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    start_date TEXT NOT NULL,
    end_date TEXT NOT NULL,
    initial_capital REAL NOT NULL,
    final_capital REAL NOT NULL,
    total_return REAL NOT NULL,
    sharpe_ratio REAL,
    max_drawdown REAL,
    win_rate REAL,
    total_trades INTEGER,
    parameters TEXT, -- JSON string with strategy parameters
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for position tracking
CREATE TABLE IF NOT EXISTS positions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    side TEXT NOT NULL, -- 'LONG', 'SHORT'
    entry_price REAL NOT NULL,
    quantity REAL NOT NULL,
    stop_loss REAL,
    take_profit REAL,
    entry_time INTEGER NOT NULL,
    exit_time INTEGER,
    exit_price REAL,
    pnl REAL,
    status TEXT DEFAULT 'OPEN', -- 'OPEN', 'CLOSED'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
