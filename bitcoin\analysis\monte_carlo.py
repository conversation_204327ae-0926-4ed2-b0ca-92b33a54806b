"""
Monte Carlo simulation for strategy robustness testing.
Analyzes strategy performance under various market scenarios.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy


class MonteCarloSimulator:
    """
    Monte Carlo simulation for trading strategy analysis.
    Tests strategy robustness under different market conditions.
    """

    def __init__(self,
                 initial_cash: float = 10000,
                 commission: float = 0.001):
        self.initial_cash = initial_cash
        self.commission = commission
        self.runner = BacktestRunner(initial_cash, commission)

    def run_monte_carlo_simulation(self,
                                 base_data: pd.DataFrame,
                                 strategy_class,
                                 strategy_params: Dict,
                                 n_simulations: int = 1000,
                                 noise_level: float = 0.02) -> Dict[str, Any]:
        """
        Run Monte Carlo simulation with price noise.

        Args:
            base_data: Historical price data
            strategy_class: Strategy class to test
            strategy_params: Strategy parameters
            n_simulations: Number of simulations
            noise_level: Level of random noise to add

        Returns:
            Dictionary with simulation results
        """
        print(f"🎲 Running Monte Carlo simulation with {n_simulations} iterations...")

        results = {
            'returns': [],
            'sharpe_ratios': [],
            'max_drawdowns': [],
            'win_rates': [],
            'total_trades': [],
            'profit_factors': [],
            'simulation_data': []
        }

        successful_sims = 0

        for sim in range(n_simulations):
            if (sim + 1) % 100 == 0:
                print(f"Completed {sim + 1}/{n_simulations} simulations...")

            try:
                # Generate noisy data
                noisy_data = self._add_price_noise(base_data, noise_level)

                # Run backtest
                result = self.runner.run_backtest(strategy_class, noisy_data, strategy_params)

                # Store results
                results['returns'].append(result['total_return'])
                results['sharpe_ratios'].append(result['metrics'].get('sharpe_ratio', 0))
                results['max_drawdowns'].append(result['metrics'].get('max_drawdown', 0))
                results['win_rates'].append(result['metrics'].get('win_rate', 0))
                results['total_trades'].append(result['metrics'].get('total_trades', 0))
                results['profit_factors'].append(result['metrics'].get('profit_factor', 0))

                successful_sims += 1

            except Exception as e:
                # Skip failed simulations
                continue

        print(f"✅ Completed {successful_sims} successful simulations")

        # Calculate statistics only if we have results
        if successful_sims > 0:
            results['statistics'] = self._calculate_monte_carlo_stats(results)
        else:
            results['statistics'] = {}
        results['n_successful'] = successful_sims
        results['success_rate'] = successful_sims / n_simulations

        return results

    def run_bootstrap_simulation(self,
                               base_data: pd.DataFrame,
                               strategy_class,
                               strategy_params: Dict,
                               n_simulations: int = 1000,
                               block_size: int = 50) -> Dict[str, Any]:
        """
        Run bootstrap simulation by resampling historical data blocks.

        Args:
            base_data: Historical price data
            strategy_class: Strategy class to test
            strategy_params: Strategy parameters
            n_simulations: Number of simulations
            block_size: Size of blocks for resampling

        Returns:
            Dictionary with simulation results
        """
        print(f"🔄 Running Bootstrap simulation with {n_simulations} iterations...")

        results = {
            'returns': [],
            'sharpe_ratios': [],
            'max_drawdowns': [],
            'win_rates': [],
            'total_trades': [],
            'profit_factors': []
        }

        successful_sims = 0

        for sim in range(n_simulations):
            if (sim + 1) % 100 == 0:
                print(f"Completed {sim + 1}/{n_simulations} simulations...")

            try:
                # Generate bootstrap sample
                bootstrap_data = self._bootstrap_resample(base_data, block_size)

                # Run backtest
                result = self.runner.run_backtest(strategy_class, bootstrap_data, strategy_params)

                # Store results
                results['returns'].append(result['total_return'])
                results['sharpe_ratios'].append(result['metrics'].get('sharpe_ratio', 0))
                results['max_drawdowns'].append(result['metrics'].get('max_drawdown', 0))
                results['win_rates'].append(result['metrics'].get('win_rate', 0))
                results['total_trades'].append(result['metrics'].get('total_trades', 0))
                results['profit_factors'].append(result['metrics'].get('profit_factor', 0))

                successful_sims += 1

            except Exception:
                continue

        print(f"✅ Completed {successful_sims} successful simulations")

        # Calculate statistics
        results['statistics'] = self._calculate_monte_carlo_stats(results)
        results['n_successful'] = successful_sims
        results['success_rate'] = successful_sims / n_simulations

        return results

    def run_parameter_sensitivity_analysis(self,
                                         base_data: pd.DataFrame,
                                         strategy_class,
                                         base_params: Dict,
                                         param_variations: Dict[str, float],
                                         n_simulations: int = 500) -> Dict[str, Any]:
        """
        Analyze sensitivity to parameter variations.

        Args:
            base_data: Historical price data
            strategy_class: Strategy class to test
            base_params: Base strategy parameters
            param_variations: Parameter variation ranges (param_name: variation_pct)
            n_simulations: Number of simulations

        Returns:
            Dictionary with sensitivity analysis results
        """
        print(f"🎯 Running parameter sensitivity analysis...")

        results = {
            'parameter_sets': [],
            'returns': [],
            'sharpe_ratios': [],
            'max_drawdowns': []
        }

        for sim in range(n_simulations):
            if (sim + 1) % 100 == 0:
                print(f"Completed {sim + 1}/{n_simulations} parameter variations...")

            # Generate random parameter variation
            varied_params = base_params.copy()

            for param_name, variation_pct in param_variations.items():
                if param_name in base_params:
                    base_value = base_params[param_name]
                    variation = np.random.uniform(-variation_pct, variation_pct)

                    if isinstance(base_value, int):
                        new_value = max(1, int(base_value * (1 + variation)))
                    else:
                        new_value = max(0.1, base_value * (1 + variation))

                    varied_params[param_name] = new_value

            try:
                # Run backtest with varied parameters
                result = self.runner.run_backtest(strategy_class, base_data, varied_params)

                # Store results
                results['parameter_sets'].append(varied_params.copy())
                results['returns'].append(result['total_return'])
                results['sharpe_ratios'].append(result['metrics'].get('sharpe_ratio', 0))
                results['max_drawdowns'].append(result['metrics'].get('max_drawdown', 0))

            except Exception:
                continue

        # Calculate sensitivity statistics
        results['sensitivity_stats'] = self._calculate_sensitivity_stats(results, param_variations)

        return results

    def _add_price_noise(self, data: pd.DataFrame, noise_level: float) -> pd.DataFrame:
        """Add random noise to price data."""
        noisy_data = data.copy()

        # Calculate returns
        returns = data['close'].pct_change().dropna()

        # Generate noise
        noise = np.random.normal(0, noise_level, len(data))

        # Apply noise to returns
        noisy_returns = returns.values
        noisy_returns[1:] += noise[1:len(noisy_returns)+1]

        # Reconstruct prices
        noisy_prices = [data['close'].iloc[0]]
        for ret in noisy_returns[1:]:
            new_price = noisy_prices[-1] * (1 + ret)
            noisy_prices.append(max(new_price, 1))  # Prevent negative prices

        # Update OHLC data proportionally
        price_ratio = np.array(noisy_prices) / data['close'].values

        noisy_data['open'] = data['open'] * price_ratio
        noisy_data['high'] = data['high'] * price_ratio
        noisy_data['low'] = data['low'] * price_ratio
        noisy_data['close'] = noisy_prices

        return noisy_data

    def _bootstrap_resample(self, data: pd.DataFrame, block_size: int) -> pd.DataFrame:
        """Bootstrap resample data in blocks."""
        n_blocks = len(data) // block_size

        if n_blocks < 2:
            return data.copy()

        # Create blocks
        blocks = []
        for i in range(n_blocks):
            start_idx = i * block_size
            end_idx = min(start_idx + block_size, len(data))
            blocks.append(data.iloc[start_idx:end_idx].copy())

        # Randomly sample blocks with replacement
        sampled_blocks = np.random.choice(len(blocks), size=n_blocks, replace=True)

        # Concatenate sampled blocks
        resampled_data = pd.concat([blocks[i] for i in sampled_blocks], ignore_index=True)

        # Reset index to maintain time series structure
        resampled_data.index = data.index[:len(resampled_data)]

        return resampled_data

    def _calculate_monte_carlo_stats(self, results: Dict) -> Dict[str, Any]:
        """Calculate Monte Carlo simulation statistics."""
        stats = {}

        for metric in ['returns', 'sharpe_ratios', 'max_drawdowns', 'win_rates', 'profit_factors']:
            if metric in results and results[metric]:
                values = np.array(results[metric])

                stats[metric] = {
                    'mean': np.mean(values),
                    'median': np.median(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'percentile_5': np.percentile(values, 5),
                    'percentile_25': np.percentile(values, 25),
                    'percentile_75': np.percentile(values, 75),
                    'percentile_95': np.percentile(values, 95),
                    'positive_rate': np.mean(values > 0) * 100 if metric == 'returns' else None
                }

        return stats

    def _calculate_sensitivity_stats(self, results: Dict, param_variations: Dict) -> Dict[str, Any]:
        """Calculate parameter sensitivity statistics."""
        sensitivity_stats = {}

        if not results['parameter_sets']:
            return sensitivity_stats

        # Convert to DataFrame for easier analysis
        param_df = pd.DataFrame(results['parameter_sets'])
        param_df['returns'] = results['returns']
        param_df['sharpe_ratios'] = results['sharpe_ratios']
        param_df['max_drawdowns'] = results['max_drawdowns']

        # Calculate correlations
        for param_name in param_variations.keys():
            if param_name in param_df.columns:
                sensitivity_stats[param_name] = {
                    'return_correlation': param_df[param_name].corr(param_df['returns']),
                    'sharpe_correlation': param_df[param_name].corr(param_df['sharpe_ratios']),
                    'drawdown_correlation': param_df[param_name].corr(param_df['max_drawdowns'])
                }

        return sensitivity_stats

    def generate_monte_carlo_report(self, results: Dict[str, Any], simulation_type: str = "Monte Carlo") -> str:
        """Generate comprehensive Monte Carlo analysis report."""
        stats = results['statistics']

        report = f"""
🎲 {simulation_type.upper()} SIMULATION REPORT
{'='*50}

📊 SIMULATION OVERVIEW
{'-'*30}
Total Simulations: {results.get('n_successful', 0)}
Success Rate: {results.get('success_rate', 0)*100:.1f}%
"""

        if not stats or 'returns' not in stats:
            report += """
❌ SIMULATION FAILED
{'-'*30}
No successful simulations completed.
This may indicate:
- Strategy parameters are invalid
- Data quality issues
- Insufficient data for backtesting

Please check your strategy configuration and data.
"""
            return report

        report += f"""
📈 RETURN ANALYSIS
{'-'*30}
Mean Return: {stats['returns']['mean']:.2f}%
Median Return: {stats['returns']['median']:.2f}%
Standard Deviation: {stats['returns']['std']:.2f}%
Best Case (95th percentile): {stats['returns']['percentile_95']:.2f}%
Worst Case (5th percentile): {stats['returns']['percentile_5']:.2f}%
Probability of Profit: {stats['returns']['positive_rate']:.1f}%

📊 RISK METRICS
{'-'*30}
Mean Sharpe Ratio: {stats['sharpe_ratios']['mean']:.2f}
Median Sharpe Ratio: {stats['sharpe_ratios']['median']:.2f}
Mean Max Drawdown: {stats['max_drawdowns']['mean']:.2f}%
Worst Drawdown (5th percentile): {stats['max_drawdowns']['percentile_5']:.2f}%

🎯 TRADING METRICS
{'-'*30}
Mean Win Rate: {stats['win_rates']['mean']:.1f}%
Mean Profit Factor: {stats['profit_factors']['mean']:.2f}
Mean Total Trades: {stats['total_trades']['mean']:.0f}

📊 CONFIDENCE INTERVALS
{'-'*30}
Return 90% CI: [{stats['returns']['percentile_5']:.2f}%, {stats['returns']['percentile_95']:.2f}%]
Sharpe 90% CI: [{stats['sharpe_ratios']['percentile_5']:.2f}, {stats['sharpe_ratios']['percentile_95']:.2f}]
Drawdown 90% CI: [{stats['max_drawdowns']['percentile_5']:.2f}%, {stats['max_drawdowns']['percentile_95']:.2f}%]

⚠️ RISK ASSESSMENT
{'-'*30}
"""

        # Risk assessment
        prob_loss = 100 - stats['returns']['positive_rate']
        worst_case = stats['returns']['percentile_5']

        if prob_loss > 40:
            risk_level = "HIGH"
        elif prob_loss > 25:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"

        report += f"Risk Level: {risk_level}\n"
        report += f"Probability of Loss: {prob_loss:.1f}%\n"
        report += f"Worst Case Scenario: {worst_case:.2f}%\n"

        if worst_case < -20:
            report += "⚠️ WARNING: Potential for significant losses detected\n"

        report += f"""

💡 INTERPRETATION
{'-'*30}
This simulation tests strategy robustness under various market conditions.
Results show the range of possible outcomes and associated probabilities.
Use this information to assess risk tolerance and position sizing.

⚠️ DISCLAIMER
{'-'*30}
Simulations are based on historical data and assumptions.
Future market conditions may differ significantly.
Past performance does not guarantee future results.
"""

        return report


def main():
    """Example usage of MonteCarloSimulator."""
    from data.client import BinanceDataClient

    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=1000)

    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=1000)

    print(f"Loaded {len(df)} candles for Monte Carlo simulation")

    # Initialize simulator
    simulator = MonteCarloSimulator()

    # Strategy parameters
    strategy_params = {
        'ma_short': 10,
        'ma_long': 50,
        'rsi_period': 14,
        'printlog': False
    }

    # Run Monte Carlo simulation
    print("\n🎲 Running Monte Carlo simulation...")
    mc_results = simulator.run_monte_carlo_simulation(
        df, MARSIStrategy, strategy_params, n_simulations=100, noise_level=0.01
    )

    # Generate report
    mc_report = simulator.generate_monte_carlo_report(mc_results, "Monte Carlo")
    print(mc_report)

    # Run parameter sensitivity analysis
    print("\n🎯 Running parameter sensitivity analysis...")
    param_variations = {
        'ma_short': 0.3,  # ±30% variation
        'ma_long': 0.3,   # ±30% variation
        'rsi_period': 0.2  # ±20% variation
    }

    sensitivity_results = simulator.run_parameter_sensitivity_analysis(
        df, MARSIStrategy, strategy_params, param_variations, n_simulations=50
    )

    print("\n📊 Parameter Sensitivity Results:")
    for param, stats in sensitivity_results['sensitivity_stats'].items():
        print(f"{param}:")
        print(f"  Return correlation: {stats['return_correlation']:.3f}")
        print(f"  Sharpe correlation: {stats['sharpe_correlation']:.3f}")

    # Save results
    filename = f"monte_carlo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(filename, 'w') as f:
        f.write(mc_report)
    print(f"\n📄 Report saved to: {filename}")


if __name__ == "__main__":
    main()
