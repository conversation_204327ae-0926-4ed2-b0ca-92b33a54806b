"""
Tests for risk management functionality.
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from risk.position_sizing import (
    RiskManager, PositionSizer, calculate_risk_metrics
)


class TestRiskManager:
    """Test cases for RiskManager."""
    
    @pytest.fixture
    def risk_manager(self):
        """Create RiskManager instance."""
        return RiskManager(
            max_risk_per_trade=0.02,  # 2%
            max_portfolio_risk=0.10,  # 10%
            max_correlation=0.7,
            max_drawdown_limit=0.15   # 15%
        )
    
    @pytest.fixture
    def sample_returns(self):
        """Create sample return series."""
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
        return pd.Series(returns)
    
    def test_risk_manager_initialization(self, risk_manager):
        """Test RiskManager initialization."""
        assert risk_manager.max_risk_per_trade == 0.02
        assert risk_manager.max_portfolio_risk == 0.10
        assert risk_manager.max_correlation == 0.7
        assert risk_manager.max_drawdown_limit == 0.15
        assert len(risk_manager.current_positions) == 0
        assert risk_manager.portfolio_value == 0
    
    def test_fixed_risk_position_sizing(self, risk_manager):
        """Test fixed risk position sizing calculation."""
        capital = 10000
        entry_price = 50000
        stop_loss = 48000
        
        position_size = risk_manager.calculate_position_size_fixed_risk(
            capital, entry_price, stop_loss
        )
        
        # Check that position size is reasonable
        assert position_size > 0
        assert position_size < capital / entry_price  # Can't buy more than capital allows
        
        # Check risk calculation
        risk_amount = position_size * (entry_price - stop_loss)
        expected_risk = capital * risk_manager.max_risk_per_trade
        assert abs(risk_amount - expected_risk) < 1.0
    
    def test_invalid_stop_loss(self, risk_manager):
        """Test handling of invalid stop loss."""
        capital = 10000
        entry_price = 50000
        stop_loss = 52000  # Stop above entry for long position
        
        position_size = risk_manager.calculate_position_size_fixed_risk(
            capital, entry_price, stop_loss
        )
        
        # Should return 0 for invalid stop
        assert position_size == 0
    
    def test_atr_stop_target_calculation(self, risk_manager):
        """Test ATR-based stop and target calculation."""
        # Create sample data with ATR
        df = pd.DataFrame({
            'close': [50000, 50500, 49800, 51000, 50200],
            'ATR': [500, 520, 480, 510, 495]
        })
        
        result_df = risk_manager.calculate_atr_stop_target(df)
        
        # Check that stop and target columns are added
        assert 'stop_loss' in result_df.columns
        assert 'take_profit' in result_df.columns
        assert 'stop_loss_short' in result_df.columns
        assert 'take_profit_short' in result_df.columns
        
        # Check relationships
        assert (result_df['stop_loss'] < result_df['close']).all()
        assert (result_df['take_profit'] > result_df['close']).all()
        assert (result_df['stop_loss_short'] > result_df['close']).all()
        assert (result_df['take_profit_short'] < result_df['close']).all()
    
    def test_kelly_criterion(self, risk_manager):
        """Test Kelly Criterion calculation."""
        # Test with profitable system
        win_rate = 0.6
        avg_win = 150
        avg_loss = 100
        
        kelly_fraction = risk_manager.calculate_kelly_criterion(win_rate, avg_win, avg_loss)
        
        # Kelly should be positive for profitable system
        assert kelly_fraction > 0
        assert kelly_fraction <= 0.25  # Should be capped at 25%
        
        # Test with unprofitable system
        kelly_fraction = risk_manager.calculate_kelly_criterion(0.3, 100, 200)
        assert kelly_fraction == 0  # Should be 0 for unprofitable system
    
    def test_kelly_criterion_edge_cases(self, risk_manager):
        """Test Kelly Criterion edge cases."""
        # Zero average loss
        kelly = risk_manager.calculate_kelly_criterion(0.6, 150, 0)
        assert kelly == 0
        
        # Invalid win rate
        kelly = risk_manager.calculate_kelly_criterion(0, 150, 100)
        assert kelly == 0
        
        kelly = risk_manager.calculate_kelly_criterion(1, 150, 100)
        assert kelly == 0
    
    def test_volatility_position_sizing(self, risk_manager):
        """Test volatility-based position sizing."""
        capital = 10000
        target_volatility = 0.02  # 2% target
        asset_volatility = 0.04   # 4% asset volatility
        
        position_size = risk_manager.calculate_volatility_position_size(
            capital, target_volatility, asset_volatility
        )
        
        # Position should be scaled down due to higher volatility
        assert position_size == capital * 0.5  # 2%/4% = 0.5
        
        # Test with zero volatility
        position_size = risk_manager.calculate_volatility_position_size(
            capital, target_volatility, 0
        )
        assert position_size == 0
    
    def test_sharpe_optimal_sizing(self, risk_manager, sample_returns):
        """Test Sharpe-optimal position sizing."""
        optimal_size = risk_manager.calculate_sharpe_optimal_size(sample_returns)
        
        # Should return a reasonable leverage
        assert optimal_size >= 0
        assert optimal_size <= 2.0  # Should be capped
        
        # Test with insufficient data
        short_returns = sample_returns[:10]
        optimal_size = risk_manager.calculate_sharpe_optimal_size(short_returns)
        assert optimal_size == 0
    
    def test_correlation_risk_check(self, risk_manager, sample_returns):
        """Test correlation risk checking."""
        # Create correlated returns
        correlated_returns = sample_returns * 0.8 + np.random.normal(0, 0.01, len(sample_returns))
        correlated_returns = pd.Series(correlated_returns, index=sample_returns.index)
        
        existing_positions = {"BTC": sample_returns}
        
        # High correlation should be rejected
        is_safe = risk_manager.check_correlation_risk(correlated_returns, existing_positions)
        assert not is_safe
        
        # Uncorrelated returns should be accepted
        uncorrelated_returns = pd.Series(np.random.normal(0, 0.02, len(sample_returns)), 
                                       index=sample_returns.index)
        is_safe = risk_manager.check_correlation_risk(uncorrelated_returns, existing_positions)
        assert is_safe
        
        # No existing positions should be safe
        is_safe = risk_manager.check_correlation_risk(sample_returns, {})
        assert is_safe
    
    def test_portfolio_state_tracking(self, risk_manager):
        """Test portfolio state tracking."""
        # Update portfolio value
        risk_manager.update_portfolio_state(12000)
        assert risk_manager.portfolio_value == 12000
        assert risk_manager.max_portfolio_value == 12000
        assert risk_manager.current_drawdown == 0
        
        # Simulate drawdown
        risk_manager.update_portfolio_state(10000)
        expected_drawdown = (12000 - 10000) / 12000
        assert abs(risk_manager.current_drawdown - expected_drawdown) < 1e-10
        
        # Recovery
        risk_manager.update_portfolio_state(13000)
        assert risk_manager.max_portfolio_value == 13000
        assert risk_manager.current_drawdown == 0
    
    def test_drawdown_protection(self, risk_manager):
        """Test drawdown protection mechanism."""
        # Set portfolio to max value
        risk_manager.update_portfolio_state(10000)
        assert not risk_manager.should_stop_trading()
        
        # Simulate large drawdown
        risk_manager.update_portfolio_state(8000)  # 20% drawdown
        assert risk_manager.should_stop_trading()  # Should stop at 15% limit
        
        # Smaller drawdown should be OK
        risk_manager.update_portfolio_state(8600)  # 14% drawdown
        assert not risk_manager.should_stop_trading()
    
    def test_trailing_stop_calculation(self, risk_manager):
        """Test trailing stop calculation."""
        entry_price = 50000
        current_price = 52000
        highest_price = 53000
        atr = 500
        
        trailing_stop = risk_manager.calculate_trailing_stop(
            entry_price, current_price, highest_price, atr, trail_mult=2.0
        )
        
        # Trailing stop should be below highest price
        assert trailing_stop < highest_price
        
        # Should be above initial stop
        initial_stop = entry_price - (2.0 * atr)
        assert trailing_stop >= initial_stop
    
    def test_position_summary(self, risk_manager):
        """Test position summary generation."""
        # Empty portfolio
        summary = risk_manager.get_position_summary()
        assert summary['total_positions'] == 0
        assert summary['total_risk_amount'] == 0
        assert summary['portfolio_risk_percentage'] == 0
        assert summary['trading_allowed'] == True
        
        # Add mock position
        risk_manager.current_positions['BTC'] = {'risk_amount': 200}
        risk_manager.update_portfolio_state(10000)
        
        summary = risk_manager.get_position_summary()
        assert summary['total_positions'] == 1
        assert summary['total_risk_amount'] == 200
        assert summary['portfolio_risk_percentage'] == 2.0  # 200/10000 * 100


class TestPositionSizer:
    """Test cases for PositionSizer utility class."""
    
    def test_fixed_risk_size(self):
        """Test fixed risk position sizing."""
        capital = 10000
        entry = 50000
        stop = 48000
        risk_pct = 0.02
        
        size = PositionSizer.fixed_risk_size(capital, entry, stop, risk_pct)
        
        assert size > 0
        assert isinstance(size, int)
        
        # Check risk calculation
        risk_amount = size * (entry - stop)
        expected_risk = capital * risk_pct
        assert abs(risk_amount - expected_risk) < entry  # Allow some rounding error
    
    def test_percent_of_capital(self):
        """Test percent of capital position sizing."""
        capital = 10000
        price = 50000
        percent = 0.1
        
        size = PositionSizer.percent_of_capital(capital, price, percent)
        
        expected_size = int((capital * percent) / price)
        assert size == expected_size
        assert isinstance(size, int)
    
    def test_volatility_adjusted(self):
        """Test volatility-adjusted position sizing."""
        capital = 10000
        price = 50000
        volatility = 0.04
        target_vol = 0.02
        
        size = PositionSizer.volatility_adjusted(capital, price, volatility, target_vol)
        
        assert size > 0
        assert isinstance(size, int)
        
        # Should be smaller due to higher volatility
        base_size = PositionSizer.percent_of_capital(capital, price, 0.1)
        assert size < base_size
    
    def test_edge_cases(self):
        """Test edge cases for PositionSizer."""
        # Invalid prices
        assert PositionSizer.fixed_risk_size(10000, 0, 1000) == 0
        assert PositionSizer.percent_of_capital(10000, 0) == 0
        assert PositionSizer.volatility_adjusted(10000, 0, 0.02) == 0
        
        # Invalid stop (above entry)
        assert PositionSizer.fixed_risk_size(10000, 50000, 52000) == 0


class TestRiskMetrics:
    """Test cases for risk metrics calculation."""
    
    @pytest.fixture
    def sample_returns(self):
        """Create sample return series."""
        np.random.seed(42)
        # Create returns with some trend and volatility
        returns = np.random.normal(0.0005, 0.02, 252)  # Slight positive drift
        return pd.Series(returns)
    
    def test_calculate_risk_metrics(self, sample_returns):
        """Test comprehensive risk metrics calculation."""
        metrics = calculate_risk_metrics(sample_returns)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'total_return', 'annualized_return', 'volatility', 'sharpe_ratio',
            'sortino_ratio', 'max_drawdown', 'current_drawdown', 'var_95',
            'var_99', 'expected_shortfall'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
        
        # Check metric ranges and relationships
        assert -1 <= metrics['total_return'] <= 10  # Reasonable range
        assert 0 <= metrics['volatility'] <= 2     # Reasonable volatility
        assert metrics['max_drawdown'] <= 0        # Should be negative
        assert metrics['var_95'] <= 0              # Should be negative
        assert metrics['var_99'] <= metrics['var_95']  # 99% VaR should be worse
        assert metrics['expected_shortfall'] <= metrics['var_95']
    
    def test_empty_returns(self):
        """Test handling of empty returns."""
        empty_returns = pd.Series([])
        metrics = calculate_risk_metrics(empty_returns)
        
        # Should return empty dict
        assert metrics == {}
    
    def test_constant_returns(self):
        """Test handling of constant returns."""
        constant_returns = pd.Series([0.01] * 100)
        metrics = calculate_risk_metrics(constant_returns)
        
        # Volatility should be zero
        assert metrics['volatility'] == 0
        
        # Sharpe ratio should be infinite or very large
        assert metrics['sharpe_ratio'] >= 100 or np.isinf(metrics['sharpe_ratio'])
        
        # No drawdown with constant positive returns
        assert metrics['max_drawdown'] == 0
    
    def test_negative_returns(self):
        """Test handling of consistently negative returns."""
        negative_returns = pd.Series([-0.01] * 100)
        metrics = calculate_risk_metrics(negative_returns)
        
        # Total return should be negative
        assert metrics['total_return'] < 0
        
        # Sharpe ratio should be negative
        assert metrics['sharpe_ratio'] < 0
        
        # Should have significant drawdown
        assert metrics['max_drawdown'] < -0.5


if __name__ == "__main__":
    pytest.main([__file__])
