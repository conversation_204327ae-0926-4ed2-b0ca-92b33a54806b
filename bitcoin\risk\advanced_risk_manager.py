"""
Advanced Risk Management System for Enterprise Trading.
Implements sophisticated risk controls, position sizing, and portfolio management.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    POSITION_SIZE = "position_size"
    DRAWDOWN = "drawdown"
    CORRELATION = "correlation"
    VOLATILITY = "volatility"
    CONCENTRATION = "concentration"
    VAR_BREACH = "var_breach"


@dataclass
class RiskAlert:
    alert_type: AlertType
    level: RiskLevel
    message: str
    value: float
    threshold: float
    timestamp: datetime
    symbol: Optional[str] = None


@dataclass
class PositionRisk:
    symbol: str
    quantity: float
    market_value: float
    unrealized_pnl: float
    var_1d: float
    var_5d: float
    beta: float
    correlation_btc: float
    concentration_pct: float


class AdvancedRiskManager:
    """
    Enterprise-grade risk management system.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._default_config()
        self.logger = logging.getLogger(__name__)
        self.alerts: List[RiskAlert] = []
        self.positions: Dict[str, PositionRisk] = {}
        
    def _default_config(self) -> Dict[str, Any]:
        """Default risk management configuration."""
        return {
            'max_portfolio_risk': 0.02,  # 2% portfolio VaR
            'max_position_size': 0.10,   # 10% max position size
            'max_drawdown': 0.15,        # 15% max drawdown
            'max_correlation': 0.8,      # 80% max correlation
            'max_concentration': 0.25,   # 25% max single asset
            'var_confidence': 0.95,      # 95% VaR confidence
            'rebalance_threshold': 0.05, # 5% rebalance threshold
            'stop_loss_pct': 0.05,       # 5% stop loss
            'take_profit_pct': 0.15,     # 15% take profit
            'max_leverage': 3.0,         # 3x max leverage
            'volatility_lookback': 30,   # 30 days volatility
            'correlation_lookback': 60   # 60 days correlation
        }
    
    def calculate_position_size(self, 
                              symbol: str,
                              entry_price: float,
                              stop_loss_price: float,
                              portfolio_value: float,
                              volatility: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate optimal position size using multiple methods.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price
            stop_loss_price: Stop loss price
            portfolio_value: Total portfolio value
            volatility: Asset volatility (optional)
            
        Returns:
            Position sizing recommendations
        """
        risk_per_trade = self.config['max_portfolio_risk']
        max_position_pct = self.config['max_position_size']
        
        # Method 1: Fixed Risk per Trade
        risk_amount = portfolio_value * risk_per_trade
        price_risk = abs(entry_price - stop_loss_price)
        fixed_risk_size = risk_amount / price_risk if price_risk > 0 else 0
        
        # Method 2: Volatility-based sizing
        vol_size = 0
        if volatility:
            target_vol = 0.02  # 2% target volatility
            vol_size = (target_vol * portfolio_value) / (volatility * entry_price)
        
        # Method 3: Kelly Criterion (simplified)
        win_rate = 0.55  # Assumed win rate
        avg_win = 0.08   # Assumed average win
        avg_loss = 0.04  # Assumed average loss
        
        if avg_loss > 0:
            kelly_f = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
            kelly_size = max(0, min(kelly_f * portfolio_value / entry_price, 
                                  portfolio_value * 0.25 / entry_price))
        else:
            kelly_size = 0
        
        # Method 4: Maximum position size
        max_size = (portfolio_value * max_position_pct) / entry_price
        
        # Choose the most conservative size
        recommended_size = min(filter(lambda x: x > 0, [
            fixed_risk_size, vol_size or float('inf'), 
            kelly_size, max_size
        ]))
        
        return {
            'recommended_size': recommended_size,
            'fixed_risk_size': fixed_risk_size,
            'volatility_size': vol_size,
            'kelly_size': kelly_size,
            'max_size': max_size,
            'position_value': recommended_size * entry_price,
            'position_pct': (recommended_size * entry_price) / portfolio_value * 100,
            'risk_amount': risk_amount,
            'method_used': 'conservative'
        }
    
    def calculate_var(self, 
                     returns: pd.Series, 
                     confidence: float = 0.95,
                     holding_period: int = 1) -> float:
        """
        Calculate Value at Risk (VaR).
        
        Args:
            returns: Historical returns
            confidence: Confidence level
            holding_period: Holding period in days
            
        Returns:
            VaR value
        """
        if len(returns) < 30:
            return 0.0
        
        # Historical VaR
        var_percentile = (1 - confidence) * 100
        var = np.percentile(returns, var_percentile)
        
        # Scale for holding period
        var_scaled = var * np.sqrt(holding_period)
        
        return abs(var_scaled)
    
    def calculate_expected_shortfall(self, 
                                   returns: pd.Series, 
                                   confidence: float = 0.95) -> float:
        """
        Calculate Expected Shortfall (Conditional VaR).
        
        Args:
            returns: Historical returns
            confidence: Confidence level
            
        Returns:
            Expected Shortfall value
        """
        if len(returns) < 30:
            return 0.0
        
        var_percentile = (1 - confidence) * 100
        var_threshold = np.percentile(returns, var_percentile)
        
        # Expected shortfall is the mean of returns below VaR
        tail_returns = returns[returns <= var_threshold]
        
        if len(tail_returns) > 0:
            return abs(tail_returns.mean())
        else:
            return abs(var_threshold)
    
    def calculate_portfolio_risk(self, 
                               positions: Dict[str, Dict],
                               price_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Calculate comprehensive portfolio risk metrics.
        
        Args:
            positions: Dictionary of positions {symbol: {quantity, price}}
            price_data: Dictionary of price series {symbol: price_series}
            
        Returns:
            Portfolio risk metrics
        """
        if not positions or not price_data:
            return {}
        
        # Calculate returns for each asset
        returns_data = {}
        for symbol, prices in price_data.items():
            if len(prices) > 1:
                returns_data[symbol] = prices.pct_change().dropna()
        
        if not returns_data:
            return {}
        
        # Portfolio weights and values
        total_value = sum(pos['quantity'] * pos['price'] for pos in positions.values())
        weights = {}
        
        for symbol, pos in positions.items():
            position_value = pos['quantity'] * pos['price']
            weights[symbol] = position_value / total_value if total_value > 0 else 0
        
        # Calculate portfolio returns
        portfolio_returns = pd.Series(0, index=list(returns_data.values())[0].index)
        
        for symbol, weight in weights.items():
            if symbol in returns_data:
                portfolio_returns += weight * returns_data[symbol]
        
        # Risk metrics
        portfolio_vol = portfolio_returns.std() * np.sqrt(252)  # Annualized
        portfolio_var_1d = self.calculate_var(portfolio_returns, 0.95, 1)
        portfolio_var_5d = self.calculate_var(portfolio_returns, 0.95, 5)
        portfolio_es = self.calculate_expected_shortfall(portfolio_returns, 0.95)
        
        # Maximum drawdown
        cumulative_returns = (1 + portfolio_returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Sharpe ratio
        excess_returns = portfolio_returns - 0.02/252  # Assume 2% risk-free rate
        sharpe_ratio = excess_returns.mean() / portfolio_returns.std() * np.sqrt(252)
        
        return {
            'total_value': total_value,
            'volatility': portfolio_vol,
            'var_1d': portfolio_var_1d,
            'var_5d': portfolio_var_5d,
            'expected_shortfall': portfolio_es,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'weights': weights,
            'returns': portfolio_returns
        }
    
    def check_risk_limits(self, 
                         portfolio_metrics: Dict[str, Any],
                         positions: Dict[str, Dict]) -> List[RiskAlert]:
        """
        Check portfolio against risk limits and generate alerts.
        
        Args:
            portfolio_metrics: Portfolio risk metrics
            positions: Current positions
            
        Returns:
            List of risk alerts
        """
        alerts = []
        
        # Check portfolio VaR
        if portfolio_metrics.get('var_1d', 0) > self.config['max_portfolio_risk']:
            alerts.append(RiskAlert(
                alert_type=AlertType.VAR_BREACH,
                level=RiskLevel.HIGH,
                message=f"Portfolio VaR ({portfolio_metrics['var_1d']:.2%}) exceeds limit ({self.config['max_portfolio_risk']:.2%})",
                value=portfolio_metrics['var_1d'],
                threshold=self.config['max_portfolio_risk'],
                timestamp=datetime.now()
            ))
        
        # Check maximum drawdown
        if portfolio_metrics.get('max_drawdown', 0) > self.config['max_drawdown']:
            alerts.append(RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                level=RiskLevel.CRITICAL,
                message=f"Maximum drawdown ({portfolio_metrics['max_drawdown']:.2%}) exceeds limit ({self.config['max_drawdown']:.2%})",
                value=portfolio_metrics['max_drawdown'],
                threshold=self.config['max_drawdown'],
                timestamp=datetime.now()
            ))
        
        # Check position concentration
        weights = portfolio_metrics.get('weights', {})
        for symbol, weight in weights.items():
            if weight > self.config['max_concentration']:
                alerts.append(RiskAlert(
                    alert_type=AlertType.CONCENTRATION,
                    level=RiskLevel.MEDIUM,
                    message=f"Position {symbol} ({weight:.2%}) exceeds concentration limit ({self.config['max_concentration']:.2%})",
                    value=weight,
                    threshold=self.config['max_concentration'],
                    timestamp=datetime.now(),
                    symbol=symbol
                ))
        
        # Check volatility
        if portfolio_metrics.get('volatility', 0) > 0.5:  # 50% annual volatility
            alerts.append(RiskAlert(
                alert_type=AlertType.VOLATILITY,
                level=RiskLevel.HIGH,
                message=f"Portfolio volatility ({portfolio_metrics['volatility']:.2%}) is very high",
                value=portfolio_metrics['volatility'],
                threshold=0.5,
                timestamp=datetime.now()
            ))
        
        return alerts
    
    def generate_risk_report(self, 
                           portfolio_metrics: Dict[str, Any],
                           positions: Dict[str, Dict],
                           alerts: List[RiskAlert]) -> str:
        """Generate comprehensive risk report."""
        
        report = f"""
🛡️ ADVANCED RISK MANAGEMENT REPORT
{'='*50}

📊 PORTFOLIO OVERVIEW
{'-'*30}
Total Portfolio Value: ${portfolio_metrics.get('total_value', 0):,.2f}
Number of Positions: {len(positions)}
Portfolio Volatility: {portfolio_metrics.get('volatility', 0)*100:.2f}%
Sharpe Ratio: {portfolio_metrics.get('sharpe_ratio', 0):.2f}

⚠️ RISK METRICS
{'-'*30}
1-Day VaR (95%): {portfolio_metrics.get('var_1d', 0)*100:.2f}%
5-Day VaR (95%): {portfolio_metrics.get('var_5d', 0)*100:.2f}%
Expected Shortfall: {portfolio_metrics.get('expected_shortfall', 0)*100:.2f}%
Maximum Drawdown: {portfolio_metrics.get('max_drawdown', 0)*100:.2f}%

📈 POSITION BREAKDOWN
{'-'*30}
"""
        
        weights = portfolio_metrics.get('weights', {})
        for symbol, weight in sorted(weights.items(), key=lambda x: x[1], reverse=True):
            position_value = positions.get(symbol, {}).get('quantity', 0) * positions.get(symbol, {}).get('price', 0)
            report += f"{symbol}: {weight*100:.1f}% (${position_value:,.2f})\n"
        
        report += f"""

🚨 RISK ALERTS ({len(alerts)})
{'-'*30}
"""
        
        if alerts:
            for alert in alerts:
                level_emoji = {
                    RiskLevel.LOW: "🟢",
                    RiskLevel.MEDIUM: "🟡", 
                    RiskLevel.HIGH: "🟠",
                    RiskLevel.CRITICAL: "🔴"
                }
                
                report += f"{level_emoji[alert.level]} {alert.level.value.upper()}: {alert.message}\n"
        else:
            report += "✅ No risk alerts - Portfolio within limits\n"
        
        report += f"""

📋 RISK LIMITS
{'-'*30}
Max Portfolio Risk: {self.config['max_portfolio_risk']*100:.1f}%
Max Position Size: {self.config['max_position_size']*100:.1f}%
Max Drawdown: {self.config['max_drawdown']*100:.1f}%
Max Concentration: {self.config['max_concentration']*100:.1f}%

💡 RECOMMENDATIONS
{'-'*30}
"""
        
        # Generate recommendations based on current state
        if portfolio_metrics.get('var_1d', 0) > self.config['max_portfolio_risk'] * 0.8:
            report += "• Consider reducing position sizes to lower portfolio risk\n"
        
        if portfolio_metrics.get('max_drawdown', 0) > self.config['max_drawdown'] * 0.7:
            report += "• Implement tighter stop-loss levels\n"
        
        max_weight = max(weights.values()) if weights else 0
        if max_weight > self.config['max_concentration'] * 0.8:
            report += "• Diversify portfolio to reduce concentration risk\n"
        
        if portfolio_metrics.get('sharpe_ratio', 0) < 1.0:
            report += "• Review strategy performance and risk-adjusted returns\n"
        
        report += f"""

⚠️ DISCLAIMER
{'-'*30}
Risk metrics are based on historical data and statistical models.
Market conditions can change rapidly and past performance does not guarantee future results.
Always maintain appropriate risk controls and position sizing.
"""
        
        return report


def main():
    """Example usage of AdvancedRiskManager."""
    # Initialize risk manager
    risk_manager = AdvancedRiskManager()
    
    # Example portfolio
    positions = {
        'BTCUSDT': {'quantity': 0.5, 'price': 45000},
        'ETHUSDT': {'quantity': 2.0, 'price': 3000},
        'ADAUSDT': {'quantity': 1000, 'price': 1.2}
    }
    
    # Example price data (would come from real data in practice)
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    price_data = {
        'BTCUSDT': pd.Series(np.random.normal(45000, 2000, 100), index=dates),
        'ETHUSDT': pd.Series(np.random.normal(3000, 200, 100), index=dates),
        'ADAUSDT': pd.Series(np.random.normal(1.2, 0.1, 100), index=dates)
    }
    
    # Calculate portfolio risk
    portfolio_metrics = risk_manager.calculate_portfolio_risk(positions, price_data)
    
    # Check risk limits
    alerts = risk_manager.check_risk_limits(portfolio_metrics, positions)
    
    # Generate report
    report = risk_manager.generate_risk_report(portfolio_metrics, positions, alerts)
    print(report)
    
    # Position sizing example
    sizing = risk_manager.calculate_position_size(
        symbol='BTCUSDT',
        entry_price=45000,
        stop_loss_price=43000,
        portfolio_value=100000,
        volatility=0.04
    )
    
    print(f"\n📊 Position Sizing Recommendation:")
    print(f"Recommended Size: {sizing['recommended_size']:.4f} BTC")
    print(f"Position Value: ${sizing['position_value']:,.2f}")
    print(f"Portfolio %: {sizing['position_pct']:.2f}%")


if __name__ == "__main__":
    main()
