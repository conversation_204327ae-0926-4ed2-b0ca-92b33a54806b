"""
Walk-forward analysis for robust strategy validation.
Tests strategy performance across different market conditions.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy
from optimization.parameter_optimizer import ParameterOptimizer


class WalkForwardAnalyzer:
    """
    Perform walk-forward analysis to validate strategy robustness.
    """
    
    def __init__(self, 
                 initial_cash: float = 10000,
                 commission: float = 0.001,
                 optimization_window: int = 500,
                 test_window: int = 100,
                 step_size: int = 50):
        """
        Initialize walk-forward analyzer.
        
        Args:
            initial_cash: Starting capital
            commission: Commission rate
            optimization_window: Number of periods for optimization
            test_window: Number of periods for testing
            step_size: Step size for rolling window
        """
        self.initial_cash = initial_cash
        self.commission = commission
        self.optimization_window = optimization_window
        self.test_window = test_window
        self.step_size = step_size
        self.runner = BacktestRunner(initial_cash, commission)
        self.optimizer = ParameterOptimizer(initial_cash, commission)
        
    def run_walk_forward_analysis(self,
                                 data: pd.DataFrame,
                                 strategy_class,
                                 param_ranges: Dict[str, List],
                                 optimization_metric: str = 'sharpe_ratio') -> Dict[str, Any]:
        """
        Run complete walk-forward analysis.
        
        Args:
            data: Historical price data
            strategy_class: Strategy class to test
            param_ranges: Parameter ranges for optimization
            optimization_metric: Metric to optimize
            
        Returns:
            Dictionary with analysis results
        """
        print("🔄 Starting Walk-Forward Analysis...")
        print(f"Data length: {len(data)} periods")
        print(f"Optimization window: {self.optimization_window}")
        print(f"Test window: {self.test_window}")
        print(f"Step size: {self.step_size}")
        
        results = {
            'periods': [],
            'optimization_results': [],
            'test_results': [],
            'parameters_used': [],
            'dates': [],
            'cumulative_returns': [],
            'drawdowns': []
        }
        
        # Calculate number of walk-forward periods
        total_window = self.optimization_window + self.test_window
        max_start = len(data) - total_window
        
        if max_start <= 0:
            raise ValueError("Insufficient data for walk-forward analysis")
        
        num_periods = (max_start // self.step_size) + 1
        print(f"Will run {num_periods} walk-forward periods")
        
        cumulative_value = self.initial_cash
        
        for period in range(num_periods):
            start_idx = period * self.step_size
            opt_end_idx = start_idx + self.optimization_window
            test_end_idx = opt_end_idx + self.test_window
            
            if test_end_idx > len(data):
                break
                
            print(f"\n📊 Period {period + 1}/{num_periods}")
            print(f"Optimization: {data.index[start_idx]} to {data.index[opt_end_idx-1]}")
            print(f"Testing: {data.index[opt_end_idx]} to {data.index[test_end_idx-1]}")
            
            # Split data
            opt_data = data.iloc[start_idx:opt_end_idx].copy()
            test_data = data.iloc[opt_end_idx:test_end_idx].copy()
            
            # Optimization phase
            print("🎯 Optimizing parameters...")
            opt_result = self._optimize_parameters(
                opt_data, strategy_class, param_ranges, optimization_metric
            )
            
            if not opt_result:
                print("❌ Optimization failed, skipping period")
                continue
            
            # Test phase
            print("🧪 Testing optimized parameters...")
            test_result = self._test_parameters(
                test_data, strategy_class, opt_result['best_params']
            )
            
            if not test_result:
                print("❌ Testing failed, skipping period")
                continue
            
            # Update cumulative performance
            period_return = test_result['total_return'] / 100
            cumulative_value *= (1 + period_return)
            
            # Store results
            results['periods'].append(period + 1)
            results['optimization_results'].append(opt_result)
            results['test_results'].append(test_result)
            results['parameters_used'].append(opt_result['best_params'])
            results['dates'].append({
                'opt_start': data.index[start_idx],
                'opt_end': data.index[opt_end_idx-1],
                'test_start': data.index[opt_end_idx],
                'test_end': data.index[test_end_idx-1]
            })
            results['cumulative_returns'].append((cumulative_value / self.initial_cash - 1) * 100)
            
            # Calculate drawdown
            if len(results['cumulative_returns']) > 1:
                peak = max(results['cumulative_returns'])
                current = results['cumulative_returns'][-1]
                drawdown = (current - peak) / (1 + peak/100) * 100
            else:
                drawdown = 0
            results['drawdowns'].append(drawdown)
            
            print(f"✅ Period return: {test_result['total_return']:.2f}%")
            print(f"✅ Cumulative return: {results['cumulative_returns'][-1]:.2f}%")
        
        # Calculate summary statistics
        results['summary'] = self._calculate_summary_stats(results)
        
        print(f"\n🎉 Walk-Forward Analysis Complete!")
        print(f"Total periods: {len(results['periods'])}")
        print(f"Final cumulative return: {results['cumulative_returns'][-1]:.2f}%")
        
        return results
    
    def _optimize_parameters(self,
                           data: pd.DataFrame,
                           strategy_class,
                           param_ranges: Dict[str, List],
                           metric: str) -> Optional[Dict]:
        """Optimize parameters for given data period."""
        try:
            if strategy_class == MARSIStrategy:
                # Use simplified optimization for speed
                best_params = self._grid_search_ma_rsi(data, param_ranges, metric)
            elif strategy_class == SuperTrendStrategy:
                best_params = self._grid_search_supertrend(data, param_ranges, metric)
            else:
                return None
            
            if best_params:
                return {
                    'best_params': best_params['params'],
                    'best_value': best_params['score'],
                    'metric': metric
                }
            
        except Exception as e:
            print(f"❌ Optimization error: {e}")
            
        return None
    
    def _grid_search_ma_rsi(self, data: pd.DataFrame, param_ranges: Dict, metric: str) -> Optional[Dict]:
        """Simple grid search for MA+RSI strategy."""
        best_score = -np.inf
        best_params = None
        
        ma_short_range = param_ranges.get('ma_short', [5, 10, 15])
        ma_long_range = param_ranges.get('ma_long', [20, 30, 50])
        rsi_period_range = param_ranges.get('rsi_period', [10, 14, 20])
        
        for ma_short in ma_short_range:
            for ma_long in ma_long_range:
                for rsi_period in rsi_period_range:
                    if ma_short >= ma_long:
                        continue
                    
                    params = {
                        'ma_short': ma_short,
                        'ma_long': ma_long,
                        'rsi_period': rsi_period,
                        'printlog': False
                    }
                    
                    try:
                        result = self.runner.run_backtest(MARSIStrategy, data, params)
                        
                        if metric == 'sharpe_ratio':
                            score = result['metrics'].get('sharpe_ratio', -999)
                        elif metric == 'total_return':
                            score = result['total_return']
                        else:
                            score = result['metrics'].get('profit_factor', 0)
                        
                        if score > best_score:
                            best_score = score
                            best_params = params
                            
                    except Exception:
                        continue
        
        if best_params:
            return {'params': best_params, 'score': best_score}
        return None
    
    def _grid_search_supertrend(self, data: pd.DataFrame, param_ranges: Dict, metric: str) -> Optional[Dict]:
        """Simple grid search for SuperTrend strategy."""
        best_score = -np.inf
        best_params = None
        
        atr_period_range = param_ranges.get('atr_period', [10, 14, 20])
        atr_mult_range = param_ranges.get('atr_multiplier', [2.0, 3.0, 4.0])
        
        for atr_period in atr_period_range:
            for atr_mult in atr_mult_range:
                params = {
                    'atr_period': atr_period,
                    'atr_multiplier': atr_mult,
                    'printlog': False
                }
                
                try:
                    result = self.runner.run_backtest(SuperTrendStrategy, data, params)
                    
                    if metric == 'sharpe_ratio':
                        score = result['metrics'].get('sharpe_ratio', -999)
                    elif metric == 'total_return':
                        score = result['total_return']
                    else:
                        score = result['metrics'].get('profit_factor', 0)
                    
                    if score > best_score:
                        best_score = score
                        best_params = params
                        
                except Exception:
                    continue
        
        if best_params:
            return {'params': best_params, 'score': best_score}
        return None
    
    def _test_parameters(self, data: pd.DataFrame, strategy_class, params: Dict) -> Optional[Dict]:
        """Test parameters on out-of-sample data."""
        try:
            result = self.runner.run_backtest(strategy_class, data, params)
            return result
        except Exception as e:
            print(f"❌ Testing error: {e}")
            return None
    
    def _calculate_summary_stats(self, results: Dict) -> Dict[str, Any]:
        """Calculate summary statistics for walk-forward analysis."""
        if not results['test_results']:
            return {}
        
        # Extract returns
        returns = [r['total_return'] for r in results['test_results']]
        cumulative_returns = results['cumulative_returns']
        
        # Calculate statistics
        summary = {
            'total_periods': len(returns),
            'positive_periods': sum(1 for r in returns if r > 0),
            'negative_periods': sum(1 for r in returns if r < 0),
            'win_rate': (sum(1 for r in returns if r > 0) / len(returns)) * 100,
            'average_return': np.mean(returns),
            'median_return': np.median(returns),
            'std_return': np.std(returns),
            'best_period': max(returns),
            'worst_period': min(returns),
            'final_cumulative_return': cumulative_returns[-1] if cumulative_returns else 0,
            'max_drawdown': min(results['drawdowns']) if results['drawdowns'] else 0,
            'sharpe_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        }
        
        return summary
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive walk-forward analysis report."""
        summary = results['summary']
        
        report = f"""
🔄 WALK-FORWARD ANALYSIS REPORT
{'='*50}

📊 OVERVIEW
{'-'*30}
Total Periods: {summary['total_periods']}
Positive Periods: {summary['positive_periods']}
Negative Periods: {summary['negative_periods']}
Win Rate: {summary['win_rate']:.1f}%

📈 PERFORMANCE METRICS
{'-'*30}
Final Cumulative Return: {summary['final_cumulative_return']:.2f}%
Average Period Return: {summary['average_return']:.2f}%
Median Period Return: {summary['median_return']:.2f}%
Return Volatility: {summary['std_return']:.2f}%
Best Period: {summary['best_period']:.2f}%
Worst Period: {summary['worst_period']:.2f}%
Maximum Drawdown: {summary['max_drawdown']:.2f}%
Sharpe Ratio: {summary['sharpe_ratio']:.2f}

🎯 PARAMETER STABILITY
{'-'*30}
"""
        
        # Parameter analysis
        if results['parameters_used']:
            param_names = list(results['parameters_used'][0].keys())
            param_names = [p for p in param_names if p != 'printlog']
            
            for param in param_names:
                values = [p.get(param, 0) for p in results['parameters_used']]
                if values:
                    report += f"{param}: Mean={np.mean(values):.2f}, Std={np.std(values):.2f}\n"
        
        report += f"""

📊 PERIOD-BY-PERIOD RESULTS
{'-'*30}
"""
        
        for i, (period, test_result) in enumerate(zip(results['periods'], results['test_results'])):
            date_info = results['dates'][i]
            report += f"Period {period}: {test_result['total_return']:+.2f}% "
            report += f"({date_info['test_start'].strftime('%Y-%m-%d')} to {date_info['test_end'].strftime('%Y-%m-%d')})\n"
        
        report += f"""

⚠️ DISCLAIMER
{'-'*30}
Walk-forward analysis provides historical validation but does not guarantee future performance.
Past results do not predict future returns. Use for educational purposes only.
"""
        
        return report


def main():
    """Example usage of WalkForwardAnalyzer."""
    from data.client import BinanceDataClient
    
    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=2000)
    
    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 2000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=2000)
    
    print(f"Loaded {len(df)} candles for walk-forward analysis")
    
    # Initialize analyzer
    analyzer = WalkForwardAnalyzer(
        optimization_window=300,
        test_window=50,
        step_size=25
    )
    
    # Define parameter ranges
    param_ranges = {
        'ma_short': [5, 10, 15],
        'ma_long': [20, 30, 50],
        'rsi_period': [10, 14, 20]
    }
    
    # Run analysis
    try:
        results = analyzer.run_walk_forward_analysis(
            df, MARSIStrategy, param_ranges, 'sharpe_ratio'
        )
        
        # Generate report
        report = analyzer.generate_report(results)
        print(report)
        
        # Save results
        filename = f"walk_forward_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")
        
    except Exception as e:
        print(f"❌ Walk-forward analysis failed: {e}")


if __name__ == "__main__":
    main()
