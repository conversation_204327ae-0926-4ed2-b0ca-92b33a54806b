"""
Advanced Logging System with structured logging, performance tracking, and monitoring.
"""

import logging
import logging.handlers
import json
import time
import traceback
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LogEntry:
    timestamp: str
    level: str
    logger_name: str
    message: str
    module: str
    function: str
    line_number: int
    thread_id: int
    process_id: int
    extra_data: Optional[Dict[str, Any]] = None
    exception_info: Optional[str] = None


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        # Create structured log entry
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread_id': record.thread,
            'process_id': record.process
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry['extra'] = extra_fields
        
        return json.dumps(log_entry, default=str)


class PerformanceTracker:
    """Track performance metrics for logging."""
    
    def __init__(self):
        self._start_times: Dict[str, float] = {}
        self._metrics: Dict[str, List[float]] = {}
        self._lock = threading.Lock()
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        with self._lock:
            self._start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> Optional[float]:
        """End timing an operation and return duration."""
        with self._lock:
            if operation in self._start_times:
                duration = time.time() - self._start_times[operation]
                del self._start_times[operation]
                
                if operation not in self._metrics:
                    self._metrics[operation] = []
                self._metrics[operation].append(duration)
                
                return duration
        return None
    
    def get_stats(self, operation: str) -> Dict[str, float]:
        """Get statistics for an operation."""
        with self._lock:
            if operation not in self._metrics or not self._metrics[operation]:
                return {}
            
            durations = self._metrics[operation]
            return {
                'count': len(durations),
                'total': sum(durations),
                'average': sum(durations) / len(durations),
                'min': min(durations),
                'max': max(durations)
            }
    
    def clear_metrics(self):
        """Clear all metrics."""
        with self._lock:
            self._metrics.clear()
            self._start_times.clear()


class AdvancedLogger:
    """
    Advanced logging system with structured logging and performance tracking.
    """
    
    def __init__(self, 
                 name: str = "trading_system",
                 log_dir: str = "logs",
                 level: LogLevel = LogLevel.INFO,
                 enable_console: bool = True,
                 enable_file: bool = True,
                 enable_structured: bool = True,
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5):
        
        self.name = name
        self.log_dir = Path(log_dir)
        self.level = level
        self.enable_structured = enable_structured
        self.performance_tracker = PerformanceTracker()
        
        # Create log directory
        self.log_dir.mkdir(exist_ok=True)
        
        # Setup logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.value))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Setup handlers
        if enable_console:
            self._setup_console_handler()
        
        if enable_file:
            self._setup_file_handlers(max_file_size, backup_count)
        
        # Setup structured logging
        if enable_structured:
            self._setup_structured_handler(max_file_size, backup_count)
    
    def _setup_console_handler(self):
        """Setup console logging handler."""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Colored formatter for console
        console_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        console_formatter = logging.Formatter(console_format)
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(console_handler)
    
    def _setup_file_handlers(self, max_file_size: int, backup_count: int):
        """Setup file logging handlers."""
        # General log file
        general_log = self.log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            general_log,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setLevel(logging.DEBUG)
        
        file_format = '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
        file_formatter = logging.Formatter(file_format)
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
        
        # Error log file
        error_log = self.log_dir / f"{self.name}_errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(error_handler)
    
    def _setup_structured_handler(self, max_file_size: int, backup_count: int):
        """Setup structured JSON logging handler."""
        structured_log = self.log_dir / f"{self.name}_structured.jsonl"
        structured_handler = logging.handlers.RotatingFileHandler(
            structured_log,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        structured_handler.setLevel(logging.DEBUG)
        structured_handler.setFormatter(StructuredFormatter())
        
        self.logger.addHandler(structured_handler)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log error message."""
        if exception:
            self.logger.error(message, exc_info=exception, extra=kwargs)
        else:
            self.logger.error(message, extra=kwargs)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log critical message."""
        if exception:
            self.logger.critical(message, exc_info=exception, extra=kwargs)
        else:
            self.logger.critical(message, extra=kwargs)
    
    def log_trade(self, symbol: str, side: str, quantity: float, price: float, **kwargs):
        """Log trading activity."""
        self.info(
            f"Trade executed: {side} {quantity} {symbol} @ {price}",
            trade_symbol=symbol,
            trade_side=side,
            trade_quantity=quantity,
            trade_price=price,
            **kwargs
        )
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log performance metrics."""
        self.info(
            f"Performance: {operation} completed in {duration:.4f}s",
            performance_operation=operation,
            performance_duration=duration,
            **kwargs
        )
    
    def log_risk_event(self, event_type: str, severity: str, details: Dict[str, Any]):
        """Log risk management events."""
        message = f"Risk event: {event_type} - {severity}"
        
        if severity.lower() in ['high', 'critical']:
            self.error(message, risk_event_type=event_type, risk_severity=severity, **details)
        else:
            self.warning(message, risk_event_type=event_type, risk_severity=severity, **details)
    
    def log_system_event(self, event: str, status: str, **kwargs):
        """Log system events."""
        self.info(
            f"System event: {event} - {status}",
            system_event=event,
            system_status=status,
            **kwargs
        )
    
    def log_api_call(self, endpoint: str, method: str, status_code: int, duration: float, **kwargs):
        """Log API calls."""
        self.info(
            f"API call: {method} {endpoint} - {status_code} ({duration:.3f}s)",
            api_endpoint=endpoint,
            api_method=method,
            api_status_code=status_code,
            api_duration=duration,
            **kwargs
        )
    
    def time_operation(self, operation: str):
        """Context manager for timing operations."""
        return TimedOperation(self, operation)
    
    def get_performance_stats(self) -> Dict[str, Dict[str, float]]:
        """Get all performance statistics."""
        return {op: self.performance_tracker.get_stats(op) 
                for op in self.performance_tracker._metrics.keys()}
    
    def clear_performance_stats(self):
        """Clear performance statistics."""
        self.performance_tracker.clear_metrics()
    
    def get_log_files(self) -> List[Path]:
        """Get list of log files."""
        return list(self.log_dir.glob(f"{self.name}*.log*"))
    
    def get_log_summary(self) -> Dict[str, Any]:
        """Get logging summary."""
        log_files = self.get_log_files()
        total_size = sum(f.stat().st_size for f in log_files if f.exists())
        
        return {
            'logger_name': self.name,
            'log_level': self.level.value,
            'log_directory': str(self.log_dir),
            'log_files_count': len(log_files),
            'total_log_size_mb': total_size / (1024 * 1024),
            'structured_logging': self.enable_structured,
            'handlers_count': len(self.logger.handlers)
        }


class TimedOperation:
    """Context manager for timing operations."""
    
    def __init__(self, logger: AdvancedLogger, operation: str):
        self.logger = logger
        self.operation = operation
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.performance_tracker.start_timer(self.operation)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = self.logger.performance_tracker.end_timer(self.operation)
        if duration:
            self.logger.log_performance(self.operation, duration)
        
        if exc_type:
            self.logger.error(
                f"Operation {self.operation} failed",
                exception=exc_val,
                operation=self.operation
            )


# Global logger instance
trading_logger = AdvancedLogger()


def get_logger(name: str = None) -> AdvancedLogger:
    """Get logger instance."""
    if name:
        return AdvancedLogger(name)
    return trading_logger


def main():
    """Example usage of AdvancedLogger."""
    # Initialize logger
    logger = AdvancedLogger("example_system")
    
    # Basic logging
    logger.info("System starting up")
    logger.debug("Debug information", user_id=123, session_id="abc123")
    
    # Trade logging
    logger.log_trade("BTCUSDT", "BUY", 0.1, 45000, strategy="MA_RSI")
    
    # Performance logging with context manager
    with logger.time_operation("data_processing"):
        time.sleep(0.1)  # Simulate work
    
    # Risk event logging
    logger.log_risk_event(
        "position_size_exceeded",
        "high",
        {"symbol": "BTCUSDT", "current_size": 0.15, "max_size": 0.10}
    )
    
    # API call logging
    logger.log_api_call("/api/v1/trades", "POST", 201, 0.045)
    
    # Get performance stats
    stats = logger.get_performance_stats()
    print(f"Performance stats: {stats}")
    
    # Get log summary
    summary = logger.get_log_summary()
    print(f"Log summary: {summary}")
    
    # Error logging
    try:
        raise ValueError("Example error")
    except Exception as e:
        logger.error("An error occurred", exception=e, context="example")


if __name__ == "__main__":
    main()
