import requests
import pandas as pd
import matplotlib.pyplot as plt
import os
import json
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, f1_score, classification_report
import joblib
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset



WEBHOOK_URL = os.getenv('DISCORD_WEBHOOK_URL', 'https://discord.com/api/webhooks/1377074935926755430/GIs97sttzctWjvQkfzb8IIVY4nGJSnysqAW4KRS5EYRssz8NuRKn64BKZke_cLt6rAOG')


class SimpleNN(nn.Module):
    def __init__(self, input_size):
        super(SimpleNN, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(input_size, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 2)  # 2 classes: alta ou baixa
        )

    def forward(self, x):
        return self.network(x)


def fetch_alpha_vantage_klines(symbol='BTC', market='USD', interval='60min', api_key='4TA6M4ZM8ONGDO97'):
    url = (
        f'https://www.alphavantage.co/query?function=CRYPTO_INTRADAY'
        f'&symbol={symbol}&market={market}&interval={interval}&apikey={api_key}'
    )

    response = requests.get(url)
    data = response.json()

    # O Alpha Vantage retorna os dados em uma nested dict chamada "Time Series Crypto (60min)"
    time_series_key = f'Time Series Crypto ({interval})'
    if time_series_key not in data:
        raise ValueError(f"Erro na resposta da API Alpha Vantage: {data}")

    df = pd.DataFrame.from_dict(data[time_series_key], orient='index')
    df.index = pd.to_datetime(df.index)
    df = df.sort_index()

    # Renomeia e converte colunas
    df = df.rename(columns={
        '1. open': 'open',
        '2. high': 'high',
        '3. low': 'low',
        '4. close': 'close',
        '5. volume': 'volume'
    })
    for col in df.columns:
        df[col] = df[col].astype(float)

    return df

def train_nn_model(df, epochs=50, batch_size=32, learning_rate=0.001):
    df = create_labels(df)
    features = get_features(df)
    df = df.loc[features.index]

    X = torch.tensor(features.values, dtype=torch.float32)
    y = torch.tensor(df['target'].values, dtype=torch.long)  # long para CrossEntropyLoss

    dataset = TensorDataset(X, y)
    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size

    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)

    model = SimpleNN(input_size=X.shape[1])
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    model.train()
    for epoch in range(epochs):
        total_loss = 0
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()

        avg_loss = total_loss / len(train_loader)
        print(f"Epoch {epoch+1}/{epochs} - Loss: {avg_loss:.4f}")

    # Avaliação simples no test set
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            outputs = model(batch_X)
            _, predicted = torch.max(outputs, 1)
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()

    accuracy = correct / total
    print(f"Test Accuracy: {accuracy:.4f}")

    return model

def predict_next_movement_nn(model, df):
    model.eval()
    features = get_features(df)
    last_features = torch.tensor(features.iloc[-1].values, dtype=torch.float32).unsqueeze(0)  # batch 1

    with torch.no_grad():
        outputs = model(last_features)
        _, predicted = torch.max(outputs, 1)

    return predicted.item() 

def prepare_data_lstm(df, feature_cols, target_col='target', window_size=20):
    # normaliza features
    scaler = StandardScaler()
    features = scaler.fit_transform(df[feature_cols].dropna())
    
    X, y = [], []
    for i in range(len(features) - window_size):
        X.append(features[i:i+window_size])
        y.append(df[target_col].iloc[i + window_size])
    return np.array(X), np.array(y), scaler

def train_lstm(df):
    feature_cols = ['MA_Short', 'MA_Long', 'RSI', 'ATR', 'OBV', 'Upper_BB', 'Lower_BB']
    df = create_labels(df)
    df = df.dropna(subset=feature_cols + ['target'])
    X, y, scaler = prepare_data_lstm(df, feature_cols)

    model = Sequential()
    model.add(LSTM(50, input_shape=(X.shape[1], X.shape[2])))
    model.add(Dense(1, activation='sigmoid'))

    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    model.fit(X, y, epochs=20, batch_size=32, validation_split=0.2)

    return model, scaler

def send_alert_to_discord(message):
    payload = {"content": message}
    
    response = requests.post(WEBHOOK_URL, json=payload)

    if response.status_code == 204:
        print("✅ Alerta enviado para Discord com sucesso.")
    else:
        print(f"❌ Falha no envio do alerta: {response.status_code} {response.text}")

def predict_lstm(model, scaler, df, window_size=20):
    feature_cols = ['MA_Short', 'MA_Long', 'RSI', 'ATR', 'OBV', 'Upper_BB', 'Lower_BB']
    df = df.dropna(subset=feature_cols)
    features = scaler.transform(df[feature_cols].tail(window_size))
    X = features.reshape(1, window_size, len(feature_cols))
    prediction = model.predict(X)[0][0]
    return 1 if prediction > 0.5 else 0

def train_classifier(df, save_path='btc_rf_model.joblib'):
    df = create_labels(df)
    features = get_features(df)
    df = df.loc[features.index]

    X = features.values
    y = df['target'].values

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)

    y_pred = model.predict(X_test)

    print("Classification Metrics:")
    print("Accuracy:", accuracy_score(y_test, y_pred))
    print("F1 Score:", f1_score(y_test, y_pred))
    print(classification_report(y_test, y_pred))

    # Salvar o modelo
    if save_path:
      joblib.dump(model, save_path)
      print(f"Modelo salvo em {save_path}")

    # joblib.dump(model, save_path)
    # print(f"✅ Modelo salvo em {save_path}")

    return model

def load_model(path='btc_rf_model.joblib'):
    model = joblib.load(path)
    print(f"✅ Modelo carregado de {path}")
    return model


def predict_next_movement(model, df):
    features = get_features(df)
    last_features = features.iloc[-1].values.reshape(1, -1)
    prediction = model.predict(last_features)[0]
    return prediction  # 1 = alta, 0 = baixa/estável

def predict_next_movement(model, df):
    features = get_features(df)
    last_features = features.iloc[-1].values.reshape(1, -1)
    prediction = model.predict(last_features)[0]
    return prediction  # 1 = alta, 0 = baixa/estável

def train_classifier(df):
    df = create_labels(df)
    features = get_features(df)
    
    # Alinhar com target
    df = df.loc[features.index]
    
    X = features.values
    y = df['target'].values

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)

    print("Accuracy:", accuracy_score(y_test, y_pred))
    print("F1 Score:", f1_score(y_test, y_pred))
    print(classification_report(y_test, y_pred))

    return model

def fetch_binance_klines(symbol='BTCUSDT', interval='1h', limit=200):
    url = f'https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}'
    response = requests.get(url)
    response.raise_for_status()
    data = response.json()
    df = pd.DataFrame(data, columns=[
        "open_time", "open", "high", "low", "close", "volume", 
        "close_time", "quote_asset_volume", "number_of_trades", 
        "taker_buy_base", "taker_buy_quote", "ignore"
    ])
    df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
    df.set_index('open_time', inplace=True)
    df['close'] = df['close'].astype(float)
    return df

def compute_signals(df):
    df['MA_Short'] = df['close'].rolling(window=10).mean()
    df['MA_Long'] = df['close'].rolling(window=50).mean()
    df['Signal'] = 0
    df.loc[df.index[10:], 'Signal'] = (df['MA_Short'][10:] > df['MA_Long'][10:]).astype(int)
    df['Position'] = df['Signal'].diff()
    return df

def compute_rsi(df, window=14):
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window).mean()
    avg_loss = loss.rolling(window).mean()
    rs = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + rs))
    return df

def add_bollinger_bands(df, window=20):
    df['MA20'] = df['close'].rolling(window=window).mean()
    df['STD20'] = df['close'].rolling(window=window).std()
    df['Upper_BB'] = df['MA20'] + (2 * df['STD20'])
    df['Lower_BB'] = df['MA20'] - (2 * df['STD20'])
    return df

def predict_future_prices(df, periods=5):
    # Usando regressão linear simples para prever os próximos preços (períodos horas)
    df_clean = df.dropna(subset=['close']).copy()
    df_clean['timestamp'] = df_clean.index.astype(np.int64) // 10**9  # converter para segundos
    X = np.array(df_clean['timestamp']).reshape(-1,1)
    y = df_clean['close'].values

    model = LinearRegression()
    model.fit(X, y)

    last_timestamp = df_clean['timestamp'].iloc[-1]
    future_timestamps = np.array([last_timestamp + 3600 * i for i in range(1, periods+1)]).reshape(-1,1)

    preds = model.predict(future_timestamps)

    future_dates = pd.to_datetime(future_timestamps.flatten(), unit='s')
    prediction_df = pd.DataFrame({'predicted_close': preds}, index=future_dates)
    return prediction_df

def compute_atr(df, window=14):
    high_low = df['high'].astype(float) - df['low'].astype(float)
    high_close_prev = (df['high'].astype(float) - df['close'].shift(1).astype(float)).abs()
    low_close_prev = (df['low'].astype(float) - df['close'].shift(1).astype(float)).abs()
    
    tr = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
    df['ATR'] = tr.rolling(window=window).mean()
    return df


def compute_obv(df):
    df['close'] = df['close'].astype(float)
    df['volume'] = df['volume'].astype(float)
    obv = [0]
    
    for i in range(1, len(df)):
        if df['close'].iloc[i] > df['close'].iloc[i-1]:
            obv.append(obv[-1] + df['volume'].iloc[i])
        elif df['close'].iloc[i] < df['close'].iloc[i-1]:
            obv.append(obv[-1] - df['volume'].iloc[i])
        else:
            obv.append(obv[-1])
            
    df['OBV'] = obv
    return df

def create_labels(df):
    df = df.copy()
    df['close_next'] = df['close'].shift(-1)
    df['target'] = (df['close_next'] > df['close']).astype(int)
    df.dropna(inplace=True)
    return df

def get_features(df):
    features = df[['MA_Short', 'MA_Long', 'RSI', 'ATR', 'OBV', 'Upper_BB', 'Lower_BB']].copy()
    features = features.dropna()
    return features

def check_alerts(df, model, prediction_df=None):
    alerts = []
    latest = df.dropna().iloc[-1]
    timestamp = latest.name.strftime('%Y-%m-%d %H:%M:%S')

    # RSI
    if latest['RSI'] > 70:
        alerts.append("⚠️ **RSI está sobrecomprado (>70)**")
    elif latest['RSI'] < 30:
        alerts.append("⚠️ **RSI está sobrevendido (<30)**")
    else:
        alerts.append("ℹ️ **RSI está neutro**")

    # Sinal de cruzamento das médias móveis
    if latest['Position'] == 1:
        alerts.append("🔔 **Sinal de Compra** - cruzamento das médias móveis")
    elif latest['Position'] == -1:
        alerts.append("🔔 **Sinal de Venda** - cruzamento das médias móveis")
    else:
        alerts.append("ℹ️ **Sem sinal claro de cruzamento**")

    # Previsão do modelo Random Forest
    next_move = predict_next_movement(model, df)
    next_move_str = '**Alta 📈**' if next_move == 1 else '**Baixa/Estável 📉**'

    alerts.append(f"🔮 Modelo prevê: {next_move_str} no próximo candle")

    # Previsão de preço futuro e cálculo de ganho/perda hipotético
    if prediction_df is not None:
        future_price = prediction_df['predicted_close'].iloc[0]
        current_price = latest['close']
        price_change_pct = (future_price - current_price) / current_price * 100
        investment = 10.0
        expected_gain = investment * price_change_pct / 100

        alerts.append(f"📈 Previsão para próxima hora: **${future_price:.2f}** ({price_change_pct:.2f}%)")
        alerts.append(f"💰 Ganho/perda estimado para $10 investidos: **${expected_gain:.2f}**")

        alert_message = (
            f"⏰ **Alerta BTC - {timestamp} (UTC)**\n\n" +
            "\n\n".join(alerts) + "\n"
         )
    return alert_message


def plot_and_save(df, prediction_df=None, filename='btc_binance_signals.png'):
    import matplotlib.pyplot as plt

    fig, axes = plt.subplots(6, 1, figsize=(16, 22), sharex=True)

    # 1) Preço, MAs e Bollinger Bands + pontos compra/venda
    ax = axes[0]
    ax.plot(df['close'], label='BTC Close Price', alpha=0.5)
    ax.plot(df['MA_Short'], label='MA Short (10)', alpha=0.8)
    ax.plot(df['MA_Long'], label='MA Long (50)', alpha=0.8)
    ax.plot(df['Upper_BB'], label='Upper BB', linestyle='--', color='gray')
    ax.plot(df['Lower_BB'], label='Lower BB', linestyle='--', color='gray')
    
    buys = df[df['Position'] == 1]
    sells = df[df['Position'] == -1]
    ax.scatter(buys.index, buys['close'], marker='^', color='green', label='Buy', s=120, edgecolors='black', linewidth=1.5)
    ax.scatter(sells.index, sells['close'], marker='v', color='red', label='Sell', s=120, edgecolors='black', linewidth=1.5)

    # Plot previsão futura se disponível
    if prediction_df is not None:
        ax.plot(prediction_df.index, prediction_df['predicted_close'], label='Previsão Linear', linestyle=':', color='purple')

    ax.set_title('Preço BTC, Médias Móveis e Bollinger Bands')
    ax.legend()
    ax.grid(True)

    # 2) RSI
    ax = axes[1]
    ax.plot(df['RSI'], color='orange', label='RSI')
    ax.axhline(70, color='red', linestyle='--', alpha=0.4)
    ax.axhline(30, color='green', linestyle='--', alpha=0.4)
    ax.set_title('RSI')
    ax.legend()
    ax.grid(True)

    # 3) Volume
    ax = axes[2]
    ax.plot(df['volume'].astype(float), label='Volume', color='blue')
    ax.set_title('Volume')
    ax.legend()
    ax.grid(True)

    # 4) MACD
    ax = axes[3]
    ax.plot(df['MACD'], label='MACD', color='blue')
    ax.plot(df['MACD_signal'], label='Signal Line', color='orange')
    ax.bar(df.index, df['MACD_hist'], label='Histogram', color='gray', alpha=0.5)
    ax.set_title('MACD')
    ax.legend()
    ax.grid(True)

    # 5) ATR
    ax = axes[4]
    ax.plot(df['ATR'], label='ATR', color='magenta')
    ax.set_title('ATR - Average True Range')
    ax.legend()
    ax.grid(True)

    # 6) OBV
    ax = axes[5]
    ax.plot(df['OBV'], label='OBV', color='brown')
    ax.set_title('OBV - On-Balance Volume')
    ax.legend()
    ax.grid(True)

    plt.tight_layout()
    plt.savefig(filename)
    print(f"✅ Gráfico salvo em {filename}")
    plt.close(fig)  # fecha a figura para liberar memória

def send_alert_to_discord(message):
    payload = {
        "content": message
    }
    response = requests.post(WEBHOOK_URL, data={"payload_json": json.dumps(payload)})

    if response.status_code == 204:
        print("✅ Alerta enviado para Discord com sucesso.")
    else:
        print(f"❌ Falha no envio do alerta: {response.status_code} {response.text}")


def send_to_discord(image_path, message="📈 Análise BTC 1h"):
    with open(image_path, 'rb') as f:
        files = {
            'file': (image_path, f, 'image/png')
        }
        payload = {
            "content": message
        }
        response = requests.post(WEBHOOK_URL, data={"payload_json": json.dumps(payload)}, files=files)

    if response.status_code == 204:
        print("✅ Enviado para Discord com sucesso.")
    else:
        print(f"❌ Falha no envio: {response.status_code} {response.text}")

def compute_macd(df, short=12, long=26, signal=9):
    df['EMA_short'] = df['close'].ewm(span=short, adjust=False).mean()
    df['EMA_long'] = df['close'].ewm(span=long, adjust=False).mean()
    df['MACD'] = df['EMA_short'] - df['EMA_long']
    df['MACD_signal'] = df['MACD'].ewm(span=signal, adjust=False).mean()
    df['MACD_hist'] = df['MACD'] - df['MACD_signal']
    return df

def save_nn_model(model, path='nn_model.pth'):
    torch.save(model.state_dict(), path)

def load_nn_model(input_size, path='nn_model.pth'):
    model = SimpleNN(input_size)
    model.load_state_dict(torch.load(path))
    model.eval()
    return model

def main():
    df = fetch_binance_klines()
    df = compute_macd(df)
    df = compute_atr(df)
    df = compute_obv(df)
    df = compute_signals(df)
    df = compute_rsi(df)
    df = add_bollinger_bands(df)

    prediction_df = predict_future_prices(df, periods=5)

    plot_and_save(df, prediction_df)
    model_path = 'btc_rf_model.joblib'
    if not os.path.exists(model_path):
        model = train_classifier(df)
    else:
        model = load_model(model_path)

    latest = df.dropna().iloc[-1]
    # model = load_model()
    next_move = predict_next_movement(model, df)
    next_movement = predict_next_movement(model, df)


    nn_model = train_nn_model(df)

    next_move_nn = predict_next_movement_nn(nn_model, df) 
    print(f"Próximo movimento previsto pela rede neural: {'Alta' if next_move_nn == 1 else 'Baixa/Estável'}")
    print(f"Próximo movimento previsto: {'Alta' if next_movement == 1 else 'Baixa/Estável'}")

    from datetime import datetime
    now_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    status = (
        f"⏰ {now_str}\n"
       f"📆 Candle: {latest.name.strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"📉 Último preço: ${latest['close']:.2f}\n"
        f"🔍 RSI: {latest['RSI']:.2f} ({'Sobrevendido' if latest['RSI'] < 30 else 'Sobrecomprado' if latest['RSI'] > 70 else 'Neutro'})\n"
        f"📌 Sinal atual: {'Compra' if latest['Position'] == 1 else 'Venda' if latest['Position'] == -1 else 'Aguardando'}\n"
        f"🔮 Previsão próximo candle: {'Alta 📈' if next_move == 1 else 'Baixa/Estável 📉'}\n"
        f"💵 Se investir $10, possível ganho: ${10 * (prediction_df['predicted_close'].iloc[0] / latest['close'] - 1):.2f}"
        f"🔮 Previsão próxima hora: ${prediction_df['predicted_close'].iloc[0]:.2f}"
    )

    alert_message = check_alerts(df, model, prediction_df)

    if alert_message:
        send_alert_to_discord(alert_message)
    else:
        print("Nenhum alerta gerado.")

    send_to_discord('btc_binance_signals.png', status)

if __name__ == "__main__":
    main()
