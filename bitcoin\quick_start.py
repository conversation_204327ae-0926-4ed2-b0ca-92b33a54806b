#!/usr/bin/env python3
"""
Quick start example for Bitcoin Trading Signals system.
Demonstrates basic usage of all major components.
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.client import BinanceDataClient
from indicators.ta_wrappers import add_all_indicators, get_trading_signals
from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy
from execution.paper_trader import PaperTrader, OrderSide, OrderType
from risk.position_sizing import RiskManager


def main():
    """Quick start demonstration."""
    print("🚀 Bitcoin Trading Signals - Quick Start")
    print("=" * 50)

    # 1. Data Collection
    print("\n📊 Step 1: Data Collection")
    print("-" * 30)

    client = BinanceDataClient()

    # Fetch historical data
    print("Fetching Bitcoin data from Binance...")
    df = client.fetch_historical_klines("BTCUSDT", "1h", 500)

    if df.empty:
        print("❌ Failed to fetch data. Check your internet connection.")
        return

    print(f"✅ Fetched {len(df)} candles")
    print(f"📅 Period: {df.index[0]} to {df.index[-1]}")
    print(f"💰 Latest price: ${df['close'].iloc[-1]:,.2f}")

    # Store in database
    client.store_candles(df, "BTCUSDT")
    print("✅ Data stored in SQLite database")

    # 2. Technical Analysis
    print("\n📈 Step 2: Technical Analysis")
    print("-" * 30)

    # Add all indicators
    print("Calculating technical indicators...")
    df_with_indicators = add_all_indicators(df)

    # Generate trading signals
    signals = get_trading_signals(df_with_indicators)

    # Display latest analysis
    latest = df_with_indicators.iloc[-1]
    latest_signals = signals.iloc[-1]

    print(f"📊 RSI: {latest['RSI']:.1f}")
    rsi_status = 'Overbought 🔴' if latest['RSI'] > 70 else 'Oversold 🟢' if latest['RSI'] < 30 else 'Neutral 🟡'
    print(f"   Status: {rsi_status}")

    print(f"📈 Moving Averages:")
    print(f"   Short (10): ${latest['MA_Short']:,.2f}")
    print(f"   Long (50): ${latest['MA_Long']:,.2f}")

    ma_signal = 'Buy 🟢' if latest_signals['MA_Signal'] == 1 else 'Sell 🔴' if latest_signals['MA_Signal'] == -1 else 'Hold 🟡'
    print(f"   Signal: {ma_signal}")

    print(f"🎯 SuperTrend: ${latest['SuperTrend']:,.2f}")
    print(f"📊 VWAP: ${latest['VWAP']:,.2f}")
    print(f"📉 ATR: ${latest['ATR']:.2f}")

    # 3. Risk Management
    print("\n⚖️ Step 3: Risk Management")
    print("-" * 30)

    risk_mgr = RiskManager(max_risk_per_trade=0.02)  # 2% risk per trade

    # Calculate position size
    capital = 10000
    entry_price = latest['close']
    stop_loss = entry_price - (1.5 * latest['ATR'])  # ATR-based stop

    position_size = risk_mgr.calculate_position_size_fixed_risk(
        capital, entry_price, stop_loss
    )

    print(f"💰 Capital: ${capital:,.2f}")
    print(f"🎯 Entry Price: ${entry_price:,.2f}")
    print(f"🛑 Stop Loss: ${stop_loss:,.2f}")
    print(f"📏 Position Size: {position_size:.4f} BTC")
    print(f"💵 Position Value: ${position_size * entry_price:,.2f}")
    print(f"⚠️ Risk Amount: ${position_size * (entry_price - stop_loss):,.2f}")

    # 4. Backtesting
    print("\n🧪 Step 4: Strategy Backtesting")
    print("-" * 30)

    print("Running MA+RSI strategy backtest...")
    runner = BacktestRunner(initial_cash=10000, commission=0.001)

    result = runner.run_backtest(
        MARSIStrategy,
        df_with_indicators,
        strategy_params={
            'ma_short': 10,
            'ma_long': 50,
            'rsi_period': 14,
            'printlog': False
        }
    )

    print(f"📊 Backtest Results:")
    print(f"   Total Return: {result['total_return']:.2f}%")

    # Check if metrics exist before accessing them
    metrics = result.get('metrics', {})
    sharpe = metrics.get('sharpe_ratio', 0)
    max_dd = metrics.get('max_drawdown', 0)
    win_rate = metrics.get('win_rate', 0)
    total_trades = metrics.get('total_trades', 0)
    profit_factor = metrics.get('profit_factor', 0)

    print(f"   Sharpe Ratio: {sharpe:.2f}")
    print(f"   Max Drawdown: {max_dd:.2f}%")
    print(f"   Win Rate: {win_rate:.1f}%")
    print(f"   Total Trades: {total_trades}")
    print(f"   Profit Factor: {profit_factor:.2f}")

    # 5. Paper Trading
    print("\n📝 Step 5: Paper Trading Demo")
    print("-" * 30)

    trader = PaperTrader(initial_balance=10000)

    # Simulate a buy order
    print("Placing paper buy order...")
    order_id = trader.create_order(
        "BTCUSDT",
        OrderSide.BUY,
        OrderType.MARKET,
        0.1  # 0.1 BTC
    )

    # Process market data to execute the order
    trader.process_market_data("BTCUSDT", entry_price)

    # Show portfolio
    summary = trader.get_portfolio_summary()
    print(f"📊 Paper Portfolio:")
    print(f"   Balance: ${summary['balance']:,.2f}")
    print(f"   Total Equity: ${summary['total_equity']:,.2f}")
    print(f"   Positions: {summary['positions_count']}")

    positions = trader.get_positions()
    if positions:
        pos = positions[0]
        print(f"   BTC Position: {pos['quantity']:.4f} @ ${pos['entry_price']:,.2f}")

    # 6. Performance Summary
    print("\n📈 Step 6: Performance Summary")
    print("-" * 30)

    # Calculate some basic metrics from the data
    returns = df['close'].pct_change().dropna()

    print(f"📊 Market Statistics (last {len(df)} hours):")
    print(f"   Price Change: {((df['close'].iloc[-1] / df['close'].iloc[0]) - 1) * 100:.2f}%")
    print(f"   Volatility: {returns.std() * 100:.2f}% per hour")
    print(f"   Max Price: ${df['high'].max():,.2f}")
    print(f"   Min Price: ${df['low'].min():,.2f}")

    # 7. Next Steps
    print("\n🎯 Next Steps")
    print("-" * 30)
    print("1. 📊 Run dashboard: python main.py dashboard")
    print("2. 🔄 Start live data: python main.py setup --websocket")
    print("3. 📈 Analyze market: python main.py analyze --discord")
    print("4. 🧪 Run backtest: python main.py backtest --strategy ma_rsi --report")
    print("5. 📝 Paper trade: python main.py paper buy --quantity 0.01")

    print("\n✅ Quick start completed successfully!")
    print("🚀 Your Bitcoin trading system is ready!")

    # Health check
    health = client.health_check()
    print(f"\n🏥 System Health:")
    print(f"   Database: {health['total_candles']} candles stored")
    print(f"   WebSocket: {'Running' if health['websocket_running'] else 'Stopped'}")
    print(f"   Data Path: {health['database_path']}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Quick start interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during quick start: {e}")
        print("💡 Try running individual components to debug the issue")
