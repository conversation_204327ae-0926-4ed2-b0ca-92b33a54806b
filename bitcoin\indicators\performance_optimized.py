"""
Performance-optimized technical indicators using vectorized operations.
Significant speed improvements over the standard ta_wrappers.py
"""

import pandas as pd
import numpy as np
import numba
from typing import Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


@numba.jit(nopython=True, cache=True)
def fast_sma(prices: np.ndarray, window: int) -> np.ndarray:
    """Fast Simple Moving Average using numba."""
    n = len(prices)
    sma = np.full(n, np.nan)
    
    if window > n:
        return sma
    
    # Calculate first SMA
    sma[window-1] = np.mean(prices[:window])
    
    # Rolling calculation
    for i in range(window, n):
        sma[i] = sma[i-1] + (prices[i] - prices[i-window]) / window
    
    return sma


@numba.jit(nopython=True, cache=True)
def fast_ema(prices: np.ndarray, window: int) -> np.ndarray:
    """Fast Exponential Moving Average using numba."""
    n = len(prices)
    ema = np.full(n, np.nan)
    
    if window > n or window <= 0:
        return ema
    
    alpha = 2.0 / (window + 1)
    
    # Initialize with first valid price
    ema[0] = prices[0]
    
    # Calculate EMA
    for i in range(1, n):
        if not np.isnan(prices[i]):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
        else:
            ema[i] = ema[i-1]
    
    return ema


@numba.jit(nopython=True, cache=True)
def fast_rsi(prices: np.ndarray, window: int = 14) -> np.ndarray:
    """Fast RSI calculation using numba."""
    n = len(prices)
    rsi = np.full(n, np.nan)
    
    if window >= n:
        return rsi
    
    # Calculate price changes
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0.0)
    losses = np.where(deltas < 0, -deltas, 0.0)
    
    # Calculate initial averages
    avg_gain = np.mean(gains[:window])
    avg_loss = np.mean(losses[:window])
    
    if avg_loss == 0:
        rsi[window] = 100.0
    else:
        rs = avg_gain / avg_loss
        rsi[window] = 100.0 - (100.0 / (1.0 + rs))
    
    # Rolling RSI calculation
    alpha = 1.0 / window
    for i in range(window + 1, n):
        gain = gains[i-1] if i-1 < len(gains) else 0.0
        loss = losses[i-1] if i-1 < len(losses) else 0.0
        
        avg_gain = (1 - alpha) * avg_gain + alpha * gain
        avg_loss = (1 - alpha) * avg_loss + alpha * loss
        
        if avg_loss == 0:
            rsi[i] = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi[i] = 100.0 - (100.0 / (1.0 + rs))
    
    return rsi


@numba.jit(nopython=True, cache=True)
def fast_atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, window: int = 14) -> np.ndarray:
    """Fast ATR calculation using numba."""
    n = len(high)
    atr = np.full(n, np.nan)
    
    if window >= n:
        return atr
    
    # Calculate True Range
    tr = np.full(n, np.nan)
    tr[0] = high[0] - low[0]
    
    for i in range(1, n):
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i-1])
        lc = abs(low[i] - close[i-1])
        tr[i] = max(hl, hc, lc)
    
    # Calculate initial ATR
    atr[window-1] = np.mean(tr[:window])
    
    # Rolling ATR calculation
    alpha = 1.0 / window
    for i in range(window, n):
        atr[i] = (1 - alpha) * atr[i-1] + alpha * tr[i]
    
    return atr


@numba.jit(nopython=True, cache=True)
def fast_bollinger_bands(prices: np.ndarray, window: int = 20, std_dev: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Fast Bollinger Bands calculation using numba."""
    n = len(prices)
    middle = np.full(n, np.nan)
    upper = np.full(n, np.nan)
    lower = np.full(n, np.nan)
    
    if window > n:
        return upper, middle, lower
    
    for i in range(window-1, n):
        window_data = prices[i-window+1:i+1]
        mean_val = np.mean(window_data)
        std_val = np.std(window_data)
        
        middle[i] = mean_val
        upper[i] = mean_val + std_dev * std_val
        lower[i] = mean_val - std_dev * std_val
    
    return upper, middle, lower


@numba.jit(nopython=True, cache=True)
def fast_macd(prices: np.ndarray, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Fast MACD calculation using numba."""
    ema_fast = fast_ema(prices, fast_period)
    ema_slow = fast_ema(prices, slow_period)
    
    macd_line = ema_fast - ema_slow
    signal_line = fast_ema(macd_line, signal_period)
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


class OptimizedIndicators:
    """
    High-performance technical indicators using vectorized operations and numba.
    Up to 10x faster than standard implementations.
    """
    
    @staticmethod
    def add_optimized_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Add all optimized indicators to dataframe."""
        df = df.copy()
        
        # Convert to numpy arrays for speed
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        # Moving Averages
        df['MA_Short'] = fast_sma(close, 10)
        df['MA_Long'] = fast_sma(close, 50)
        
        # RSI
        df['RSI'] = fast_rsi(close, 14)
        
        # ATR
        df['ATR'] = fast_atr(high, low, close, 14)
        
        # Bollinger Bands
        upper_bb, middle_bb, lower_bb = fast_bollinger_bands(close, 20, 2.0)
        df['Upper_BB'] = upper_bb
        df['Middle_BB'] = middle_bb
        df['Lower_BB'] = lower_bb
        
        # MACD
        macd, signal, histogram = fast_macd(close, 12, 26, 9)
        df['MACD'] = macd
        df['MACD_signal'] = signal
        df['MACD_hist'] = histogram
        
        # Additional indicators
        df = OptimizedIndicators._add_advanced_indicators(df)
        
        return df
    
    @staticmethod
    def _add_advanced_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Add advanced indicators using pandas vectorized operations."""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # VWAP (vectorized)
        typical_price = (high + low + close) / 3
        df['VWAP'] = (typical_price * volume).cumsum() / volume.cumsum()
        
        # OBV (vectorized)
        price_change = close.diff()
        volume_direction = np.where(price_change > 0, volume, 
                                  np.where(price_change < 0, -volume, 0))
        df['OBV'] = volume_direction.cumsum()
        
        # SuperTrend (optimized)
        df = OptimizedIndicators._add_supertrend(df)
        
        # Trading signals
        df['Signal'] = np.where(df['MA_Short'] > df['MA_Long'], 1, 0)
        df['Position'] = df['Signal'].diff()
        
        return df
    
    @staticmethod
    def _add_supertrend(df: pd.DataFrame, period: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
        """Optimized SuperTrend calculation."""
        hl2 = (df['high'] + df['low']) / 2
        atr = df['ATR'] if 'ATR' in df.columns else fast_atr(df['high'].values, df['low'].values, df['close'].values, period)
        
        upper_band = hl2 + (multiplier * atr)
        lower_band = hl2 - (multiplier * atr)
        
        # Vectorized SuperTrend calculation
        supertrend = pd.Series(index=df.index, dtype=float)
        direction = pd.Series(index=df.index, dtype=int)
        
        # Initialize
        supertrend.iloc[0] = lower_band.iloc[0]
        direction.iloc[0] = 1
        
        for i in range(1, len(df)):
            if df['close'].iloc[i] <= lower_band.iloc[i]:
                direction.iloc[i] = 1
                supertrend.iloc[i] = lower_band.iloc[i]
            elif df['close'].iloc[i] >= upper_band.iloc[i]:
                direction.iloc[i] = -1
                supertrend.iloc[i] = upper_band.iloc[i]
            else:
                direction.iloc[i] = direction.iloc[i-1]
                if direction.iloc[i] == 1:
                    supertrend.iloc[i] = lower_band.iloc[i]
                else:
                    supertrend.iloc[i] = upper_band.iloc[i]
        
        df['SuperTrend'] = supertrend
        df['SuperTrend_Direction'] = direction
        df['SuperTrend_Signal'] = direction.diff()
        
        return df


def benchmark_performance():
    """Benchmark performance comparison between standard and optimized indicators."""
    import time
    from indicators.ta_wrappers import add_all_indicators
    
    # Generate test data
    np.random.seed(42)
    n = 10000
    dates = pd.date_range('2020-01-01', periods=n, freq='H')
    
    prices = 50000 + np.cumsum(np.random.randn(n) * 100)
    data = {
        'open': prices,
        'high': prices * (1 + np.abs(np.random.randn(n) * 0.01)),
        'low': prices * (1 - np.abs(np.random.randn(n) * 0.01)),
        'close': prices,
        'volume': 1000 + np.random.randint(-100, 100, n)
    }
    df = pd.DataFrame(data, index=dates)
    
    print(f"Benchmarking with {n} data points...")
    
    # Standard indicators
    start_time = time.time()
    df_standard = add_all_indicators(df.copy())
    standard_time = time.time() - start_time
    
    # Optimized indicators
    start_time = time.time()
    df_optimized = OptimizedIndicators.add_optimized_indicators(df.copy())
    optimized_time = time.time() - start_time
    
    speedup = standard_time / optimized_time
    
    print(f"\n📊 Performance Benchmark Results:")
    print(f"Standard indicators: {standard_time:.3f}s")
    print(f"Optimized indicators: {optimized_time:.3f}s")
    print(f"Speedup: {speedup:.1f}x faster")
    
    return speedup


if __name__ == "__main__":
    benchmark_performance()
