"""
Tests for paper trading functionality.
"""

import pytest
import tempfile
import os
import sys
from datetime import datetime, timezone

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from execution.paper_trader import (
    PaperTrader, Order, Position, OrderType, OrderSide, OrderStatus
)


class TestPaperTrader:
    """Test cases for PaperTrader."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        os.unlink(path)
    
    @pytest.fixture
    def trader(self, temp_db):
        """Create PaperTrader instance with temporary database."""
        return PaperTrader(
            initial_balance=10000.0,
            commission_rate=0.001,
            slippage_bps=2.0,
            db_path=temp_db
        )
    
    def test_trader_initialization(self, trader):
        """Test PaperTrader initialization."""
        assert trader.initial_balance == 10000.0
        assert trader.balance == 10000.0
        assert trader.commission_rate == 0.001
        assert trader.slippage_bps == 0.0002  # 2 bps converted to decimal
        assert len(trader.positions) == 0
        assert len(trader.orders) == 0
    
    def test_order_creation(self, trader):
        """Test order creation."""
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        
        assert order_id.startswith("PAPER_")
        assert order_id in trader.orders
        
        order = trader.orders[order_id]
        assert order.symbol == "BTCUSDT"
        assert order.side == OrderSide.BUY
        assert order.order_type == OrderType.MARKET
        assert order.quantity == 0.1
        assert order.status == OrderStatus.PENDING
    
    def test_limit_order_creation(self, trader):
        """Test limit order creation."""
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.LIMIT,
            0.1,
            price=50000.0
        )
        
        order = trader.orders[order_id]
        assert order.order_type == OrderType.LIMIT
        assert order.price == 50000.0
    
    def test_stop_order_creation(self, trader):
        """Test stop order creation."""
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.SELL,
            OrderType.STOP,
            0.1,
            stop_price=49000.0
        )
        
        order = trader.orders[order_id]
        assert order.order_type == OrderType.STOP
        assert order.stop_price == 49000.0
    
    def test_market_order_execution(self, trader):
        """Test market order execution."""
        # Create buy order
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        
        # Process market data to execute order
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Check order was executed
        order = trader.orders[order_id]
        assert order.status == OrderStatus.FILLED
        assert order.filled_price is not None
        assert order.filled_quantity == 0.1
        
        # Check position was created
        assert "BTCUSDT" in trader.positions
        position = trader.positions["BTCUSDT"]
        assert position.quantity == 0.1
        assert position.side == "LONG"
        
        # Check balance was reduced
        expected_cost = 0.1 * order.filled_price * (1 + trader.commission_rate)
        assert trader.balance < 10000.0
        assert abs(trader.balance - (10000.0 - expected_cost)) < 1.0
    
    def test_limit_order_execution(self, trader):
        """Test limit order execution."""
        # Create limit buy order below market
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.LIMIT,
            0.1,
            price=49000.0
        )
        
        # Process market data above limit price - should not execute
        trader.process_market_data("BTCUSDT", 50000.0)
        order = trader.orders[order_id]
        assert order.status == OrderStatus.PENDING
        
        # Process market data at limit price - should execute
        trader.process_market_data("BTCUSDT", 49000.0)
        order = trader.orders[order_id]
        assert order.status == OrderStatus.FILLED
        assert order.filled_price == 49000.0
    
    def test_stop_order_execution(self, trader):
        """Test stop order execution."""
        # First create a position
        buy_order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Create stop sell order
        stop_order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.SELL,
            OrderType.STOP,
            0.1,
            stop_price=49000.0
        )
        
        # Process market data above stop - should not execute
        trader.process_market_data("BTCUSDT", 50500.0)
        stop_order = trader.orders[stop_order_id]
        assert stop_order.status == OrderStatus.PENDING
        
        # Process market data at stop price - should execute
        trader.process_market_data("BTCUSDT", 49000.0)
        stop_order = trader.orders[stop_order_id]
        assert stop_order.status == OrderStatus.FILLED
    
    def test_insufficient_balance_validation(self, trader):
        """Test validation of insufficient balance."""
        # Try to buy more than balance allows
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            1.0  # 1 BTC at 50k = 50k, but balance is only 10k
        )
        
        # Order should be rejected
        order = trader.orders[order_id]
        assert order.status == OrderStatus.REJECTED
    
    def test_insufficient_position_validation(self, trader):
        """Test validation of insufficient position for sell."""
        # Try to sell without having position
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.SELL,
            OrderType.MARKET,
            0.1
        )
        
        # Order should be rejected
        order = trader.orders[order_id]
        assert order.status == OrderStatus.REJECTED
    
    def test_position_updates(self, trader):
        """Test position price updates."""
        # Create position
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        trader.process_market_data("BTCUSDT", 50000.0)
        
        position = trader.positions["BTCUSDT"]
        initial_pnl = position.unrealized_pnl
        
        # Update with higher price
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Check position was updated
        assert position.current_price == 51000.0
        assert position.unrealized_pnl > initial_pnl
        assert position.unrealized_pnl > 0  # Should be profitable
    
    def test_portfolio_summary(self, trader):
        """Test portfolio summary calculation."""
        # Initial summary
        summary = trader.get_portfolio_summary()
        assert summary['balance'] == 10000.0
        assert summary['unrealized_pnl'] == 0.0
        assert summary['total_equity'] == 10000.0
        assert summary['total_return_pct'] == 0.0
        assert summary['positions_count'] == 0
        
        # Create position
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Update price
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Check updated summary
        summary = trader.get_portfolio_summary()
        assert summary['positions_count'] == 1
        assert summary['unrealized_pnl'] > 0
        assert summary['total_equity'] > summary['balance']
        assert summary['total_return_pct'] > 0
    
    def test_order_cancellation(self, trader):
        """Test order cancellation."""
        # Create limit order that won't execute immediately
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.LIMIT,
            0.1,
            price=40000.0  # Well below market
        )
        
        # Cancel order
        success = trader.cancel_order(order_id)
        assert success
        
        order = trader.orders[order_id]
        assert order.status == OrderStatus.CANCELLED
        
        # Try to cancel already cancelled order
        success = trader.cancel_order(order_id)
        assert not success
    
    def test_multiple_positions(self, trader):
        """Test handling multiple positions."""
        # Create first position
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Add to position
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.05)
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Check position was combined
        position = trader.positions["BTCUSDT"]
        assert position.quantity == 0.15
        
        # Entry price should be weighted average
        expected_entry = (0.1 * 50000 + 0.05 * 51000) / 0.15
        assert abs(position.entry_price - expected_entry) < 1.0
    
    def test_partial_position_close(self, trader):
        """Test partial position closing."""
        # Create position
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.2)
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Partially close position
        trader.create_order("BTCUSDT", OrderSide.SELL, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Check position was reduced
        position = trader.positions["BTCUSDT"]
        assert position.quantity == 0.1
        
        # Check realized P&L was recorded
        assert position.realized_pnl > 0
    
    def test_complete_position_close(self, trader):
        """Test complete position closing."""
        # Create position
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 50000.0)
        
        # Close entire position
        trader.create_order("BTCUSDT", OrderSide.SELL, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Position should be removed
        assert "BTCUSDT" not in trader.positions
    
    def test_slippage_application(self, trader):
        """Test slippage application in market orders."""
        # Create buy market order
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        
        market_price = 50000.0
        trader.process_market_data("BTCUSDT", market_price)
        
        order = trader.orders[order_id]
        
        # Buy order should have positive slippage (higher price)
        expected_slippage = market_price * trader.slippage_bps
        expected_price = market_price + expected_slippage
        
        assert abs(order.filled_price - expected_price) < 1.0
    
    def test_commission_calculation(self, trader):
        """Test commission calculation."""
        order_id = trader.create_order(
            "BTCUSDT",
            OrderSide.BUY,
            OrderType.MARKET,
            0.1
        )
        
        trader.process_market_data("BTCUSDT", 50000.0)
        
        order = trader.orders[order_id]
        expected_commission = 0.1 * order.filled_price * trader.commission_rate
        
        assert abs(order.commission - expected_commission) < 0.01
    
    def test_performance_metrics(self, trader):
        """Test performance metrics calculation."""
        # Create some trades
        trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 50000.0)
        
        trader.process_market_data("BTCUSDT", 51000.0)
        
        trader.create_order("BTCUSDT", OrderSide.SELL, OrderType.MARKET, 0.1)
        trader.process_market_data("BTCUSDT", 51000.0)
        
        # Get performance metrics
        metrics = trader.get_performance_metrics()
        
        if metrics:  # Only check if we have enough data
            assert 'total_return' in metrics
            assert 'sharpe_ratio' in metrics
            assert 'max_drawdown' in metrics
            assert 'volatility' in metrics
    
    def test_data_persistence(self, temp_db):
        """Test data persistence to database."""
        trader1 = PaperTrader(db_path=temp_db)
        
        # Create and execute order
        order_id = trader1.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
        trader1.process_market_data("BTCUSDT", 50000.0)
        
        # Create new trader instance with same database
        trader2 = PaperTrader(db_path=temp_db)
        
        # Check that data persists (this is a basic check - full persistence would require more complex implementation)
        assert os.path.exists(temp_db)


if __name__ == "__main__":
    pytest.main([__file__])
