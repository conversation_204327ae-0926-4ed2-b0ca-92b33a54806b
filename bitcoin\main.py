#!/usr/bin/env python3
"""
Main entry point for Bitcoin Trading Signals system.
Provides CLI interface for all major functionalities.
"""

import argparse
import sys
import os
import logging
from datetime import datetime
import pandas as pd

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.client import BinanceDataClient
from indicators.ta_wrappers import add_all_indicators, get_trading_signals
from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy
from execution.paper_trader import PaperTrader, OrderSide, OrderType
from risk.position_sizing import RiskManager
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_data(args):
    """Setup and initialize data collection."""
    print("🔄 Setting up data collection...")

    client = BinanceDataClient()

    # Fetch historical data
    print(f"📊 Fetching {args.limit} candles for {args.symbol}...")
    df = client.fetch_historical_klines(args.symbol, args.interval, args.limit)

    if df.empty:
        print("❌ Failed to fetch data")
        return

    # Store in database
    client.store_candles(df, args.symbol)
    print(f"✅ Stored {len(df)} candles in database")

    # Start WebSocket if requested
    if args.websocket:
        print("🔌 Starting WebSocket connection...")
        client.start_websocket(args.symbol.lower(), args.interval)
        print("✅ WebSocket started. Press Ctrl+C to stop.")

        try:
            import time
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            client.stop_websocket()
            print("\n🛑 WebSocket stopped")


def analyze_market(args):
    """Perform market analysis and generate signals."""
    print("📈 Analyzing market...")

    client = BinanceDataClient()
    df = client.get_candles(args.symbol, args.limit)

    if df.empty:
        print("❌ No data available. Run 'setup' first.")
        return

    # Add indicators
    print("🔧 Calculating technical indicators...")
    df = add_all_indicators(df)

    # Generate signals
    signals = get_trading_signals(df)

    # Get latest values
    latest = df.iloc[-1]
    latest_signals = signals.iloc[-1] if not signals.empty else None

    # Display analysis
    print("\n" + "="*50)
    print("📊 BITCOIN MARKET ANALYSIS")
    print("="*50)
    print(f"⏰ Time: {latest.name.strftime('%Y-%m-%d %H:%M:%S')} UTC")
    print(f"💰 Price: ${latest['close']:,.2f}")

    if 'RSI' in latest:
        rsi_status = 'Overbought 🔴' if latest['RSI'] > 70 else 'Oversold 🟢' if latest['RSI'] < 30 else 'Neutral 🟡'
        print(f"📊 RSI: {latest['RSI']:.1f} ({rsi_status})")

    if latest_signals is not None:
        ma_signal = 'Buy 🟢' if latest_signals['MA_Signal'] == 1 else 'Sell 🔴' if latest_signals['MA_Signal'] == -1 else 'Hold 🟡'
        print(f"📈 MA Signal: {ma_signal}")

        if 'SuperTrend_Buy' in latest_signals:
            st_signal = 'Buy 🟢' if latest_signals['SuperTrend_Buy'] else 'Sell 🔴' if latest_signals['SuperTrend_Sell'] else 'Hold 🟡'
            print(f"🎯 SuperTrend: {st_signal}")

    if 'ATR' in latest:
        print(f"📉 ATR: ${latest['ATR']:.2f}")

    if 'VWAP' in latest:
        print(f"📊 VWAP: ${latest['VWAP']:,.2f}")

    # Send Discord alert if webhook is configured
    webhook_url = os.getenv('DISCORD_WEBHOOK_URL')
    if webhook_url and args.discord:
        send_discord_alert(latest, latest_signals, webhook_url)


def run_backtest(args):
    """Run strategy backtesting."""
    print("🧪 Running backtest...")

    client = BinanceDataClient()
    df = client.get_candles(args.symbol, args.limit)

    if df.empty:
        print("❌ No data available. Run 'setup' first.")
        return

    # Select strategy
    if args.strategy == 'ma_rsi':
        strategy_class = MARSIStrategy
        strategy_params = {
            'ma_short': args.ma_short,
            'ma_long': args.ma_long,
            'rsi_period': args.rsi_period
        }
    elif args.strategy == 'supertrend':
        strategy_class = SuperTrendStrategy
        strategy_params = {
            'atr_period': args.atr_period,
            'atr_multiplier': args.atr_multiplier
        }
    else:
        print(f"❌ Unknown strategy: {args.strategy}")
        return

    # Run backtest
    runner = BacktestRunner(args.capital, args.commission)
    result = runner.run_backtest(strategy_class, df, strategy_params)

    # Display results
    print("\n" + "="*50)
    print("🧪 BACKTEST RESULTS")
    print("="*50)
    print(f"Strategy: {result['strategy']}")
    print(f"Period: {result['data_period']['start']} to {result['data_period']['end']}")
    print(f"Initial Capital: ${result['initial_cash']:,.2f}")
    print(f"Final Value: ${result['final_value']:,.2f}")
    print(f"Total Return: {result['total_return']:.2f}%")
    print(f"Sharpe Ratio: {result['metrics']['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {result['metrics']['max_drawdown']:.2f}%")
    print(f"Win Rate: {result['metrics']['win_rate']:.1f}%")
    print(f"Total Trades: {result['metrics']['total_trades']}")
    print(f"Profit Factor: {result['metrics']['profit_factor']:.2f}")

    # Generate HTML report if requested
    if args.report:
        report_path = f"backtest_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        runner.generate_report(result, report_path)
        print(f"📄 Report saved: {report_path}")


def paper_trade(args):
    """Run paper trading simulation."""
    print("📝 Starting paper trading...")

    # Initialize paper trader
    trader = PaperTrader(args.capital, args.commission)

    # Get current market data
    client = BinanceDataClient()
    df = client.get_candles(args.symbol, 1)

    if df.empty:
        print("❌ No market data available")
        return

    current_price = df['close'].iloc[-1]

    # Place order based on command
    if args.action == 'buy':
        order_id = trader.create_order(
            args.symbol,
            OrderSide.BUY,
            OrderType.MARKET,
            args.quantity
        )
        print(f"🟢 Buy order created: {order_id}")

    elif args.action == 'sell':
        order_id = trader.create_order(
            args.symbol,
            OrderSide.SELL,
            OrderType.MARKET,
            args.quantity
        )
        print(f"🔴 Sell order created: {order_id}")

    # Process market data to execute orders
    trader.process_market_data(args.symbol, current_price)

    # Show portfolio summary
    summary = trader.get_portfolio_summary()
    print("\n📊 Portfolio Summary:")
    print(f"Balance: ${summary['balance']:,.2f}")
    print(f"Unrealized P&L: ${summary['unrealized_pnl']:,.2f}")
    print(f"Total Equity: ${summary['total_equity']:,.2f}")
    print(f"Total Return: {summary['total_return_pct']:.2f}%")


def send_discord_alert(latest, signals, webhook_url):
    """Send alert to Discord."""
    try:
        rsi_status = 'Overbought' if latest['RSI'] > 70 else 'Oversold' if latest['RSI'] < 30 else 'Neutral'

        if signals is not None:
            ma_signal = 'Buy' if signals['MA_Signal'] == 1 else 'Sell' if signals['MA_Signal'] == -1 else 'Hold'
        else:
            ma_signal = 'N/A'

        message = f"""
🤖 **Bitcoin Signal Alert**

📊 **Price:** ${latest['close']:,.2f}
📈 **RSI:** {latest['RSI']:.1f} ({rsi_status})
🔄 **MA Signal:** {ma_signal}
📉 **ATR:** ${latest['ATR']:.2f}

⏰ {latest.name.strftime('%Y-%m-%d %H:%M:%S')} UTC
        """

        payload = {"content": message}
        response = requests.post(webhook_url, json=payload)

        if response.status_code == 204:
            print("✅ Discord alert sent")
        else:
            print(f"❌ Discord alert failed: {response.status_code}")

    except Exception as e:
        print(f"❌ Discord alert error: {e}")


def optimize_strategy(args):
    """Run parameter optimization."""
    print("🎯 Starting parameter optimization...")

    try:
        from optimization.parameter_optimizer import ParameterOptimizer

        # Load data
        client = BinanceDataClient()
        df = client.get_candles(args.symbol, args.limit)

        if df.empty:
            print("❌ No data available. Run 'setup' first.")
            return

        # Initialize optimizer
        optimizer = ParameterOptimizer()

        # Run optimization
        if args.strategy == 'ma_rsi':
            result = optimizer.optimize_ma_rsi_strategy(
                df, n_trials=args.trials, objective=args.objective
            )
        else:
            result = optimizer.optimize_supertrend_strategy(
                df, n_trials=args.trials, objective=args.objective
            )

        # Display results
        print("\n" + "="*50)
        print("🎯 OPTIMIZATION RESULTS")
        print("="*50)
        print(f"Strategy: {args.strategy}")
        print(f"Objective: {args.objective}")
        print(f"Trials: {args.trials}")
        print(f"Best Value: {result['best_value']:.3f}")
        print(f"\nBest Parameters:")
        for param, value in result['best_params'].items():
            print(f"  {param}: {value}")

        print(f"\nFinal Backtest Results:")
        final_result = result['final_result']
        print(f"  Total Return: {final_result['total_return']:.2f}%")
        print(f"  Sharpe Ratio: {final_result['metrics'].get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {final_result['metrics'].get('max_drawdown', 0):.2f}%")
        print(f"  Win Rate: {final_result['metrics'].get('win_rate', 0):.1f}%")

    except ImportError:
        print("❌ Optimization module not available. Install optuna: pip install optuna")
    except Exception as e:
        print(f"❌ Optimization failed: {e}")


def multi_timeframe_analysis(args):
    """Run multi-timeframe analysis."""
    print("📊 Starting multi-timeframe analysis...")

    try:
        from analysis.multi_timeframe import MultiTimeframeAnalyzer

        analyzer = MultiTimeframeAnalyzer(args.symbol)
        report = analyzer.generate_report()

        print(report)

        # Save to file
        filename = f"mtf_analysis_{args.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")

    except Exception as e:
        print(f"❌ Multi-timeframe analysis failed: {e}")


def generate_pdf_report(args):
    """Generate PDF report."""
    print("📄 Generating PDF report...")

    try:
        from reports.pdf_generator import TradingReportGenerator
        from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy

        # Load data
        client = BinanceDataClient()
        df = client.get_candles(args.symbol, limit=1000)

        if df.empty:
            print("❌ No data available. Run 'setup' first.")
            return

        # Add indicators
        df = add_all_indicators(df)

        # Run backtest for report
        runner = BacktestRunner(initial_cash=10000, commission=0.001)

        if args.strategy == 'ma_rsi':
            strategy_class = MARSIStrategy
            strategy_params = {'printlog': False}
        else:
            strategy_class = SuperTrendStrategy
            strategy_params = {'printlog': False}

        result = runner.run_backtest(strategy_class, df, strategy_params)

        # Generate PDF report
        generator = TradingReportGenerator()
        output_path = args.output

        generator.generate_strategy_report(result, df, output_path)
        print(f"✅ PDF report generated: {output_path}")

    except ImportError as e:
        print(f"❌ Report generation failed. Missing dependency: {e}")
        print("Install required packages: pip install matplotlib seaborn")
    except Exception as e:
        print(f"❌ Report generation failed: {e}")


def run_ml_analysis(args):
    """Run machine learning analysis."""
    print("🤖 Starting machine learning analysis...")

    try:
        if args.features:
            # Feature engineering
            from ml.feature_engineering import AdvancedFeatureEngineer

            client = BinanceDataClient()
            df = client.get_candles(args.symbol, limit=2000)

            if df.empty:
                print("❌ No data available. Run 'setup' first.")
                return

            engineer = AdvancedFeatureEngineer()
            df_features = engineer.create_all_features(df)
            df_with_targets = engineer.create_target_variables(df_features)

            print(f"✅ Created {len(df_features.columns)} features")

            # Show feature importance
            selected_features = engineer.select_features(df_with_targets, 'target_up')
            print(f"✅ Selected {len(selected_features)} best features")

        if args.train:
            # Train ML models
            from ml.models import TradingMLModels
            from ml.feature_engineering import AdvancedFeatureEngineer

            client = BinanceDataClient()
            df = client.get_candles(args.symbol, limit=2000)

            if df.empty:
                print("❌ No data available. Run 'setup' first.")
                return

            # Feature engineering
            engineer = AdvancedFeatureEngineer()
            df_features = engineer.create_all_features(df)
            df_with_targets = engineer.create_target_variables(df_features)

            # Prepare data
            target = 'target_up'
            features = engineer.select_features(df_with_targets, target)
            df_clean = df_with_targets[features + [target]].dropna()

            if len(df_clean) < 100:
                print("❌ Insufficient clean data for training")
                return

            X = df_clean[features]
            y = df_clean[target]

            # Split data
            split_idx = int(len(X) * 0.8)
            X_train = X.iloc[:split_idx]
            y_train = y.iloc[:split_idx]
            X_test = X.iloc[split_idx:]
            y_test = y.iloc[split_idx:]

            # Train models
            ml_models = TradingMLModels()
            results = ml_models.train_models(X_train, y_train, X_test, y_test)

            # Evaluate
            evaluation = ml_models.evaluate_models(results, X_test, y_test)
            print("\n📊 Model Evaluation Results:")
            print(evaluation.to_string(index=False))

            # Save models
            ml_models.save_models(results, f"ml_models_{args.symbol}.joblib")

        if args.cv:
            # Cross-validation
            from ml.models import TradingMLModels
            from ml.feature_engineering import AdvancedFeatureEngineer

            client = BinanceDataClient()
            df = client.get_candles(args.symbol, limit=1000)

            if df.empty:
                print("❌ No data available. Run 'setup' first.")
                return

            # Feature engineering
            engineer = AdvancedFeatureEngineer()
            df_features = engineer.create_all_features(df)
            df_with_targets = engineer.create_target_variables(df_features)

            # Prepare data
            target = 'target_up'
            features = engineer.select_features(df_with_targets, target)[:20]  # Use top 20 features
            df_clean = df_with_targets[features + [target]].dropna()

            if len(df_clean) < 200:
                print("❌ Insufficient clean data for cross-validation")
                return

            X = df_clean[features]
            y = df_clean[target]

            # Run cross-validation
            ml_models = TradingMLModels()
            cv_results = ml_models.time_series_cross_validation(X, y, 'random_forest', n_splits=5)

            print(f"\n✅ Cross-validation completed")
            print(f"Mean AUC: {cv_results['mean_auc']:.3f} ± {cv_results['std_auc']:.3f}")

    except Exception as e:
        print(f"❌ ML analysis failed: {e}")


def run_walk_forward_analysis(args):
    """Run walk-forward analysis."""
    print("🔄 Starting walk-forward analysis...")

    try:
        from analysis.walk_forward import WalkForwardAnalyzer
        from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy

        # Load data
        client = BinanceDataClient()
        df = client.get_candles(args.symbol, limit=2000)

        if df.empty:
            print("❌ No data available. Run 'setup' first.")
            return

        # Initialize analyzer
        analyzer = WalkForwardAnalyzer(
            optimization_window=300,
            test_window=50,
            step_size=25
        )

        # Define parameter ranges
        if args.strategy == 'ma_rsi':
            strategy_class = MARSIStrategy
            param_ranges = {
                'ma_short': [5, 10, 15],
                'ma_long': [20, 30, 50],
                'rsi_period': [10, 14, 20]
            }
        else:
            strategy_class = SuperTrendStrategy
            param_ranges = {
                'atr_period': [10, 14, 20],
                'atr_multiplier': [2.0, 3.0, 4.0]
            }

        # Run analysis
        results = analyzer.run_walk_forward_analysis(
            df, strategy_class, param_ranges, 'sharpe_ratio'
        )

        # Generate report
        report = analyzer.generate_report(results)
        print(report)

        # Save results
        filename = f"walk_forward_{args.strategy}_{args.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")

    except Exception as e:
        print(f"❌ Walk-forward analysis failed: {e}")


def run_monte_carlo_simulation(args):
    """Run Monte Carlo simulation."""
    print("🎲 Starting Monte Carlo simulation...")

    try:
        from analysis.monte_carlo import MonteCarloSimulator
        from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy

        # Load data
        client = BinanceDataClient()
        df = client.get_candles(args.symbol, limit=1000)

        if df.empty:
            print("❌ No data available. Run 'setup' first.")
            return

        # Initialize simulator
        simulator = MonteCarloSimulator()

        # Strategy parameters
        if args.strategy == 'ma_rsi':
            strategy_class = MARSIStrategy
            strategy_params = {
                'ma_short': 10,
                'ma_long': 50,
                'rsi_period': 14,
                'printlog': False
            }
        else:
            strategy_class = SuperTrendStrategy
            strategy_params = {
                'atr_period': 14,
                'atr_multiplier': 3.0,
                'printlog': False
            }

        # Run simulation
        results = simulator.run_monte_carlo_simulation(
            df, strategy_class, strategy_params,
            n_simulations=args.simulations,
            noise_level=args.noise
        )

        # Generate report
        report = simulator.generate_monte_carlo_report(results)
        print(report)

        # Save results
        filename = f"monte_carlo_{args.strategy}_{args.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")

    except Exception as e:
        print(f"❌ Monte Carlo simulation failed: {e}")


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Bitcoin Trading Signals System")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Setup command
    setup_parser = subparsers.add_parser('setup', help='Setup data collection')
    setup_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    setup_parser.add_argument('--interval', default='1h', help='Candle interval')
    setup_parser.add_argument('--limit', type=int, default=1000, help='Number of candles')
    setup_parser.add_argument('--websocket', action='store_true', help='Start WebSocket')

    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze market and generate signals')
    analyze_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    analyze_parser.add_argument('--limit', type=int, default=100, help='Number of candles')
    analyze_parser.add_argument('--discord', action='store_true', help='Send Discord alert')

    # Backtest command
    backtest_parser = subparsers.add_parser('backtest', help='Run strategy backtest')
    backtest_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    backtest_parser.add_argument('--strategy', choices=['ma_rsi', 'supertrend'], default='ma_rsi', help='Strategy to test')
    backtest_parser.add_argument('--capital', type=float, default=10000, help='Initial capital')
    backtest_parser.add_argument('--commission', type=float, default=0.001, help='Commission rate')
    backtest_parser.add_argument('--limit', type=int, default=1000, help='Number of candles')
    backtest_parser.add_argument('--report', action='store_true', help='Generate HTML report')

    # Strategy parameters
    backtest_parser.add_argument('--ma-short', type=int, default=10, help='MA short period')
    backtest_parser.add_argument('--ma-long', type=int, default=50, help='MA long period')
    backtest_parser.add_argument('--rsi-period', type=int, default=14, help='RSI period')
    backtest_parser.add_argument('--atr-period', type=int, default=10, help='ATR period')
    backtest_parser.add_argument('--atr-multiplier', type=float, default=3.0, help='ATR multiplier')

    # Paper trade command
    paper_parser = subparsers.add_parser('paper', help='Paper trading')
    paper_parser.add_argument('action', choices=['buy', 'sell', 'status'], help='Trading action')
    paper_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    paper_parser.add_argument('--quantity', type=float, default=0.01, help='Order quantity')
    paper_parser.add_argument('--capital', type=float, default=10000, help='Initial capital')
    paper_parser.add_argument('--commission', type=float, default=0.001, help='Commission rate')

    # Dashboard command
    dashboard_parser = subparsers.add_parser('dashboard', help='Start Streamlit dashboard')

    # Optimize command
    optimize_parser = subparsers.add_parser('optimize', help='Optimize strategy parameters')
    optimize_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    optimize_parser.add_argument('--strategy', choices=['ma_rsi', 'supertrend'], default='ma_rsi', help='Strategy to optimize')
    optimize_parser.add_argument('--trials', type=int, default=50, help='Number of optimization trials')
    optimize_parser.add_argument('--objective', choices=['sharpe_ratio', 'total_return', 'profit_factor'], default='sharpe_ratio', help='Optimization objective')
    optimize_parser.add_argument('--limit', type=int, default=1000, help='Number of candles')

    # Multi-timeframe command
    mtf_parser = subparsers.add_parser('mtf', help='Multi-timeframe analysis')
    mtf_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')

    # Report command
    report_parser = subparsers.add_parser('report', help='Generate PDF report')
    report_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    report_parser.add_argument('--strategy', choices=['ma_rsi', 'supertrend'], default='ma_rsi', help='Strategy for report')
    report_parser.add_argument('--output', default='trading_report.pdf', help='Output PDF path')

    # ML commands
    ml_parser = subparsers.add_parser('ml', help='Machine learning analysis')
    ml_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    ml_parser.add_argument('--features', action='store_true', help='Run feature engineering')
    ml_parser.add_argument('--train', action='store_true', help='Train ML models')
    ml_parser.add_argument('--cv', action='store_true', help='Run cross-validation')

    # Walk-forward analysis
    wf_parser = subparsers.add_parser('walkforward', help='Walk-forward analysis')
    wf_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    wf_parser.add_argument('--strategy', choices=['ma_rsi', 'supertrend'], default='ma_rsi', help='Strategy to test')
    wf_parser.add_argument('--periods', type=int, default=10, help='Number of walk-forward periods')

    # Monte Carlo simulation
    mc_parser = subparsers.add_parser('montecarlo', help='Monte Carlo simulation')
    mc_parser.add_argument('--symbol', default='BTCUSDT', help='Trading symbol')
    mc_parser.add_argument('--strategy', choices=['ma_rsi', 'supertrend'], default='ma_rsi', help='Strategy to test')
    mc_parser.add_argument('--simulations', type=int, default=1000, help='Number of simulations')
    mc_parser.add_argument('--noise', type=float, default=0.02, help='Noise level for simulation')

    args = parser.parse_args()

    if args.command == 'setup':
        setup_data(args)
    elif args.command == 'analyze':
        analyze_market(args)
    elif args.command == 'backtest':
        run_backtest(args)
    elif args.command == 'paper':
        paper_trade(args)
    elif args.command == 'dashboard':
        os.system('streamlit run app.py')
    elif args.command == 'optimize':
        optimize_strategy(args)
    elif args.command == 'mtf':
        multi_timeframe_analysis(args)
    elif args.command == 'report':
        generate_pdf_report(args)
    elif args.command == 'ml':
        run_ml_analysis(args)
    elif args.command == 'walkforward':
        run_walk_forward_analysis(args)
    elif args.command == 'montecarlo':
        run_monte_carlo_simulation(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
