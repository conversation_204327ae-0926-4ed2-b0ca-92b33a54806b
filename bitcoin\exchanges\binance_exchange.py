"""
Binance exchange implementation using the unified exchange interface.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
import time
import hmac
import hashlib
import requests
from urllib.parse import urlencode

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exchanges.base_exchange import (
    BaseExchange, OrderType, OrderSide, OrderStatus,
    ExchangeError, InsufficientFundsError, InvalidSymbolError, RateLimitError
)


class BinanceExchange(BaseExchange):
    """
    Binance exchange implementation with full trading capabilities.
    """
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = True):
        super().__init__(api_key, secret_key, testnet)
        
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
        
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
    
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """Generate signature for authenticated requests."""
        query_string = urlencode(params)
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, 
                     method: str, 
                     endpoint: str, 
                     params: Optional[Dict] = None,
                     signed: bool = False) -> Dict[str, Any]:
        """Make HTTP request to Binance API."""
        if params is None:
            params = {}
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            params['signature'] = self._generate_signature(params)
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = self.session.get(url, params=params)
            elif method == 'POST':
                response = self.session.post(url, params=params)
            elif method == 'DELETE':
                response = self.session.delete(url, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            if response.status_code == 429:
                raise RateLimitError("API rate limit exceeded")
            elif response.status_code == 400:
                error_data = response.json()
                if error_data.get('code') == -2010:
                    raise InsufficientFundsError("Insufficient account balance")
                elif error_data.get('code') == -1121:
                    raise InvalidSymbolError("Invalid symbol")
                else:
                    raise ExchangeError(f"API error: {error_data}")
            else:
                raise ExchangeError(f"HTTP error {response.status_code}: {e}")
        except requests.exceptions.RequestException as e:
            raise ExchangeError(f"Request failed: {e}")
    
    def get_account_balance(self) -> Dict[str, float]:
        """Get account balance for all assets."""
        data = self._make_request('GET', '/api/v3/account', signed=True)
        
        balances = {}
        for balance in data['balances']:
            asset = balance['asset']
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked
            
            if total > 0:  # Only include assets with balance
                balances[asset] = {
                    'free': free,
                    'locked': locked,
                    'total': total
                }
        
        return balances
    
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get current ticker information."""
        params = {'symbol': symbol}
        data = self._make_request('GET', '/api/v3/ticker/24hr', params)
        
        return {
            'symbol': data['symbol'],
            'price': float(data['lastPrice']),
            'bid': float(data['bidPrice']),
            'ask': float(data['askPrice']),
            'volume': float(data['volume']),
            'quote_volume': float(data['quoteVolume']),
            'price_change': float(data['priceChange']),
            'price_change_percent': float(data['priceChangePercent']),
            'high': float(data['highPrice']),
            'low': float(data['lowPrice']),
            'open': float(data['openPrice']),
            'close': float(data['lastPrice']),
            'timestamp': int(data['closeTime'])
        }
    
    def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, List]:
        """Get order book for a symbol."""
        params = {'symbol': symbol, 'limit': limit}
        data = self._make_request('GET', '/api/v3/depth', params)
        
        return {
            'bids': [[float(price), float(qty)] for price, qty in data['bids']],
            'asks': [[float(price), float(qty)] for price, qty in data['asks']],
            'timestamp': data['lastUpdateId']
        }
    
    def get_klines(self, 
                   symbol: str, 
                   interval: str, 
                   limit: int = 500,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> pd.DataFrame:
        """Get historical kline data."""
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': min(limit, 1000)  # Binance limit
        }
        
        if start_time:
            params['startTime'] = int(start_time.timestamp() * 1000)
        if end_time:
            params['endTime'] = int(end_time.timestamp() * 1000)
        
        data = self._make_request('GET', '/api/v3/klines', params)
        
        # Convert to DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'trades', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ])
        
        # Convert types and set index
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)
        
        return df[['open', 'high', 'low', 'close', 'volume']]
    
    def place_order(self,
                    symbol: str,
                    side: OrderSide,
                    order_type: OrderType,
                    quantity: float,
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    time_in_force: str = "GTC") -> Dict[str, Any]:
        """Place a trading order."""
        params = {
            'symbol': symbol,
            'side': side.value.upper(),
            'type': self._convert_order_type(order_type),
            'quantity': self.format_quantity(symbol, quantity),
            'timeInForce': time_in_force
        }
        
        if order_type in [OrderType.LIMIT, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT]:
            if price is None:
                raise ValueError("Price required for limit/stop orders")
            params['price'] = self.format_price(symbol, price)
        
        if order_type == OrderType.STOP_LOSS and stop_price:
            params['stopPrice'] = self.format_price(symbol, stop_price)
        
        data = self._make_request('POST', '/api/v3/order', params, signed=True)
        
        return {
            'order_id': data['orderId'],
            'symbol': data['symbol'],
            'side': data['side'].lower(),
            'type': data['type'].lower(),
            'quantity': float(data['origQty']),
            'price': float(data['price']) if data['price'] != '0.00000000' else None,
            'status': data['status'].lower(),
            'timestamp': data['transactTime']
        }
    
    def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Cancel an existing order."""
        params = {
            'symbol': symbol,
            'orderId': order_id
        }
        
        data = self._make_request('DELETE', '/api/v3/order', params, signed=True)
        
        return {
            'order_id': data['orderId'],
            'symbol': data['symbol'],
            'status': 'cancelled',
            'timestamp': int(time.time() * 1000)
        }
    
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get status of an order."""
        params = {
            'symbol': symbol,
            'orderId': order_id
        }
        
        data = self._make_request('GET', '/api/v3/order', params, signed=True)
        
        return {
            'order_id': data['orderId'],
            'symbol': data['symbol'],
            'side': data['side'].lower(),
            'type': data['type'].lower(),
            'quantity': float(data['origQty']),
            'executed_quantity': float(data['executedQty']),
            'price': float(data['price']) if data['price'] != '0.00000000' else None,
            'status': data['status'].lower(),
            'timestamp': data['time']
        }
    
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all open orders."""
        params = {}
        if symbol:
            params['symbol'] = symbol
        
        data = self._make_request('GET', '/api/v3/openOrders', params, signed=True)
        
        orders = []
        for order in data:
            orders.append({
                'order_id': order['orderId'],
                'symbol': order['symbol'],
                'side': order['side'].lower(),
                'type': order['type'].lower(),
                'quantity': float(order['origQty']),
                'executed_quantity': float(order['executedQty']),
                'price': float(order['price']) if order['price'] != '0.00000000' else None,
                'status': order['status'].lower(),
                'timestamp': order['time']
            })
        
        return orders
    
    def get_trade_history(self, 
                         symbol: str, 
                         limit: int = 500,
                         start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get trade history."""
        params = {
            'symbol': symbol,
            'limit': min(limit, 1000)
        }
        
        if start_time:
            params['startTime'] = int(start_time.timestamp() * 1000)
        if end_time:
            params['endTime'] = int(end_time.timestamp() * 1000)
        
        data = self._make_request('GET', '/api/v3/myTrades', params, signed=True)
        
        trades = []
        for trade in data:
            trades.append({
                'trade_id': trade['id'],
                'order_id': trade['orderId'],
                'symbol': trade['symbol'],
                'side': 'buy' if trade['isBuyer'] else 'sell',
                'quantity': float(trade['qty']),
                'price': float(trade['price']),
                'commission': float(trade['commission']),
                'commission_asset': trade['commissionAsset'],
                'timestamp': trade['time']
            })
        
        return trades
    
    def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information."""
        data = self._make_request('GET', '/api/v3/exchangeInfo')
        return data
    
    def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        # For Binance, fees are typically 0.1% for both maker and taker
        # In production, you might want to fetch actual fees from API
        return {
            'maker': 0.001,  # 0.1%
            'taker': 0.001   # 0.1%
        }
    
    def _convert_order_type(self, order_type: OrderType) -> str:
        """Convert internal order type to Binance format."""
        mapping = {
            OrderType.MARKET: 'MARKET',
            OrderType.LIMIT: 'LIMIT',
            OrderType.STOP_LOSS: 'STOP_LOSS_LIMIT',
            OrderType.TAKE_PROFIT: 'TAKE_PROFIT_LIMIT'
        }
        return mapping[order_type]


def main():
    """Example usage of BinanceExchange."""
    # Note: Use test credentials for testnet
    exchange = BinanceExchange(
        api_key="test_api_key",
        secret_key="test_secret_key",
        testnet=True
    )
    
    try:
        # Get ticker
        ticker = exchange.get_ticker("BTCUSDT")
        print(f"BTC Price: ${ticker['price']:,.2f}")
        
        # Get klines
        df = exchange.get_klines("BTCUSDT", "1h", limit=100)
        print(f"Loaded {len(df)} candles")
        print(f"Latest close: ${df['close'].iloc[-1]:,.2f}")
        
    except ExchangeError as e:
        print(f"Exchange error: {e}")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
