"""
Position sizing and risk management utilities.
Implements various position sizing methods and risk controls.
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class RiskManager:
    """
    Comprehensive risk management system for trading strategies.
    """

    def __init__(self,
                 max_risk_per_trade: float = 0.01,  # 1% max risk per trade
                 max_portfolio_risk: float = 0.05,   # 5% max portfolio risk
                 max_correlation: float = 0.7,       # Max correlation between positions
                 max_drawdown_limit: float = 0.15):  # 15% max drawdown before stopping

        self.max_risk_per_trade = max_risk_per_trade
        self.max_portfolio_risk = max_portfolio_risk
        self.max_correlation = max_correlation
        self.max_drawdown_limit = max_drawdown_limit

        # Track current positions and risk
        self.current_positions = {}
        self.portfolio_value = 0
        self.max_portfolio_value = 0
        self.current_drawdown = 0

    def calculate_position_size_fixed_risk(self,
                                         capital: float,
                                         entry_price: float,
                                         stop_loss: float,
                                         risk_percentage: float = None) -> float:
        """
        Calculate position size based on fixed risk percentage.

        Formula: Position Size = (Capital * Risk%) / (Entry Price - Stop Loss)
        """
        risk_pct = risk_percentage or self.max_risk_per_trade

        if stop_loss >= entry_price:
            logger.warning("Stop loss must be below entry price for long positions")
            return 0

        risk_amount = capital * risk_pct
        price_risk = abs(entry_price - stop_loss)

        if price_risk == 0:
            return 0

        position_size = risk_amount / price_risk

        # Ensure we don't exceed available capital
        max_shares = capital / entry_price * 0.95  # Leave 5% buffer
        position_size = min(position_size, max_shares)

        return position_size

    def calculate_atr_stop_target(self,
                                df: pd.DataFrame,
                                atr_stop_mult: float = 1.5,
                                atr_target_mult: float = 3.0) -> pd.DataFrame:
        """
        Calculate ATR-based stop loss and take profit levels.
        """
        df = df.copy()

        # Ensure ATR is calculated
        if 'ATR' not in df.columns:
            from indicators.ta_wrappers import add_atr
            df = add_atr(df)

        # Calculate stop and target levels
        df['stop_loss'] = df['close'] - (atr_stop_mult * df['ATR'])
        df['take_profit'] = df['close'] + (atr_target_mult * df['ATR'])

        # For short positions (if needed)
        df['stop_loss_short'] = df['close'] + (atr_stop_mult * df['ATR'])
        df['take_profit_short'] = df['close'] - (atr_target_mult * df['ATR'])

        return df

    def calculate_kelly_criterion(self,
                                win_rate: float,
                                avg_win: float,
                                avg_loss: float) -> float:
        """
        Calculate optimal position size using Kelly Criterion.

        Formula: f* = (bp - q) / b
        where:
        - b = odds received (avg_win / avg_loss)
        - p = probability of winning (win_rate)
        - q = probability of losing (1 - win_rate)
        """
        if avg_loss == 0 or win_rate <= 0 or win_rate >= 1:
            return 0

        b = avg_win / avg_loss  # Odds
        p = win_rate
        q = 1 - win_rate

        kelly_fraction = (b * p - q) / b

        # Cap Kelly at reasonable levels (usually 25% max)
        kelly_fraction = max(0, min(kelly_fraction, 0.25))

        return kelly_fraction

    def calculate_volatility_position_size(self,
                                         capital: float,
                                         target_volatility: float,
                                         asset_volatility: float) -> float:
        """
        Calculate position size based on volatility targeting.

        Position Size = (Target Vol / Asset Vol) * Capital
        """
        if asset_volatility == 0:
            return 0

        vol_ratio = target_volatility / asset_volatility
        position_size = vol_ratio * capital

        return max(0, position_size)

    def calculate_sharpe_optimal_size(self,
                                    returns: pd.Series,
                                    risk_free_rate: float = 0.02) -> float:
        """
        Calculate optimal position size to maximize Sharpe ratio.
        """
        if len(returns) < 30:  # Need sufficient data
            return 0

        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate

        if excess_returns.std() == 0:
            return 0

        # Optimal leverage for maximum Sharpe
        optimal_leverage = excess_returns.mean() / (excess_returns.var())

        # Cap leverage at reasonable levels
        optimal_leverage = max(0, min(optimal_leverage, 2.0))

        return optimal_leverage

    def check_correlation_risk(self,
                             new_position_returns: pd.Series,
                             existing_positions_returns: Dict[str, pd.Series]) -> bool:
        """
        Check if new position would create excessive correlation risk.
        """
        if not existing_positions_returns:
            return True  # No existing positions, safe to proceed

        for symbol, returns in existing_positions_returns.items():
            # Align series for correlation calculation
            aligned_new, aligned_existing = new_position_returns.align(returns, join='inner')

            if len(aligned_new) < 30:  # Need sufficient data
                continue

            correlation = aligned_new.corr(aligned_existing)

            if abs(correlation) > self.max_correlation:
                logger.warning(f"High correlation ({correlation:.2f}) with existing position {symbol}")
                return False

        return True

    def update_portfolio_state(self, current_value: float):
        """Update portfolio tracking for drawdown monitoring."""
        self.portfolio_value = current_value

        if current_value > self.max_portfolio_value:
            self.max_portfolio_value = current_value

        self.current_drawdown = (self.max_portfolio_value - current_value) / self.max_portfolio_value

    def should_stop_trading(self) -> bool:
        """Check if trading should be stopped due to excessive drawdown."""
        return self.current_drawdown > self.max_drawdown_limit

    def calculate_trailing_stop(self,
                              entry_price: float,
                              current_price: float,
                              highest_price: float,
                              atr: float,
                              trail_mult: float = 2.0) -> float:
        """
        Calculate trailing stop loss level.
        """
        # Initial stop
        initial_stop = entry_price - (trail_mult * atr)

        # Trailing stop (moves up with price)
        trailing_stop = highest_price - (trail_mult * atr)

        # Use the higher of initial stop and trailing stop
        return max(initial_stop, trailing_stop)

    def get_position_summary(self) -> Dict:
        """Get current portfolio risk summary."""
        total_risk = sum(pos.get('risk_amount', 0) for pos in self.current_positions.values())
        portfolio_risk_pct = total_risk / self.portfolio_value if self.portfolio_value > 0 else 0

        return {
            'total_positions': len(self.current_positions),
            'total_risk_amount': total_risk,
            'portfolio_risk_percentage': portfolio_risk_pct * 100,
            'current_drawdown': self.current_drawdown * 100,
            'max_drawdown_limit': self.max_drawdown_limit * 100,
            'trading_allowed': not self.should_stop_trading()
        }


class PositionSizer:
    """
    Simplified position sizing utility for quick calculations.
    """

    @staticmethod
    def fixed_risk_size(capital: float,
                       entry: float,
                       stop: float,
                       risk_pct: float = 0.01) -> int:
        """Quick fixed risk position sizing."""
        if stop >= entry or entry <= 0:
            return 0

        risk_amount = capital * risk_pct
        price_risk = abs(entry - stop)

        if price_risk == 0:
            return 0

        # Calculate number of shares/units
        size = risk_amount / price_risk

        # Ensure we don't exceed available capital
        max_size = (capital * 0.95) / entry  # 95% of capital

        return int(min(size, max_size))

    @staticmethod
    def percent_of_capital(capital: float,
                          price: float,
                          percent: float = 0.1) -> int:
        """Size position as percentage of capital."""
        if price <= 0:
            return 0

        amount = capital * percent
        return int(amount / price)

    @staticmethod
    def volatility_adjusted(capital: float,
                           price: float,
                           volatility: float,
                           target_vol: float = 0.02) -> int:
        """Size position based on volatility adjustment."""
        if price <= 0 or volatility <= 0:
            return 0

        vol_adjustment = target_vol / volatility
        base_size = capital * 0.1 / price  # 10% base allocation

        return int(base_size * vol_adjustment)


def calculate_risk_metrics(returns: pd.Series) -> Dict[str, float]:
    """
    Calculate comprehensive risk metrics for a return series.
    """
    if len(returns) == 0:
        return {}

    metrics = {}

    # Basic metrics
    metrics['total_return'] = (1 + returns).prod() - 1
    metrics['annualized_return'] = (1 + returns.mean()) ** 252 - 1
    metrics['volatility'] = returns.std() * np.sqrt(252)

    # Risk metrics
    metrics['sharpe_ratio'] = metrics['annualized_return'] / metrics['volatility'] if metrics['volatility'] > 0 else 0

    # Downside metrics
    downside_returns = returns[returns < 0]
    if len(downside_returns) > 0:
        downside_vol = downside_returns.std() * np.sqrt(252)
        metrics['sortino_ratio'] = metrics['annualized_return'] / downside_vol if downside_vol > 0 else 0
    else:
        metrics['sortino_ratio'] = float('inf')

    # Drawdown
    cumulative = (1 + returns).cumprod()
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max

    metrics['max_drawdown'] = drawdown.min()
    metrics['current_drawdown'] = drawdown.iloc[-1]

    # VaR (Value at Risk)
    metrics['var_95'] = returns.quantile(0.05)
    metrics['var_99'] = returns.quantile(0.01)

    # Expected Shortfall (Conditional VaR)
    var_95 = metrics['var_95']
    tail_returns = returns[returns <= var_95]
    metrics['expected_shortfall'] = tail_returns.mean() if len(tail_returns) > 0 else 0

    return metrics


def main():
    """Example usage of risk management tools."""
    # Initialize risk manager
    risk_mgr = RiskManager(max_risk_per_trade=0.02)  # 2% risk per trade

    # Example position sizing
    capital = 10000
    entry_price = 50000
    stop_loss = 48500

    position_size = risk_mgr.calculate_position_size_fixed_risk(
        capital, entry_price, stop_loss
    )

    print(f"Capital: ${capital:,}")
    print(f"Entry: ${entry_price:,}")
    print(f"Stop Loss: ${stop_loss:,}")
    print(f"Position Size: {position_size:.4f} BTC")
    print(f"Position Value: ${position_size * entry_price:,.2f}")
    print(f"Risk Amount: ${position_size * (entry_price - stop_loss):,.2f}")

    # Kelly Criterion example
    kelly_size = risk_mgr.calculate_kelly_criterion(
        win_rate=0.6, avg_win=150, avg_loss=100
    )
    print(f"\nKelly Criterion suggests: {kelly_size:.1%} of capital")

    # Risk summary
    risk_mgr.update_portfolio_state(capital)
    summary = risk_mgr.get_position_summary()
    print(f"\nRisk Summary: {summary}")


if __name__ == "__main__":
    main()
