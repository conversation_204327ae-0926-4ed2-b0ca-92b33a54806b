["tests/test_backtest_runner.py::TestBacktestRunner::test_commission_impact", "tests/test_backtest_runner.py::TestBacktestRunner::test_compare_strategies", "tests/test_backtest_runner.py::TestBacktestRunner::test_date_filtering", "tests/test_backtest_runner.py::TestBacktestRunner::test_error_handling", "tests/test_backtest_runner.py::TestBacktestRunner::test_extract_metrics", "tests/test_backtest_runner.py::TestBacktestRunner::test_generate_report", "tests/test_backtest_runner.py::TestBacktestRunner::test_generate_report_to_file", "tests/test_backtest_runner.py::TestBacktestRunner::test_insufficient_data", "tests/test_backtest_runner.py::TestBacktestRunner::test_performance_metrics_calculation", "tests/test_backtest_runner.py::TestBacktestRunner::test_prepare_data", "tests/test_backtest_runner.py::TestBacktestRunner::test_prepare_data_validation", "tests/test_backtest_runner.py::TestBacktestRunner::test_run_backtest_ma_rsi", "tests/test_backtest_runner.py::TestBacktestRunner::test_run_backtest_supertrend", "tests/test_backtest_runner.py::TestBacktestRunner::test_runner_initialization", "tests/test_data_client.py::TestBinanceDataClient::test_data_types", "tests/test_data_client.py::TestBinanceDataClient::test_database_initialization", "tests/test_data_client.py::TestBinanceDataClient::test_deduplication", "tests/test_data_client.py::TestBinanceDataClient::test_empty_dataframe_handling", "tests/test_data_client.py::TestBinanceDataClient::test_get_latest_price", "tests/test_data_client.py::TestBinanceDataClient::test_health_check", "tests/test_data_client.py::TestBinanceDataClient::test_limit_parameter", "tests/test_data_client.py::TestBinanceDataClient::test_multiple_symbols", "tests/test_data_client.py::TestBinanceDataClient::test_store_and_retrieve_candles", "tests/test_data_client.py::test_fetch_historical_klines_data_structure", "tests/test_indicators.py::TestTechnicalIndicators::test_add_all_indicators", "tests/test_indicators.py::TestTechnicalIndicators::test_adx", "tests/test_indicators.py::TestTechnicalIndicators::test_atr", "tests/test_indicators.py::TestTechnicalIndicators::test_bollinger_bands", "tests/test_indicators.py::TestTechnicalIndicators::test_create_labels", "tests/test_indicators.py::TestTechnicalIndicators::test_data_integrity", "tests/test_indicators.py::TestTechnicalIndicators::test_edge_cases", "tests/test_indicators.py::TestTechnicalIndicators::test_get_trading_signals", "tests/test_indicators.py::TestTechnicalIndicators::test_integration_with_optimized", "tests/test_indicators.py::TestTechnicalIndicators::test_macd", "tests/test_indicators.py::TestTechnicalIndicators::test_moving_averages", "tests/test_indicators.py::TestTechnicalIndicators::test_obv", "tests/test_indicators.py::TestTechnicalIndicators::test_rsi", "tests/test_indicators.py::TestTechnicalIndicators::test_supertrend", "tests/test_indicators.py::TestTechnicalIndicators::test_vwap", "tests/test_paper_trader.py::TestPaperTrader::test_commission_calculation", "tests/test_paper_trader.py::TestPaperTrader::test_complete_position_close", "tests/test_paper_trader.py::TestPaperTrader::test_data_persistence", "tests/test_paper_trader.py::TestPaperTrader::test_insufficient_balance_validation", "tests/test_paper_trader.py::TestPaperTrader::test_insufficient_position_validation", "tests/test_paper_trader.py::TestPaperTrader::test_limit_order_creation", "tests/test_paper_trader.py::TestPaperTrader::test_limit_order_execution", "tests/test_paper_trader.py::TestPaperTrader::test_market_order_execution", "tests/test_paper_trader.py::TestPaperTrader::test_multiple_positions", "tests/test_paper_trader.py::TestPaperTrader::test_order_cancellation", "tests/test_paper_trader.py::TestPaperTrader::test_order_creation", "tests/test_paper_trader.py::TestPaperTrader::test_partial_position_close", "tests/test_paper_trader.py::TestPaperTrader::test_performance_metrics", "tests/test_paper_trader.py::TestPaperTrader::test_portfolio_summary", "tests/test_paper_trader.py::TestPaperTrader::test_position_updates", "tests/test_paper_trader.py::TestPaperTrader::test_slippage_application", "tests/test_paper_trader.py::TestPaperTrader::test_stop_order_creation", "tests/test_paper_trader.py::TestPaperTrader::test_stop_order_execution", "tests/test_paper_trader.py::TestPaperTrader::test_trader_initialization", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_data_types", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_edge_cases", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_atr", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_bollinger_bands", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_ema", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_macd", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_rsi", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_fast_sma", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_memory_efficiency", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_optimized_indicators_integration", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_performance_comparison", "tests/test_performance_optimized.py::TestOptimizedIndicators::test_signal_generation", "tests/test_performance_optimized.py::test_benchmark_function", "tests/test_risk_management.py::TestPositionSizer::test_edge_cases", "tests/test_risk_management.py::TestPositionSizer::test_fixed_risk_size", "tests/test_risk_management.py::TestPositionSizer::test_percent_of_capital", "tests/test_risk_management.py::TestPositionSizer::test_volatility_adjusted", "tests/test_risk_management.py::TestRiskManager::test_atr_stop_target_calculation", "tests/test_risk_management.py::TestRiskManager::test_correlation_risk_check", "tests/test_risk_management.py::TestRiskManager::test_drawdown_protection", "tests/test_risk_management.py::TestRiskManager::test_fixed_risk_position_sizing", "tests/test_risk_management.py::TestRiskManager::test_invalid_stop_loss", "tests/test_risk_management.py::TestRiskManager::test_kelly_criterion", "tests/test_risk_management.py::TestRiskManager::test_kelly_criterion_edge_cases", "tests/test_risk_management.py::TestRiskManager::test_portfolio_state_tracking", "tests/test_risk_management.py::TestRiskManager::test_position_summary", "tests/test_risk_management.py::TestRiskManager::test_risk_manager_initialization", "tests/test_risk_management.py::TestRiskManager::test_sharpe_optimal_sizing", "tests/test_risk_management.py::TestRiskManager::test_trailing_stop_calculation", "tests/test_risk_management.py::TestRiskManager::test_volatility_position_sizing", "tests/test_risk_management.py::TestRiskMetrics::test_calculate_risk_metrics", "tests/test_risk_management.py::TestRiskMetrics::test_constant_returns", "tests/test_risk_management.py::TestRiskMetrics::test_empty_returns", "tests/test_risk_management.py::TestRiskMetrics::test_negative_returns"]