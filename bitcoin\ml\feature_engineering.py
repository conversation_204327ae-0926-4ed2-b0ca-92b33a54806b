"""
Advanced feature engineering for machine learning trading models.
Creates sophisticated features from price and volume data.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from indicators.ta_wrappers import add_all_indicators


class AdvancedFeatureEngineer:
    """
    Create advanced features for machine learning models.
    Includes price patterns, volatility regimes, and market microstructure.
    """
    
    def __init__(self):
        self.feature_names = []
        
    def create_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive feature set."""
        print("🔧 Creating advanced features...")
        
        # Start with basic indicators
        df = add_all_indicators(df)
        
        # Add advanced features
        df = self._add_price_features(df)
        df = self._add_volatility_features(df)
        df = self._add_momentum_features(df)
        df = self._add_pattern_features(df)
        df = self._add_volume_features(df)
        df = self._add_time_features(df)
        df = self._add_regime_features(df)
        df = self._add_lag_features(df)
        df = self._add_rolling_features(df)
        df = self._add_interaction_features(df)
        
        print(f"✅ Created {len([c for c in df.columns if c not in ['open', 'high', 'low', 'close', 'volume']])} features")
        
        return df
    
    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features."""
        # Price ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Price position within bar
        df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['open_position'] = (df['open'] - df['low']) / (df['high'] - df['low'])
        
        # Body and shadow ratios
        df['body_size'] = abs(df['close'] - df['open']) / (df['high'] - df['low'])
        df['upper_shadow'] = (df['high'] - np.maximum(df['close'], df['open'])) / (df['high'] - df['low'])
        df['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / (df['high'] - df['low'])
        
        # Price gaps
        df['gap_up'] = np.where(df['open'] > df['high'].shift(1), 1, 0)
        df['gap_down'] = np.where(df['open'] < df['low'].shift(1), 1, 0)
        
        return df
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        # Returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Volatility measures
        for window in [5, 10, 20, 50]:
            df[f'volatility_{window}'] = df['returns'].rolling(window).std()
            df[f'realized_vol_{window}'] = np.sqrt(252) * df['returns'].rolling(window).std()
            
        # Parkinson volatility (high-low)
        df['parkinson_vol'] = np.sqrt(252 / (4 * np.log(2))) * np.sqrt(
            np.log(df['high'] / df['low']).rolling(20).mean()
        )
        
        # Garman-Klass volatility
        df['gk_vol'] = np.sqrt(252) * np.sqrt(
            0.5 * (np.log(df['high'] / df['low'])**2) - 
            (2 * np.log(2) - 1) * (np.log(df['close'] / df['open'])**2)
        ).rolling(20).mean()
        
        # Volatility regime
        vol_20 = df['returns'].rolling(20).std()
        vol_60 = df['returns'].rolling(60).std()
        df['vol_regime'] = np.where(vol_20 > vol_60 * 1.5, 1, 0)  # High vol regime
        
        return df
    
    def _add_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based features."""
        # Price momentum
        for period in [1, 3, 5, 10, 20]:
            df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
            
        # ROC (Rate of Change)
        for period in [5, 10, 20]:
            df[f'roc_{period}'] = ((df['close'] - df['close'].shift(period)) / 
                                  df['close'].shift(period)) * 100
        
        # Acceleration (momentum of momentum)
        df['momentum_acceleration'] = df['momentum_5'].diff()
        
        # Relative strength vs moving average
        for ma_period in [20, 50, 200]:
            ma = df['close'].rolling(ma_period).mean()
            df[f'rs_ma_{ma_period}'] = df['close'] / ma - 1
            
        return df
    
    def _add_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add candlestick pattern features."""
        # Doji patterns
        body_size = abs(df['close'] - df['open'])
        range_size = df['high'] - df['low']
        df['doji'] = np.where(body_size < range_size * 0.1, 1, 0)
        
        # Hammer/Hanging man
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        df['hammer'] = np.where(
            (lower_shadow > 2 * body_size) & (upper_shadow < body_size * 0.1), 1, 0
        )
        
        # Engulfing patterns
        df['bullish_engulfing'] = np.where(
            (df['close'] > df['open']) & 
            (df['close'].shift(1) < df['open'].shift(1)) &
            (df['open'] < df['close'].shift(1)) &
            (df['close'] > df['open'].shift(1)), 1, 0
        )
        
        df['bearish_engulfing'] = np.where(
            (df['close'] < df['open']) & 
            (df['close'].shift(1) > df['open'].shift(1)) &
            (df['open'] > df['close'].shift(1)) &
            (df['close'] < df['open'].shift(1)), 1, 0
        )
        
        # Inside/Outside bars
        df['inside_bar'] = np.where(
            (df['high'] < df['high'].shift(1)) & 
            (df['low'] > df['low'].shift(1)), 1, 0
        )
        
        df['outside_bar'] = np.where(
            (df['high'] > df['high'].shift(1)) & 
            (df['low'] < df['low'].shift(1)), 1, 0
        )
        
        return df
    
    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based features."""
        # Volume ratios
        for period in [5, 10, 20]:
            vol_ma = df['volume'].rolling(period).mean()
            df[f'volume_ratio_{period}'] = df['volume'] / vol_ma
            
        # Volume-price trend
        df['vpt'] = (df['volume'] * df['returns']).cumsum()
        
        # Ease of Movement
        distance_moved = (df['high'] + df['low']) / 2 - (df['high'].shift(1) + df['low'].shift(1)) / 2
        box_height = df['volume'] / (df['high'] - df['low'])
        df['ease_of_movement'] = distance_moved / box_height
        df['ease_of_movement_ma'] = df['ease_of_movement'].rolling(14).mean()
        
        # Volume oscillator
        vol_short = df['volume'].rolling(5).mean()
        vol_long = df['volume'].rolling(20).mean()
        df['volume_oscillator'] = ((vol_short - vol_long) / vol_long) * 100
        
        return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        # Hour of day (for intraday data)
        if hasattr(df.index, 'hour'):
            df['hour'] = df.index.hour
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            
        # Day of week
        if hasattr(df.index, 'dayofweek'):
            df['day_of_week'] = df.index.dayofweek
            df['is_weekend'] = np.where(df['day_of_week'].isin([5, 6]), 1, 0)
            
        # Month seasonality
        if hasattr(df.index, 'month'):
            df['month'] = df.index.month
            df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
            df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
            
        return df
    
    def _add_regime_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market regime features."""
        # Trend regime based on multiple MAs
        ma_20 = df['close'].rolling(20).mean()
        ma_50 = df['close'].rolling(50).mean()
        ma_200 = df['close'].rolling(200).mean()
        
        df['bull_regime'] = np.where(
            (ma_20 > ma_50) & (ma_50 > ma_200) & (df['close'] > ma_20), 1, 0
        )
        
        df['bear_regime'] = np.where(
            (ma_20 < ma_50) & (ma_50 < ma_200) & (df['close'] < ma_20), 1, 0
        )
        
        # Volatility clustering
        vol_short = df['returns'].rolling(5).std()
        vol_long = df['returns'].rolling(20).std()
        df['vol_clustering'] = vol_short / vol_long
        
        return df
    
    def _add_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add lagged features."""
        # Lagged returns
        for lag in [1, 2, 3, 5]:
            df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
            
        # Lagged RSI
        if 'RSI' in df.columns:
            for lag in [1, 2, 3]:
                df[f'rsi_lag_{lag}'] = df['RSI'].shift(lag)
                
        # Lagged volume
        for lag in [1, 2, 3]:
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
            
        return df
    
    def _add_rolling_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add rolling statistical features."""
        # Rolling statistics of returns
        for window in [5, 10, 20]:
            returns_roll = df['returns'].rolling(window)
            df[f'returns_mean_{window}'] = returns_roll.mean()
            df[f'returns_std_{window}'] = returns_roll.std()
            df[f'returns_skew_{window}'] = returns_roll.skew()
            df[f'returns_kurt_{window}'] = returns_roll.kurt()
            df[f'returns_min_{window}'] = returns_roll.min()
            df[f'returns_max_{window}'] = returns_roll.max()
            
        # Rolling correlation with volume
        for window in [10, 20]:
            df[f'price_volume_corr_{window}'] = df['returns'].rolling(window).corr(
                df['volume'].pct_change()
            )
            
        return df
    
    def _add_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features between indicators."""
        # RSI * Volume interaction
        if 'RSI' in df.columns:
            df['rsi_volume_interaction'] = df['RSI'] * df['volume_ratio_20']
            
        # Momentum * Volatility
        if 'momentum_5' in df.columns and 'volatility_20' in df.columns:
            df['momentum_vol_interaction'] = df['momentum_5'] * df['volatility_20']
            
        # MA cross strength
        if 'MA_Short' in df.columns and 'MA_Long' in df.columns:
            df['ma_cross_strength'] = (df['MA_Short'] - df['MA_Long']) / df['MA_Long']
            
        return df
    
    def select_features(self, df: pd.DataFrame, target: str, method: str = 'correlation') -> List[str]:
        """Select best features using various methods."""
        # Remove non-numeric columns and target
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col != target and not col.startswith(('open', 'high', 'low', 'close'))]
        
        if method == 'correlation':
            # Select features with high correlation to target but low correlation to each other
            corr_with_target = df[feature_cols + [target]].corr()[target].abs().sort_values(ascending=False)
            selected_features = corr_with_target.head(50).index.tolist()
            selected_features.remove(target)
            
        elif method == 'variance':
            # Select features with high variance
            variances = df[feature_cols].var().sort_values(ascending=False)
            selected_features = variances.head(50).index.tolist()
            
        else:
            # Default: use all features
            selected_features = feature_cols
            
        return selected_features
    
    def create_target_variables(self, df: pd.DataFrame, lookahead: int = 1) -> pd.DataFrame:
        """Create various target variables for different prediction tasks."""
        df = df.copy()
        
        # Future returns
        df['future_return'] = df['close'].shift(-lookahead) / df['close'] - 1
        
        # Binary classification targets
        df['target_up'] = np.where(df['future_return'] > 0, 1, 0)
        df['target_strong_up'] = np.where(df['future_return'] > 0.01, 1, 0)  # >1% move
        df['target_down'] = np.where(df['future_return'] < 0, 1, 0)
        df['target_strong_down'] = np.where(df['future_return'] < -0.01, 1, 0)  # <-1% move
        
        # Multi-class target
        df['target_direction'] = np.where(
            df['future_return'] > 0.005, 2,  # Strong up
            np.where(df['future_return'] > 0, 1,  # Weak up
                    np.where(df['future_return'] > -0.005, 0,  # Neutral
                            np.where(df['future_return'] > -0.01, -1, -2)))  # Weak down, Strong down
        )
        
        # Volatility target
        df['target_volatility'] = df['returns'].shift(-lookahead).rolling(5).std()
        
        return df


def main():
    """Example usage of AdvancedFeatureEngineer."""
    from data.client import BinanceDataClient
    
    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=1000)
    
    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=1000)
    
    print(f"Loaded {len(df)} candles")
    print(f"Original features: {len(df.columns)}")
    
    # Create features
    engineer = AdvancedFeatureEngineer()
    df_features = engineer.create_all_features(df)
    
    print(f"Total features after engineering: {len(df_features.columns)}")
    
    # Create targets
    df_with_targets = engineer.create_target_variables(df_features)
    
    # Select best features
    selected_features = engineer.select_features(df_with_targets, 'target_up')
    print(f"Selected {len(selected_features)} best features")
    
    # Show sample
    print("\nSample of engineered features:")
    feature_sample = [col for col in df_features.columns if col not in df.columns][:10]
    print(df_features[feature_sample].tail())
    
    # Feature importance preview
    print("\nTop 10 features by correlation with target:")
    if 'target_up' in df_with_targets.columns:
        corr_with_target = df_with_targets[selected_features + ['target_up']].corr()['target_up'].abs().sort_values(ascending=False)
        print(corr_with_target.head(10))


if __name__ == "__main__":
    main()
