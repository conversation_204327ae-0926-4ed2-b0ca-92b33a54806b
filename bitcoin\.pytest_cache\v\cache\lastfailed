{"tests/test_backtest_runner.py::TestBacktestRunner::test_run_backtest_ma_rsi": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_extract_metrics": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_date_filtering": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_generate_report": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_generate_report_to_file": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_error_handling": true, "tests/test_backtest_runner.py::TestBacktestRunner::test_performance_metrics_calculation": true, "tests/test_indicators.py::TestTechnicalIndicators::test_atr": true, "tests/test_indicators.py::TestTechnicalIndicators::test_obv": true, "tests/test_indicators.py::TestTechnicalIndicators::test_vwap": true, "tests/test_indicators.py::TestTechnicalIndicators::test_add_all_indicators": true, "tests/test_indicators.py::TestTechnicalIndicators::test_get_trading_signals": true, "tests/test_indicators.py::TestTechnicalIndicators::test_data_integrity": true, "tests/test_indicators.py::TestTechnicalIndicators::test_integration_with_optimized": true, "tests/test_paper_trader.py::TestPaperTrader::test_stop_order_creation": true, "tests/test_paper_trader.py::TestPaperTrader::test_insufficient_balance_validation": true, "tests/test_paper_trader.py::TestPaperTrader::test_insufficient_position_validation": true, "tests/test_paper_trader.py::TestPaperTrader::test_portfolio_summary": true, "tests/test_paper_trader.py::TestPaperTrader::test_multiple_positions": true, "tests/test_performance_optimized.py::TestOptimizedIndicators::test_performance_comparison": true, "tests/test_performance_optimized.py::test_benchmark_function": true, "tests/test_risk_management.py::TestPositionSizer::test_fixed_risk_size": true, "tests/test_risk_management.py::TestPositionSizer::test_volatility_adjusted": true, "tests/test_risk_management.py::TestRiskMetrics::test_constant_returns": true}