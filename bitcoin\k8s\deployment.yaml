apiVersion: apps/v1
kind: Deployment
metadata:
  name: bitcoin-trading-app
  namespace: trading
  labels:
    app: bitcoin-trading
    component: app
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: bitcoin-trading
      component: app
  template:
    metadata:
      labels:
        app: bitcoin-trading
        component: app
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: bitcoin-trading
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: trading-app
        image: ghcr.io/your-username/bitcoin-trading:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8501
          name: dashboard
          protocol: TCP
        - containerPort: 8000
          name: api
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: trading-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: BINANCE_API_KEY
          valueFrom:
            secretKeyRef:
              name: trading-secrets
              key: binance-api-key
        - name: BINANCE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: trading-secrets
              key: binance-secret-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8501
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8501
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: trading-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: trading-logs-pvc
      - name: config-volume
        configMap:
          name: trading-config
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "trading-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: bitcoin-trading-service
  namespace: trading
  labels:
    app: bitcoin-trading
    component: app
spec:
  type: ClusterIP
  ports:
  - port: 8501
    targetPort: 8501
    protocol: TCP
    name: dashboard
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: api
  selector:
    app: bitcoin-trading
    component: app

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: bitcoin-trading
  namespace: trading

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: trading-config
  namespace: trading
data:
  app.yaml: |
    app:
      name: "Bitcoin Trading System"
      version: "1.0.0"
      debug: false
    
    trading:
      default_symbol: "BTCUSDT"
      default_timeframe: "1h"
      max_positions: 5
      risk_per_trade: 0.01
    
    api:
      rate_limit: 100
      timeout: 30
    
    logging:
      level: "INFO"
      format: "json"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: trading-data-pvc
  namespace: trading
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: trading-logs-pvc
  namespace: trading
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bitcoin-trading-ingress
  namespace: trading
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - trading.yourdomain.com
    secretName: trading-tls
  rules:
  - host: trading.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: bitcoin-trading-service
            port:
              number: 8501
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: bitcoin-trading-service
            port:
              number: 8000

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bitcoin-trading-hpa
  namespace: trading
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bitcoin-trading-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
