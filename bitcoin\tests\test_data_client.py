"""
Unit tests for data client functionality.
"""

import pytest
import pandas as pd
import sqlite3
import tempfile
import os
from datetime import datetime, timezone
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.client import BinanceDataClient


class TestBinanceDataClient:
    """Test cases for BinanceDataClient."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        os.unlink(path)
    
    @pytest.fixture
    def client(self, temp_db):
        """Create test client with temporary database."""
        return BinanceDataClient(db_path=temp_db)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing."""
        dates = pd.date_range('2024-01-01', periods=100, freq='H')
        data = {
            'open': [50000 + i * 10 for i in range(100)],
            'high': [50100 + i * 10 for i in range(100)],
            'low': [49900 + i * 10 for i in range(100)],
            'close': [50050 + i * 10 for i in range(100)],
            'volume': [100 + i for i in range(100)]
        }
        df = pd.DataFrame(data, index=dates)
        return df
    
    def test_database_initialization(self, client):
        """Test that database is properly initialized."""
        # Check that database file exists
        assert os.path.exists(client.db_path)
        
        # Check that tables are created
        with sqlite3.connect(client.db_path) as conn:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            tables = [row[0] for row in cursor.fetchall()]
            assert 'candles' in tables
    
    def test_store_and_retrieve_candles(self, client, sample_data):
        """Test storing and retrieving candle data."""
        # Store data
        client.store_candles(sample_data, "BTCUSDT")
        
        # Retrieve data
        retrieved_df = client.get_candles("BTCUSDT", limit=100)
        
        # Check that data was stored and retrieved correctly
        assert len(retrieved_df) == len(sample_data)
        assert list(retrieved_df.columns) == ['open', 'high', 'low', 'close', 'volume']
        
        # Check first and last values
        assert abs(retrieved_df['close'].iloc[0] - sample_data['close'].iloc[0]) < 0.01
        assert abs(retrieved_df['close'].iloc[-1] - sample_data['close'].iloc[-1]) < 0.01
    
    def test_deduplication(self, client, sample_data):
        """Test that duplicate data is not stored."""
        # Store data twice
        client.store_candles(sample_data, "BTCUSDT")
        client.store_candles(sample_data, "BTCUSDT")
        
        # Should still have only one copy
        retrieved_df = client.get_candles("BTCUSDT", limit=200)
        assert len(retrieved_df) == len(sample_data)
    
    def test_empty_dataframe_handling(self, client):
        """Test handling of empty dataframes."""
        empty_df = pd.DataFrame()
        
        # Should not raise an error
        client.store_candles(empty_df, "BTCUSDT")
        
        # Should return empty dataframe
        retrieved_df = client.get_candles("BTCUSDT")
        assert retrieved_df.empty
    
    def test_get_latest_price(self, client, sample_data):
        """Test getting latest price."""
        # Store data
        client.store_candles(sample_data, "BTCUSDT")
        
        # Get latest price
        latest_price = client.get_latest_price("BTCUSDT")
        
        # Should match last close price
        expected_price = sample_data['close'].iloc[-1]
        assert abs(latest_price - expected_price) < 0.01
    
    def test_health_check(self, client, sample_data):
        """Test health check functionality."""
        # Store some data
        client.store_candles(sample_data, "BTCUSDT")
        
        # Run health check
        health = client.health_check()
        
        # Check health report structure
        assert 'total_candles' in health
        assert 'symbols' in health
        assert 'websocket_running' in health
        assert 'database_path' in health
        
        # Check values
        assert health['total_candles'] == len(sample_data)
        assert health['websocket_running'] == False  # Not started in test
        assert health['database_path'] == client.db_path
    
    def test_multiple_symbols(self, client, sample_data):
        """Test storing data for multiple symbols."""
        # Store data for different symbols
        client.store_candles(sample_data, "BTCUSDT")
        client.store_candles(sample_data, "ETHUSDT")
        
        # Retrieve data for each symbol
        btc_data = client.get_candles("BTCUSDT")
        eth_data = client.get_candles("ETHUSDT")
        
        # Both should have data
        assert len(btc_data) == len(sample_data)
        assert len(eth_data) == len(sample_data)
        
        # Health check should show both symbols
        health = client.health_check()
        assert health['total_candles'] == len(sample_data) * 2
    
    def test_limit_parameter(self, client, sample_data):
        """Test limit parameter in get_candles."""
        # Store data
        client.store_candles(sample_data, "BTCUSDT")
        
        # Test different limits
        df_10 = client.get_candles("BTCUSDT", limit=10)
        df_50 = client.get_candles("BTCUSDT", limit=50)
        
        assert len(df_10) == 10
        assert len(df_50) == 50
        
        # Should return most recent data
        assert df_10.index[-1] == sample_data.index[-1]
    
    def test_data_types(self, client, sample_data):
        """Test that data types are preserved correctly."""
        # Store data
        client.store_candles(sample_data, "BTCUSDT")
        
        # Retrieve data
        retrieved_df = client.get_candles("BTCUSDT")
        
        # Check data types
        for col in ['open', 'high', 'low', 'close', 'volume']:
            assert retrieved_df[col].dtype in ['float64', 'float32']
        
        # Check index is datetime
        assert isinstance(retrieved_df.index, pd.DatetimeIndex)


@pytest.fixture
def mock_api_response():
    """Mock API response for testing."""
    return [
        [1640995200000, "50000.00", "50100.00", "49900.00", "50050.00", "100.00",
         1640998799999, "5005000.00", 1000, "50.00", "2502500.00", "0"],
        [1640998800000, "50050.00", "50150.00", "49950.00", "50100.00", "110.00",
         1641002399999, "5511000.00", 1100, "55.00", "2755500.00", "0"]
    ]


def test_fetch_historical_klines_data_structure(mock_api_response):
    """Test that API response is properly structured into DataFrame."""
    # This would require mocking the requests.get call
    # For now, we'll test the data structure transformation
    
    import pandas as pd
    
    # Simulate the DataFrame creation from API response
    df = pd.DataFrame(mock_api_response, columns=[
        "open_time", "open", "high", "low", "close", "volume", 
        "close_time", "quote_asset_volume", "number_of_trades", 
        "taker_buy_base", "taker_buy_quote", "ignore"
    ])
    
    # Convert data types as done in the actual method
    df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
    df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = df[col].astype(float)
    
    df.set_index('open_time', inplace=True)
    
    # Verify structure
    assert len(df) == 2
    assert list(df.columns[:5]) == ['open', 'high', 'low', 'close', 'volume']
    assert isinstance(df.index, pd.DatetimeIndex)
    assert df['close'].dtype == float


if __name__ == "__main__":
    pytest.main([__file__])
