"""
Parameter optimization using Optuna for strategy hyperparameter tuning.
Implements grid search, random search, and Bayesian optimization.
"""

import optuna
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Callable, Optional
import logging
from datetime import datetime
import json
import sqlite3
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress Optuna logs
optuna.logging.set_verbosity(optuna.logging.WARNING)


class ParameterOptimizer:
    """
    Advanced parameter optimization using Optuna.
    Supports multiple optimization objectives and constraints.
    """
    
    def __init__(self, 
                 initial_cash: float = 10000,
                 commission: float = 0.001,
                 db_path: str = "optimization_results.db"):
        self.initial_cash = initial_cash
        self.commission = commission
        self.db_path = db_path
        self.runner = BacktestRunner(initial_cash, commission)
        self._init_database()
        
    def _init_database(self):
        """Initialize database for storing optimization results."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS optimization_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    study_name TEXT NOT NULL,
                    trial_number INTEGER NOT NULL,
                    strategy_name TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    objective_value REAL NOT NULL,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    total_trades INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
    def optimize_ma_rsi_strategy(self, 
                                data: pd.DataFrame,
                                n_trials: int = 100,
                                objective: str = 'sharpe_ratio',
                                timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Optimize MA+RSI strategy parameters.
        
        Args:
            data: Historical price data
            n_trials: Number of optimization trials
            objective: Optimization objective ('sharpe_ratio', 'total_return', 'profit_factor')
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with best parameters and results
        """
        study_name = f"ma_rsi_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        def objective_function(trial):
            # Define parameter search space
            params = {
                'ma_short': trial.suggest_int('ma_short', 5, 20),
                'ma_long': trial.suggest_int('ma_long', 30, 100),
                'rsi_period': trial.suggest_int('rsi_period', 10, 30),
                'rsi_overbought': trial.suggest_int('rsi_overbought', 65, 80),
                'rsi_oversold': trial.suggest_int('rsi_oversold', 20, 35),
                'atr_stop_mult': trial.suggest_float('atr_stop_mult', 1.0, 3.0),
                'atr_target_mult': trial.suggest_float('atr_target_mult', 2.0, 5.0),
                'risk_per_trade': trial.suggest_float('risk_per_trade', 0.005, 0.03),
                'printlog': False
            }
            
            # Ensure ma_short < ma_long
            if params['ma_short'] >= params['ma_long']:
                return -999  # Invalid parameter combination
                
            # Ensure rsi_oversold < rsi_overbought
            if params['rsi_oversold'] >= params['rsi_overbought']:
                return -999
            
            try:
                # Run backtest
                result = self.runner.run_backtest(MARSIStrategy, data, params)
                
                # Store result in database
                self._store_trial_result(study_name, trial.number, 'MARSIStrategy', params, result)
                
                # Return objective value
                if objective == 'sharpe_ratio':
                    return result['metrics'].get('sharpe_ratio', -999)
                elif objective == 'total_return':
                    return result['total_return']
                elif objective == 'profit_factor':
                    return result['metrics'].get('profit_factor', 0)
                elif objective == 'win_rate':
                    return result['metrics'].get('win_rate', 0)
                else:
                    return result['metrics'].get('sharpe_ratio', -999)
                    
            except Exception as e:
                logger.warning(f"Trial {trial.number} failed: {e}")
                return -999
        
        # Create and run study
        study = optuna.create_study(
            direction='maximize',
            study_name=study_name,
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        study.optimize(objective_function, n_trials=n_trials, timeout=timeout)
        
        # Get best results
        best_params = study.best_params
        best_value = study.best_value
        
        # Run final backtest with best parameters
        final_result = self.runner.run_backtest(MARSIStrategy, data, best_params)
        
        return {
            'study_name': study_name,
            'best_params': best_params,
            'best_value': best_value,
            'objective': objective,
            'n_trials': n_trials,
            'final_result': final_result,
            'study': study
        }
        
    def optimize_supertrend_strategy(self,
                                   data: pd.DataFrame,
                                   n_trials: int = 50,
                                   objective: str = 'sharpe_ratio') -> Dict[str, Any]:
        """Optimize SuperTrend strategy parameters."""
        study_name = f"supertrend_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        def objective_function(trial):
            params = {
                'atr_period': trial.suggest_int('atr_period', 5, 20),
                'atr_multiplier': trial.suggest_float('atr_multiplier', 1.5, 5.0),
                'risk_per_trade': trial.suggest_float('risk_per_trade', 0.005, 0.03),
                'printlog': False
            }
            
            try:
                result = self.runner.run_backtest(SuperTrendStrategy, data, params)
                self._store_trial_result(study_name, trial.number, 'SuperTrendStrategy', params, result)
                
                if objective == 'sharpe_ratio':
                    return result['metrics'].get('sharpe_ratio', -999)
                elif objective == 'total_return':
                    return result['total_return']
                else:
                    return result['metrics'].get('sharpe_ratio', -999)
                    
            except Exception as e:
                logger.warning(f"Trial {trial.number} failed: {e}")
                return -999
        
        study = optuna.create_study(direction='maximize', study_name=study_name)
        study.optimize(objective_function, n_trials=n_trials)
        
        best_params = study.best_params
        final_result = self.runner.run_backtest(SuperTrendStrategy, data, best_params)
        
        return {
            'study_name': study_name,
            'best_params': best_params,
            'best_value': study.best_value,
            'objective': objective,
            'n_trials': n_trials,
            'final_result': final_result,
            'study': study
        }
    
    def _store_trial_result(self, study_name: str, trial_number: int, 
                          strategy_name: str, params: Dict, result: Dict):
        """Store trial result in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO optimization_results 
                    (study_name, trial_number, strategy_name, parameters, 
                     objective_value, total_return, sharpe_ratio, max_drawdown, 
                     win_rate, total_trades)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    study_name, trial_number, strategy_name, json.dumps(params),
                    result['metrics'].get('sharpe_ratio', 0),
                    result['total_return'],
                    result['metrics'].get('sharpe_ratio', 0),
                    result['metrics'].get('max_drawdown', 0),
                    result['metrics'].get('win_rate', 0),
                    result['metrics'].get('total_trades', 0)
                ))
        except Exception as e:
            logger.warning(f"Failed to store trial result: {e}")
    
    def multi_objective_optimization(self,
                                   data: pd.DataFrame,
                                   n_trials: int = 100) -> Dict[str, Any]:
        """
        Multi-objective optimization balancing return and risk.
        """
        study_name = f"multi_objective_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        def objective_function(trial):
            params = {
                'ma_short': trial.suggest_int('ma_short', 5, 20),
                'ma_long': trial.suggest_int('ma_long', 30, 100),
                'rsi_period': trial.suggest_int('rsi_period', 10, 30),
                'rsi_overbought': trial.suggest_int('rsi_overbought', 65, 80),
                'rsi_oversold': trial.suggest_int('rsi_oversold', 20, 35),
                'atr_stop_mult': trial.suggest_float('atr_stop_mult', 1.0, 3.0),
                'risk_per_trade': trial.suggest_float('risk_per_trade', 0.005, 0.03),
                'printlog': False
            }
            
            if params['ma_short'] >= params['ma_long']:
                return -999, 999  # Invalid combination
                
            if params['rsi_oversold'] >= params['rsi_overbought']:
                return -999, 999
            
            try:
                result = self.runner.run_backtest(MARSIStrategy, data, params)
                
                # Return multiple objectives: maximize return, minimize drawdown
                total_return = result['total_return']
                max_drawdown = abs(result['metrics'].get('max_drawdown', 0))
                
                return total_return, max_drawdown
                
            except Exception as e:
                logger.warning(f"Multi-objective trial failed: {e}")
                return -999, 999
        
        # Multi-objective study
        study = optuna.create_study(
            directions=['maximize', 'minimize'],  # maximize return, minimize drawdown
            study_name=study_name
        )
        
        study.optimize(objective_function, n_trials=n_trials)
        
        # Get Pareto front
        pareto_front = []
        for trial in study.best_trials:
            params = trial.params
            values = trial.values
            pareto_front.append({
                'params': params,
                'total_return': values[0],
                'max_drawdown': values[1]
            })
        
        return {
            'study_name': study_name,
            'pareto_front': pareto_front,
            'n_trials': n_trials,
            'study': study
        }
    
    def get_optimization_history(self, study_name: str) -> pd.DataFrame:
        """Get optimization history from database."""
        with sqlite3.connect(self.db_path) as conn:
            query = """
                SELECT * FROM optimization_results 
                WHERE study_name = ?
                ORDER BY trial_number
            """
            return pd.read_sql_query(query, conn, params=(study_name,))
    
    def compare_optimized_strategies(self, data: pd.DataFrame) -> pd.DataFrame:
        """Compare multiple optimized strategies."""
        results = []
        
        # Optimize MA+RSI for different objectives
        for objective in ['sharpe_ratio', 'total_return', 'profit_factor']:
            logger.info(f"Optimizing MA+RSI for {objective}...")
            result = self.optimize_ma_rsi_strategy(
                data, n_trials=50, objective=objective
            )
            
            results.append({
                'Strategy': f"MA+RSI ({objective})",
                'Best Params': str(result['best_params']),
                'Objective Value': result['best_value'],
                'Total Return (%)': result['final_result']['total_return'],
                'Sharpe Ratio': result['final_result']['metrics'].get('sharpe_ratio', 0),
                'Max Drawdown (%)': result['final_result']['metrics'].get('max_drawdown', 0),
                'Win Rate (%)': result['final_result']['metrics'].get('win_rate', 0),
                'Total Trades': result['final_result']['metrics'].get('total_trades', 0)
            })
        
        # Optimize SuperTrend
        logger.info("Optimizing SuperTrend...")
        st_result = self.optimize_supertrend_strategy(data, n_trials=30)
        
        results.append({
            'Strategy': "SuperTrend (optimized)",
            'Best Params': str(st_result['best_params']),
            'Objective Value': st_result['best_value'],
            'Total Return (%)': st_result['final_result']['total_return'],
            'Sharpe Ratio': st_result['final_result']['metrics'].get('sharpe_ratio', 0),
            'Max Drawdown (%)': st_result['final_result']['metrics'].get('max_drawdown', 0),
            'Win Rate (%)': st_result['final_result']['metrics'].get('win_rate', 0),
            'Total Trades': st_result['final_result']['metrics'].get('total_trades', 0)
        })
        
        return pd.DataFrame(results)


def main():
    """Example usage of ParameterOptimizer."""
    from data.client import BinanceDataClient
    
    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=1000)
    
    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=1000)
    
    print(f"Loaded {len(df)} candles for optimization")
    
    # Initialize optimizer
    optimizer = ParameterOptimizer()
    
    # Optimize MA+RSI strategy
    print("\n🎯 Optimizing MA+RSI Strategy...")
    result = optimizer.optimize_ma_rsi_strategy(
        df, n_trials=20, objective='sharpe_ratio'
    )
    
    print(f"\n📊 Optimization Results:")
    print(f"Best Parameters: {result['best_params']}")
    print(f"Best Sharpe Ratio: {result['best_value']:.3f}")
    print(f"Total Return: {result['final_result']['total_return']:.2f}%")
    print(f"Max Drawdown: {result['final_result']['metrics']['max_drawdown']:.2f}%")
    print(f"Win Rate: {result['final_result']['metrics']['win_rate']:.1f}%")
    
    # Compare strategies
    print("\n🔄 Comparing Optimized Strategies...")
    comparison = optimizer.compare_optimized_strategies(df)
    print(comparison.to_string(index=False))


if __name__ == "__main__":
    main()
