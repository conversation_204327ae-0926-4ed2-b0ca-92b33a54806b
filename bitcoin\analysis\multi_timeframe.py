"""
Multi-timeframe analysis for comprehensive market view.
Analyzes trends across different time horizons (1h, 4h, 1d).
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.client import BinanceDataClient
from indicators.ta_wrappers import add_all_indicators, get_trading_signals


class MultiTimeframeAnalyzer:
    """
    Analyze market trends across multiple timeframes for better decision making.
    """

    def __init__(self, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        self.client = BinanceDataClient()
        self.timeframes = {
            '1h': {'interval': '1h', 'limit': 168},   # 1 week
            '4h': {'interval': '4h', 'limit': 168},   # 4 weeks
            '1d': {'interval': '1d', 'limit': 90}     # 3 months
        }

    def fetch_multi_timeframe_data(self) -> Dict[str, pd.DataFrame]:
        """Fetch data for all timeframes."""
        data = {}

        for tf_name, tf_config in self.timeframes.items():
            try:
                df = self.client.fetch_historical_klines(
                    self.symbol,
                    tf_config['interval'],
                    tf_config['limit']
                )

                if not df.empty:
                    # Add indicators
                    df = add_all_indicators(df)
                    data[tf_name] = df
                    print(f"✅ Loaded {len(df)} candles for {tf_name}")
                else:
                    print(f"❌ No data for {tf_name}")

            except Exception as e:
                print(f"❌ Error loading {tf_name}: {e}")

        return data

    def analyze_trend_alignment(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Analyze trend alignment across timeframes.
        Strong signals occur when multiple timeframes align.
        """
        analysis = {
            'timestamp': pd.Timestamp.now(),
            'symbol': self.symbol,
            'timeframes': {},
            'alignment': {},
            'signals': {}
        }

        # Analyze each timeframe
        for tf, df in data.items():
            if df.empty:
                continue

            latest = df.iloc[-1]
            try:
                signals = get_trading_signals(df)
                latest_signals = signals.iloc[-1] if len(signals) > 0 else None
            except Exception as e:
                print(f"Warning: Could not generate signals for {tf}: {e}")
                latest_signals = None

            # Trend analysis
            trend_score = 0
            trend_strength = "Neutral"

            # MA trend
            if 'MA_Short' in df.columns and 'MA_Long' in df.columns:
                ma_short = latest.get('MA_Short', 0)
                ma_long = latest.get('MA_Long', 0)
                if not pd.isna(ma_short) and not pd.isna(ma_long):
                    if ma_short > ma_long:
                        trend_score += 1
                    else:
                        trend_score -= 1

            # Price vs VWAP
            if 'VWAP' in df.columns:
                vwap = latest.get('VWAP', 0)
                close = latest.get('close', 0)
                if not pd.isna(vwap) and not pd.isna(close):
                    if close > vwap:
                        trend_score += 1
                    else:
                        trend_score -= 1

            # SuperTrend
            if 'SuperTrend_Direction' in df.columns:
                st_direction = latest.get('SuperTrend_Direction', 0)
                if not pd.isna(st_direction):
                    if st_direction == -1:  # Bullish
                        trend_score += 1
                    else:
                        trend_score -= 1

            # RSI momentum
            if 'RSI' in df.columns:
                rsi = latest.get('RSI', 50)
                if not pd.isna(rsi):
                    if rsi > 50:
                        trend_score += 0.5
                    else:
                        trend_score -= 0.5

            # Determine trend strength
            if trend_score >= 2:
                trend_strength = "Strong Bullish"
            elif trend_score >= 1:
                trend_strength = "Bullish"
            elif trend_score <= -2:
                trend_strength = "Strong Bearish"
            elif trend_score <= -1:
                trend_strength = "Bearish"

            analysis['timeframes'][tf] = {
                'price': latest['close'],
                'trend_score': trend_score,
                'trend_strength': trend_strength,
                'rsi': latest.get('RSI', 0),
                'ma_signal': latest_signals.get('MA_Signal', 0) if latest_signals is not None else 0,
                'supertrend_direction': latest.get('SuperTrend_Direction', 0),
                'volume': latest['volume']
            }

        # Calculate alignment
        analysis['alignment'] = self._calculate_alignment(analysis['timeframes'])

        # Generate composite signals
        analysis['signals'] = self._generate_composite_signals(analysis['timeframes'], analysis['alignment'])

        return analysis

    def _calculate_alignment(self, timeframes: Dict) -> Dict[str, Any]:
        """Calculate trend alignment across timeframes."""
        if not timeframes:
            return {}

        # Count bullish/bearish timeframes
        bullish_count = 0
        bearish_count = 0
        total_count = len(timeframes)

        trend_scores = []

        for tf, data in timeframes.items():
            trend_score = data['trend_score']
            trend_scores.append(trend_score)

            if trend_score > 0:
                bullish_count += 1
            elif trend_score < 0:
                bearish_count += 1

        # Calculate alignment strength
        alignment_ratio = max(bullish_count, bearish_count) / total_count
        avg_trend_score = np.mean(trend_scores)

        # Determine overall trend
        if bullish_count > bearish_count:
            overall_trend = "Bullish"
        elif bearish_count > bullish_count:
            overall_trend = "Bearish"
        else:
            overall_trend = "Neutral"

        # Alignment strength
        if alignment_ratio >= 0.8:
            alignment_strength = "Strong"
        elif alignment_ratio >= 0.6:
            alignment_strength = "Moderate"
        else:
            alignment_strength = "Weak"

        return {
            'overall_trend': overall_trend,
            'alignment_strength': alignment_strength,
            'alignment_ratio': alignment_ratio,
            'bullish_timeframes': bullish_count,
            'bearish_timeframes': bearish_count,
            'neutral_timeframes': total_count - bullish_count - bearish_count,
            'avg_trend_score': avg_trend_score
        }

    def _generate_composite_signals(self, timeframes: Dict, alignment: Dict) -> Dict[str, Any]:
        """Generate composite trading signals based on multi-timeframe analysis."""
        signals = {
            'primary_signal': 'HOLD',
            'confidence': 0,
            'entry_conditions': [],
            'exit_conditions': [],
            'risk_level': 'Medium'
        }

        if not timeframes or not alignment:
            return signals

        overall_trend = alignment['overall_trend']
        alignment_strength = alignment['alignment_strength']
        alignment_ratio = alignment['alignment_ratio']

        # Generate primary signal
        if overall_trend == "Bullish" and alignment_strength in ["Strong", "Moderate"]:
            signals['primary_signal'] = 'BUY'
            signals['confidence'] = int(alignment_ratio * 100)

            # Entry conditions
            signals['entry_conditions'] = [
                f"Multi-timeframe bullish alignment ({alignment_ratio:.1%})",
                f"Trend strength: {alignment_strength}"
            ]

            # Check for additional confirmations
            for tf, data in timeframes.items():
                if data['rsi'] < 70:  # Not overbought
                    signals['entry_conditions'].append(f"{tf} RSI not overbought ({data['rsi']:.1f})")
                if data['ma_signal'] == 1:
                    signals['entry_conditions'].append(f"{tf} MA crossover bullish")

        elif overall_trend == "Bearish" and alignment_strength in ["Strong", "Moderate"]:
            signals['primary_signal'] = 'SELL'
            signals['confidence'] = int(alignment_ratio * 100)

            signals['entry_conditions'] = [
                f"Multi-timeframe bearish alignment ({alignment_ratio:.1%})",
                f"Trend strength: {alignment_strength}"
            ]

            for tf, data in timeframes.items():
                if data['rsi'] > 30:  # Not oversold
                    signals['entry_conditions'].append(f"{tf} RSI not oversold ({data['rsi']:.1f})")
                if data['ma_signal'] == -1:
                    signals['entry_conditions'].append(f"{tf} MA crossover bearish")

        # Risk level assessment
        if alignment_strength == "Strong":
            signals['risk_level'] = 'Low'
        elif alignment_strength == "Weak":
            signals['risk_level'] = 'High'

        # Exit conditions
        if signals['primary_signal'] == 'BUY':
            signals['exit_conditions'] = [
                "Higher timeframe trend reversal",
                "RSI overbought on multiple timeframes",
                "MA crossover bearish on 4h or 1d"
            ]
        elif signals['primary_signal'] == 'SELL':
            signals['exit_conditions'] = [
                "Higher timeframe trend reversal",
                "RSI oversold on multiple timeframes",
                "MA crossover bullish on 4h or 1d"
            ]

        return signals

    def get_timeframe_summary(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Get summary table of all timeframes."""
        summary_data = []

        for tf, df in data.items():
            if df.empty:
                continue

            latest = df.iloc[-1]
            try:
                signals = get_trading_signals(df)
                latest_signals = signals.iloc[-1] if len(signals) > 0 else None
            except Exception:
                latest_signals = None

            # Calculate price change
            if len(df) > 1:
                price_change = ((latest['close'] - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100
            else:
                price_change = 0

            summary_data.append({
                'Timeframe': tf,
                'Price': f"${latest['close']:,.2f}",
                'Change (%)': f"{price_change:+.2f}%",
                'RSI': f"{latest.get('RSI', 0):.1f}",
                'MA Signal': 'Buy' if latest_signals and latest_signals.get('MA_Signal') == 1 else 'Sell' if latest_signals and latest_signals.get('MA_Signal') == -1 else 'Hold',
                'SuperTrend': 'Bullish' if latest.get('SuperTrend_Direction') == -1 else 'Bearish' if latest.get('SuperTrend_Direction') == 1 else 'Neutral',
                'Volume': f"{latest['volume']:,.0f}"
            })

        return pd.DataFrame(summary_data)

    def generate_report(self) -> str:
        """Generate comprehensive multi-timeframe analysis report."""
        print("🔄 Fetching multi-timeframe data...")
        data = self.fetch_multi_timeframe_data()

        if not data:
            return "❌ No data available for analysis"

        print("📊 Analyzing trends...")
        analysis = self.analyze_trend_alignment(data)

        # Generate report
        report = f"""
🎯 MULTI-TIMEFRAME ANALYSIS REPORT
{'='*50}

Symbol: {self.symbol}
Timestamp: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}

📈 TREND ALIGNMENT
{'-'*30}
Overall Trend: {analysis['alignment']['overall_trend']}
Alignment Strength: {analysis['alignment']['alignment_strength']}
Alignment Ratio: {analysis['alignment']['alignment_ratio']:.1%}

Bullish Timeframes: {analysis['alignment']['bullish_timeframes']}
Bearish Timeframes: {analysis['alignment']['bearish_timeframes']}
Neutral Timeframes: {analysis['alignment']['neutral_timeframes']}

🎯 TRADING SIGNALS
{'-'*30}
Primary Signal: {analysis['signals']['primary_signal']}
Confidence: {analysis['signals']['confidence']}%
Risk Level: {analysis['signals']['risk_level']}

Entry Conditions:
"""

        for condition in analysis['signals']['entry_conditions']:
            report += f"  • {condition}\n"

        report += f"\nExit Conditions:\n"
        for condition in analysis['signals']['exit_conditions']:
            report += f"  • {condition}\n"

        report += f"\n📊 TIMEFRAME BREAKDOWN\n{'-'*30}\n"

        for tf, tf_data in analysis['timeframes'].items():
            report += f"""
{tf.upper()} Timeframe:
  Price: ${tf_data['price']:,.2f}
  Trend: {tf_data['trend_strength']} (Score: {tf_data['trend_score']})
  RSI: {tf_data['rsi']:.1f}
  MA Signal: {'Buy' if tf_data['ma_signal'] == 1 else 'Sell' if tf_data['ma_signal'] == -1 else 'Hold'}
  SuperTrend: {'Bullish' if tf_data['supertrend_direction'] == -1 else 'Bearish' if tf_data['supertrend_direction'] == 1 else 'Neutral'}
"""

        # Summary table
        summary_df = self.get_timeframe_summary(data)
        report += f"\n📋 SUMMARY TABLE\n{'-'*30}\n"
        report += summary_df.to_string(index=False)

        report += f"\n\n⚠️ DISCLAIMER\n{'-'*30}\n"
        report += "This analysis is for educational purposes only.\n"
        report += "Not financial advice. Always do your own research.\n"

        return report


def main():
    """Example usage of MultiTimeframeAnalyzer."""
    analyzer = MultiTimeframeAnalyzer("BTCUSDT")

    try:
        report = analyzer.generate_report()
        print(report)

        # Save report to file
        with open("multi_timeframe_analysis.txt", "w") as f:
            f.write(report)
        print("\n📄 Report saved to: multi_timeframe_analysis.txt")

    except Exception as e:
        print(f"❌ Error generating analysis: {e}")


if __name__ == "__main__":
    main()
