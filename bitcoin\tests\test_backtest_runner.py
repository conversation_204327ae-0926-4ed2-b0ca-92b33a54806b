"""
Tests for backtest runner functionality.
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy


class TestBacktestRunner:
    """Test cases for BacktestRunner."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for backtesting."""
        np.random.seed(42)
        n = 200  # Enough data for meaningful backtest
        dates = pd.date_range('2024-01-01', periods=n, freq='H')
        
        # Generate realistic price data with trend
        base_price = 50000
        price_changes = np.random.normal(0.001, 0.02, n)  # 0.1% drift, 2% volatility
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1000))  # Prevent negative prices
        
        prices = np.array(prices)
        
        data = {
            'open': prices,
            'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n))),
            'close': prices,
            'volume': np.random.randint(100, 1000, n)
        }
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    @pytest.fixture
    def runner(self):
        """Create BacktestRunner instance."""
        return BacktestRunner(initial_cash=10000, commission=0.001)
    
    def test_runner_initialization(self, runner):
        """Test BacktestRunner initialization."""
        assert runner.initial_cash == 10000
        assert runner.commission == 0.001
        assert isinstance(runner.results, dict)
    
    def test_prepare_data(self, runner, sample_data):
        """Test data preparation for Backtrader."""
        bt_data = runner.prepare_data(sample_data)
        
        # Should create valid Backtrader data feed
        assert bt_data is not None
        
        # Check that required columns exist in original data
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            assert col in sample_data.columns
    
    def test_prepare_data_validation(self, runner):
        """Test data preparation validation."""
        # Test with missing columns
        invalid_data = pd.DataFrame({
            'open': [1, 2, 3],
            'high': [1.1, 2.1, 3.1],
            # Missing 'low', 'close', 'volume'
        })
        
        with pytest.raises(ValueError, match="Missing required column"):
            runner.prepare_data(invalid_data)
        
        # Test with non-datetime index
        invalid_data = pd.DataFrame({
            'open': [1, 2, 3],
            'high': [1.1, 2.1, 3.1],
            'low': [0.9, 1.9, 2.9],
            'close': [1.05, 2.05, 3.05],
            'volume': [100, 200, 300]
        })
        
        with pytest.raises(ValueError, match="DataFrame must have datetime index"):
            runner.prepare_data(invalid_data)
    
    def test_run_backtest_ma_rsi(self, runner, sample_data):
        """Test running MA+RSI strategy backtest."""
        strategy_params = {
            'ma_short': 5,
            'ma_long': 20,
            'rsi_period': 14,
            'printlog': False
        }
        
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            strategy_params
        )
        
        # Check result structure
        assert 'strategy' in result
        assert 'initial_cash' in result
        assert 'final_value' in result
        assert 'total_return' in result
        assert 'metrics' in result
        assert 'data_period' in result
        assert 'strategy_params' in result
        
        # Check values
        assert result['strategy'] == 'MARSIStrategy'
        assert result['initial_cash'] == 10000
        assert result['final_value'] > 0
        assert isinstance(result['total_return'], float)
        assert isinstance(result['metrics'], dict)
    
    def test_run_backtest_supertrend(self, runner, sample_data):
        """Test running SuperTrend strategy backtest."""
        strategy_params = {
            'atr_period': 10,
            'atr_multiplier': 3.0,
            'printlog': False
        }
        
        result = runner.run_backtest(
            SuperTrendStrategy,
            sample_data,
            strategy_params
        )
        
        # Check basic result structure
        assert result['strategy'] == 'SuperTrendStrategy'
        assert 'metrics' in result
        assert result['final_value'] > 0
    
    def test_extract_metrics(self, runner, sample_data):
        """Test metrics extraction from backtest results."""
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            {'printlog': False}
        )
        
        metrics = result['metrics']
        
        # Check that key metrics are present
        expected_metrics = [
            'sharpe_ratio', 'max_drawdown', 'total_trades',
            'won_trades', 'lost_trades', 'win_rate'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
        
        # Check metric ranges
        assert 0 <= metrics['win_rate'] <= 100
        assert metrics['max_drawdown'] <= 0  # Should be negative
        assert metrics['total_trades'] >= 0
        assert metrics['won_trades'] >= 0
        assert metrics['lost_trades'] >= 0
        assert metrics['won_trades'] + metrics['lost_trades'] <= metrics['total_trades']
    
    def test_insufficient_data(self, runner):
        """Test handling of insufficient data."""
        # Create very small dataset
        small_data = pd.DataFrame({
            'open': [50000, 50100],
            'high': [50200, 50300],
            'low': [49800, 49900],
            'close': [50050, 50150],
            'volume': [100, 200]
        }, index=pd.date_range('2024-01-01', periods=2, freq='H'))
        
        with pytest.raises(ValueError, match="Insufficient data"):
            runner.run_backtest(MARSIStrategy, small_data)
    
    def test_date_filtering(self, runner, sample_data):
        """Test date range filtering."""
        # Test with date range
        start_date = sample_data.index[50].strftime('%Y-%m-%d')
        end_date = sample_data.index[150].strftime('%Y-%m-%d')
        
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            strategy_params={'printlog': False},
            start_date=start_date,
            end_date=end_date
        )
        
        # Should complete successfully
        assert 'total_return' in result
        
        # Data period should reflect the filtering
        assert result['data_period']['start'] >= start_date
        assert result['data_period']['end'] <= end_date
    
    def test_compare_strategies(self, runner, sample_data):
        """Test strategy comparison functionality."""
        strategies = [
            (MARSIStrategy, {'ma_short': 5, 'ma_long': 20, 'printlog': False}),
            (MARSIStrategy, {'ma_short': 10, 'ma_long': 50, 'printlog': False}),
        ]
        
        comparison_df = runner.compare_strategies(strategies, sample_data)
        
        # Check result structure
        assert isinstance(comparison_df, pd.DataFrame)
        assert len(comparison_df) == 2  # Two strategies
        
        # Check columns
        expected_columns = [
            'Strategy', 'Total Return (%)', 'Sharpe Ratio',
            'Max Drawdown (%)', 'Win Rate (%)', 'Total Trades'
        ]
        
        for col in expected_columns:
            assert col in comparison_df.columns, f"Missing column: {col}"
        
        # Check that all strategies are MARSIStrategy
        assert (comparison_df['Strategy'] == 'MARSIStrategy').all()
    
    def test_generate_report(self, runner, sample_data):
        """Test HTML report generation."""
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            {'printlog': False}
        )
        
        # Generate report
        html_content = runner.generate_report(result)
        
        # Check that HTML content is generated
        assert isinstance(html_content, str)
        assert len(html_content) > 1000  # Should be substantial
        assert '<html>' in html_content
        assert '</html>' in html_content
        assert 'Backtest Report' in html_content
        
        # Check that key metrics are in the report
        assert str(result['total_return']) in html_content
        assert result['strategy'] in html_content
    
    def test_generate_report_to_file(self, runner, sample_data):
        """Test HTML report generation to file."""
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            {'printlog': False}
        )
        
        # Generate report to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            temp_path = f.name
        
        try:
            html_content = runner.generate_report(result, temp_path)
            
            # Check that file was created
            assert os.path.exists(temp_path)
            
            # Check file content
            with open(temp_path, 'r') as f:
                file_content = f.read()
            
            assert file_content == html_content
            assert len(file_content) > 1000
            
        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    def test_error_handling(self, runner, sample_data):
        """Test error handling in backtesting."""
        # Test with invalid strategy parameters
        invalid_params = {
            'ma_short': 100,  # Longer than data
            'ma_long': 200,   # Much longer than data
            'printlog': False
        }
        
        # Should handle gracefully (might have poor performance but shouldn't crash)
        try:
            result = runner.run_backtest(MARSIStrategy, sample_data, invalid_params)
            # If it completes, check that result is valid
            assert 'total_return' in result
        except Exception as e:
            # If it fails, should be a reasonable error
            assert isinstance(e, (ValueError, RuntimeError))
    
    def test_performance_metrics_calculation(self, runner, sample_data):
        """Test that performance metrics are calculated correctly."""
        result = runner.run_backtest(
            MARSIStrategy,
            sample_data,
            {'printlog': False}
        )
        
        metrics = result['metrics']
        
        # Test metric relationships
        if metrics['total_trades'] > 0:
            assert metrics['won_trades'] + metrics['lost_trades'] <= metrics['total_trades']
            
            if metrics['total_trades'] > 0:
                calculated_win_rate = (metrics['won_trades'] / metrics['total_trades']) * 100
                assert abs(calculated_win_rate - metrics['win_rate']) < 0.1
        
        # Sharpe ratio should be reasonable (between -5 and 5 for most strategies)
        assert -10 <= metrics['sharpe_ratio'] <= 10
        
        # Max drawdown should be negative or zero
        assert metrics['max_drawdown'] <= 0
    
    def test_commission_impact(self, sample_data):
        """Test impact of different commission rates."""
        # Test with no commission
        runner_no_comm = BacktestRunner(initial_cash=10000, commission=0.0)
        result_no_comm = runner_no_comm.run_backtest(
            MARSIStrategy, sample_data, {'printlog': False}
        )
        
        # Test with high commission
        runner_high_comm = BacktestRunner(initial_cash=10000, commission=0.01)  # 1%
        result_high_comm = runner_high_comm.run_backtest(
            MARSIStrategy, sample_data, {'printlog': False}
        )
        
        # Higher commission should generally result in lower returns
        # (unless there are very few trades)
        if (result_no_comm['metrics']['total_trades'] > 5 and 
            result_high_comm['metrics']['total_trades'] > 5):
            assert result_no_comm['total_return'] >= result_high_comm['total_return']


if __name__ == "__main__":
    pytest.main([__file__])
