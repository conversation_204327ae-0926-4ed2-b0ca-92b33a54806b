"""
Advanced machine learning models for trading predictions.
Includes ensemble methods, neural networks, and time series models.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
import joblib

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost not available. Install with: pip install xgboost")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM not available. Install with: pip install lightgbm")


class TradingMLModels:
    """
    Advanced machine learning models for trading signal prediction.
    """

    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}

    def create_base_models(self) -> Dict[str, Any]:
        """Create base models for ensemble."""
        models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=self.random_state,
                n_jobs=-1
            ),

            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=self.random_state
            ),

            'logistic_regression': LogisticRegression(
                random_state=self.random_state,
                max_iter=1000
            ),

            'svm': SVC(
                kernel='rbf',
                probability=True,
                random_state=self.random_state
            ),

            'neural_network': MLPClassifier(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                random_state=self.random_state,
                max_iter=500
            )
        }

        # Add XGBoost if available
        if XGBOOST_AVAILABLE:
            models['xgboost'] = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=self.random_state,
                eval_metric='logloss'
            )

        # Add LightGBM if available
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=self.random_state,
                verbose=-1
            )

        return models

    def create_ensemble_model(self, base_models: Dict[str, Any]) -> VotingClassifier:
        """Create ensemble model from base models."""
        estimators = [(name, model) for name, model in base_models.items()]

        ensemble = VotingClassifier(
            estimators=estimators,
            voting='soft',  # Use probabilities
            n_jobs=-1
        )

        return ensemble

    def optimize_hyperparameters(self,
                                model_name: str,
                                X: pd.DataFrame,
                                y: pd.Series,
                                cv_folds: int = 5) -> Dict[str, Any]:
        """Optimize hyperparameters using GridSearchCV with time series split."""

        # Define parameter grids
        param_grids = {
            'random_forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            },

            'gradient_boosting': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.05, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'subsample': [0.8, 0.9, 1.0]
            },

            'logistic_regression': {
                'C': [0.1, 1.0, 10.0, 100.0],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
        }

        if model_name not in param_grids:
            print(f"No parameter grid defined for {model_name}")
            return {}

        # Create base model
        base_models = self.create_base_models()
        if model_name not in base_models:
            print(f"Model {model_name} not available")
            return {}

        model = base_models[model_name]

        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=cv_folds)

        # Grid search
        grid_search = GridSearchCV(
            model,
            param_grids[model_name],
            cv=tscv,
            scoring='roc_auc',
            n_jobs=-1,
            verbose=1
        )

        print(f"🔍 Optimizing {model_name} hyperparameters...")
        grid_search.fit(X, y)

        return {
            'best_params': grid_search.best_params_,
            'best_score': grid_search.best_score_,
            'best_model': grid_search.best_estimator_
        }

    def train_models(self,
                    X_train: pd.DataFrame,
                    y_train: pd.Series,
                    X_val: pd.DataFrame = None,
                    y_val: pd.Series = None,
                    optimize: bool = False) -> Dict[str, Any]:
        """Train all models and return results."""

        results = {}

        # Create base models
        base_models = self.create_base_models()

        print(f"🤖 Training {len(base_models)} models...")

        for name, model in base_models.items():
            print(f"Training {name}...")

            try:
                # Create pipeline with scaling
                if name in ['svm', 'neural_network', 'logistic_regression']:
                    scaler = StandardScaler()
                    pipeline = Pipeline([
                        ('scaler', scaler),
                        ('model', model)
                    ])
                else:
                    pipeline = Pipeline([
                        ('model', model)
                    ])

                # Optimize hyperparameters if requested
                if optimize and name in ['random_forest', 'gradient_boosting', 'logistic_regression']:
                    opt_result = self.optimize_hyperparameters(name, X_train, y_train)
                    if opt_result:
                        model = opt_result['best_model']
                        pipeline = Pipeline([
                            ('scaler', StandardScaler() if name in ['svm', 'neural_network', 'logistic_regression'] else 'passthrough'),
                            ('model', model)
                        ])

                # Train model
                pipeline.fit(X_train, y_train)

                # Predictions
                train_pred = pipeline.predict(X_train)
                train_proba = pipeline.predict_proba(X_train)[:, 1]

                # Validation predictions if available
                if X_val is not None and y_val is not None:
                    val_pred = pipeline.predict(X_val)
                    val_proba = pipeline.predict_proba(X_val)[:, 1]
                else:
                    val_pred = train_pred
                    val_proba = train_proba
                    y_val = y_train

                # Calculate metrics
                train_auc = roc_auc_score(y_train, train_proba)
                val_auc = roc_auc_score(y_val, val_proba)

                # Feature importance (if available)
                feature_importance = None
                if hasattr(model, 'feature_importances_'):
                    feature_importance = dict(zip(X_train.columns, model.feature_importances_))
                elif hasattr(model, 'coef_'):
                    feature_importance = dict(zip(X_train.columns, abs(model.coef_[0])))

                results[name] = {
                    'model': pipeline,
                    'train_auc': train_auc,
                    'val_auc': val_auc,
                    'train_predictions': train_pred,
                    'val_predictions': val_pred,
                    'train_probabilities': train_proba,
                    'val_probabilities': val_proba,
                    'feature_importance': feature_importance
                }

                print(f"✅ {name}: Train AUC={train_auc:.3f}, Val AUC={val_auc:.3f}")

            except Exception as e:
                print(f"❌ Error training {name}: {e}")
                continue

        # Create ensemble
        if len(results) >= 2:
            print("🎯 Creating ensemble model...")
            try:
                # Use best performing models for ensemble
                best_models = sorted(results.items(), key=lambda x: x[1]['val_auc'], reverse=True)[:5]
                ensemble_models = {name: result['model'].named_steps['model'] for name, result in best_models}

                ensemble = self.create_ensemble_model(ensemble_models)

                # Prepare data for ensemble (apply scaling if needed)
                X_train_scaled = X_train.copy()
                X_val_scaled = X_val.copy() if X_val is not None else X_train.copy()

                ensemble.fit(X_train_scaled, y_train)

                # Ensemble predictions
                ensemble_train_pred = ensemble.predict(X_train_scaled)
                ensemble_train_proba = ensemble.predict_proba(X_train_scaled)[:, 1]
                ensemble_val_pred = ensemble.predict(X_val_scaled)
                ensemble_val_proba = ensemble.predict_proba(X_val_scaled)[:, 1]

                ensemble_train_auc = roc_auc_score(y_train, ensemble_train_proba)
                ensemble_val_auc = roc_auc_score(y_val if y_val is not None else y_train, ensemble_val_proba)

                results['ensemble'] = {
                    'model': ensemble,
                    'train_auc': ensemble_train_auc,
                    'val_auc': ensemble_val_auc,
                    'train_predictions': ensemble_train_pred,
                    'val_predictions': ensemble_val_pred,
                    'train_probabilities': ensemble_train_proba,
                    'val_probabilities': ensemble_val_proba,
                    'feature_importance': None
                }

                print(f"✅ Ensemble: Train AUC={ensemble_train_auc:.3f}, Val AUC={ensemble_val_auc:.3f}")

            except Exception as e:
                print(f"❌ Error creating ensemble: {e}")

        return results

    def evaluate_models(self, results: Dict[str, Any], X_test: pd.DataFrame, y_test: pd.Series) -> pd.DataFrame:
        """Evaluate all models on test set."""

        evaluation_results = []

        for name, result in results.items():
            model = result['model']

            try:
                # Test predictions
                test_pred = model.predict(X_test)
                test_proba = model.predict_proba(X_test)[:, 1]

                # Calculate metrics
                test_auc = roc_auc_score(y_test, test_proba)
                accuracy = (test_pred == y_test).mean()

                # Precision, Recall, F1
                from sklearn.metrics import precision_score, recall_score, f1_score
                precision = precision_score(y_test, test_pred)
                recall = recall_score(y_test, test_pred)
                f1 = f1_score(y_test, test_pred)

                evaluation_results.append({
                    'Model': name,
                    'Test_AUC': test_auc,
                    'Accuracy': accuracy,
                    'Precision': precision,
                    'Recall': recall,
                    'F1_Score': f1,
                    'Train_AUC': result['train_auc'],
                    'Val_AUC': result['val_auc'],
                    'Overfit': result['train_auc'] - test_auc
                })

            except Exception as e:
                print(f"❌ Error evaluating {name}: {e}")
                continue

        return pd.DataFrame(evaluation_results).sort_values('Test_AUC', ascending=False)

    def save_models(self, results: Dict[str, Any], filepath: str):
        """Save trained models to disk."""
        models_to_save = {name: result['model'] for name, result in results.items()}
        joblib.dump(models_to_save, filepath)
        print(f"💾 Models saved to {filepath}")

    def load_models(self, filepath: str) -> Dict[str, Any]:
        """Load trained models from disk."""
        models = joblib.load(filepath)
        print(f"📂 Models loaded from {filepath}")
        return models

    def get_feature_importance_summary(self, results: Dict[str, Any]) -> pd.DataFrame:
        """Get feature importance summary across all models."""

        importance_data = []

        for name, result in results.items():
            if result['feature_importance'] is not None:
                for feature, importance in result['feature_importance'].items():
                    importance_data.append({
                        'Model': name,
                        'Feature': feature,
                        'Importance': importance
                    })

        if not importance_data:
            return pd.DataFrame()

        importance_df = pd.DataFrame(importance_data)

        # Calculate average importance across models
        avg_importance = importance_df.groupby('Feature')['Importance'].agg(['mean', 'std', 'count']).reset_index()
        avg_importance.columns = ['Feature', 'Mean_Importance', 'Std_Importance', 'Model_Count']
        avg_importance = avg_importance.sort_values('Mean_Importance', ascending=False)

        return avg_importance

    def time_series_cross_validation(self,
                                   X: pd.DataFrame,
                                   y: pd.Series,
                                   model_name: str = 'random_forest',
                                   n_splits: int = 5,
                                   test_size: int = 100) -> Dict[str, Any]:
        """
        Perform time series cross-validation with walk-forward analysis.
        """
        from sklearn.model_selection import TimeSeriesSplit

        # Create model
        base_models = self.create_base_models()
        if model_name not in base_models:
            raise ValueError(f"Model {model_name} not available")

        model = base_models[model_name]

        # Time series split
        tscv = TimeSeriesSplit(n_splits=n_splits, test_size=test_size)

        cv_results = {
            'fold_scores': [],
            'fold_predictions': [],
            'fold_actuals': [],
            'fold_dates': []
        }

        print(f"🔄 Running {n_splits}-fold time series cross-validation...")

        for fold, (train_idx, test_idx) in enumerate(tscv.split(X)):
            print(f"Fold {fold + 1}/{n_splits}")

            # Split data
            X_train_fold = X.iloc[train_idx]
            y_train_fold = y.iloc[train_idx]
            X_test_fold = X.iloc[test_idx]
            y_test_fold = y.iloc[test_idx]

            # Create pipeline
            if model_name in ['svm', 'neural_network', 'logistic_regression']:
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', model)
                ])
            else:
                pipeline = Pipeline([('model', model)])

            # Train and predict
            pipeline.fit(X_train_fold, y_train_fold)
            fold_pred = pipeline.predict_proba(X_test_fold)[:, 1]

            # Calculate AUC for this fold
            fold_auc = roc_auc_score(y_test_fold, fold_pred)

            cv_results['fold_scores'].append(fold_auc)
            cv_results['fold_predictions'].extend(fold_pred)
            cv_results['fold_actuals'].extend(y_test_fold.values)
            cv_results['fold_dates'].extend(X_test_fold.index.tolist())

            print(f"  Fold {fold + 1} AUC: {fold_auc:.3f}")

        # Calculate overall metrics
        overall_auc = roc_auc_score(cv_results['fold_actuals'], cv_results['fold_predictions'])
        mean_auc = np.mean(cv_results['fold_scores'])
        std_auc = np.std(cv_results['fold_scores'])

        cv_results.update({
            'overall_auc': overall_auc,
            'mean_auc': mean_auc,
            'std_auc': std_auc,
            'model_name': model_name
        })

        print(f"✅ CV Results: Mean AUC = {mean_auc:.3f} ± {std_auc:.3f}")
        print(f"✅ Overall AUC = {overall_auc:.3f}")

        return cv_results


def main():
    """Example usage of TradingMLModels."""
    from ml.feature_engineering import AdvancedFeatureEngineer
    from data.client import BinanceDataClient

    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=2000)

    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 2000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=2000)

    print(f"Loaded {len(df)} candles")

    # Feature engineering
    engineer = AdvancedFeatureEngineer()
    df_features = engineer.create_all_features(df)
    df_with_targets = engineer.create_target_variables(df_features, lookahead=1)

    # Prepare data
    target = 'target_up'
    features = engineer.select_features(df_with_targets, target)

    # Remove rows with NaN
    df_clean = df_with_targets[features + [target]].dropna()

    if len(df_clean) < 100:
        print("❌ Insufficient clean data for training")
        return

    X = df_clean[features]
    y = df_clean[target]

    print(f"Training data: {len(X)} samples, {len(features)} features")

    # Split data (time series split)
    split_idx = int(len(X) * 0.7)
    val_split_idx = int(len(X) * 0.85)

    X_train = X.iloc[:split_idx]
    y_train = y.iloc[:split_idx]
    X_val = X.iloc[split_idx:val_split_idx]
    y_val = y.iloc[split_idx:val_split_idx]
    X_test = X.iloc[val_split_idx:]
    y_test = y.iloc[val_split_idx:]

    print(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")

    # Train models
    ml_models = TradingMLModels()
    results = ml_models.train_models(X_train, y_train, X_val, y_val)

    # Evaluate models
    evaluation = ml_models.evaluate_models(results, X_test, y_test)
    print("\n📊 Model Evaluation Results:")
    print(evaluation.to_string(index=False))

    # Feature importance
    importance = ml_models.get_feature_importance_summary(results)
    if not importance.empty:
        print("\n🎯 Top 10 Most Important Features:")
        print(importance.head(10).to_string(index=False))

    # Save models
    ml_models.save_models(results, "trained_models.joblib")


def main():
    """Example usage of TradingMLModels."""
    from ml.feature_engineering import AdvancedFeatureEngineer
    from data.client import BinanceDataClient

    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=2000)

    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 2000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=2000)

    print(f"Loaded {len(df)} candles")

    # Feature engineering
    engineer = AdvancedFeatureEngineer()
    df_features = engineer.create_all_features(df)
    df_with_targets = engineer.create_target_variables(df_features, lookahead=1)

    # Prepare data
    target = 'target_up'
    features = engineer.select_features(df_with_targets, target)

    # Remove rows with NaN
    df_clean = df_with_targets[features + [target]].dropna()

    if len(df_clean) < 100:
        print("❌ Insufficient clean data for training")
        return

    X = df_clean[features]
    y = df_clean[target]

    print(f"Training data: {len(X)} samples, {len(features)} features")

    # Split data (time series split)
    split_idx = int(len(X) * 0.7)
    val_split_idx = int(len(X) * 0.85)

    X_train = X.iloc[:split_idx]
    y_train = y.iloc[:split_idx]
    X_val = X.iloc[split_idx:val_split_idx]
    y_val = y.iloc[split_idx:val_split_idx]
    X_test = X.iloc[val_split_idx:]
    y_test = y.iloc[val_split_idx:]

    print(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")

    # Train models
    ml_models = TradingMLModels()
    results = ml_models.train_models(X_train, y_train, X_val, y_val)

    # Evaluate models
    evaluation = ml_models.evaluate_models(results, X_test, y_test)
    print("\n📊 Model Evaluation Results:")
    print(evaluation.to_string(index=False))

    # Feature importance
    importance = ml_models.get_feature_importance_summary(results)
    if not importance.empty:
        print("\n🎯 Top 10 Most Important Features:")
        print(importance.head(10).to_string(index=False))

    # Save models
    ml_models.save_models(results, "trained_models.joblib")


if __name__ == "__main__":
    main()
