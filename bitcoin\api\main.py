"""
FastAPI REST API for Bitcoin Trading System.
Provides comprehensive endpoints for trading operations, analysis, and monitoring.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, BackgroundTasks, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import uvicorn
import logging
import time
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.client import BinanceDataClient
from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy
from analysis.simple_mtf import simple_mtf_analysis
from analysis.simple_regime import simple_regime_detection
from ml.feature_engineering import AdvancedFeatureEngineer

# Initialize FastAPI app
app = FastAPI(
    title="Bitcoin Trading System API",
    description="Comprehensive API for cryptocurrency trading analysis and automation",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Security
security = HTTPBearer()

# Global variables
client = BinanceDataClient()
runner = BacktestRunner()

# Pydantic models
class TradingSymbol(BaseModel):
    symbol: str = Field(..., example="BTCUSDT")
    limit: Optional[int] = Field(1000, ge=1, le=5000)

class BacktestRequest(BaseModel):
    symbol: str = Field(..., example="BTCUSDT")
    strategy: str = Field(..., example="ma_rsi")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    limit: Optional[int] = Field(1000, ge=100, le=5000)

class AnalysisRequest(BaseModel):
    symbol: str = Field(..., example="BTCUSDT")
    timeframes: Optional[List[str]] = Field(["1h", "4h", "1d"])

class APIResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

# Authentication (simplified for demo)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)):
    # In production, implement proper JWT validation
    if credentials.credentials != "demo_token":
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")
    return {"user_id": "demo_user"}

# Health check
@app.get("/healthz", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint for load balancers."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# Market data endpoints
@app.get("/api/v1/market/price/{symbol}", response_model=APIResponse)
async def get_current_price(symbol: str):
    """Get current price for a trading symbol."""
    try:
        # Get latest candle
        df = client.get_candles(symbol, limit=1)
        if df.empty:
            # Fetch from API if not in cache
            df = client.fetch_historical_klines(symbol, "1m", 1)
        
        if df.empty:
            raise HTTPException(status_code=404, detail="Symbol not found")
        
        latest = df.iloc[-1]
        
        return APIResponse(
            success=True,
            data={
                "symbol": symbol,
                "price": float(latest['close']),
                "volume": float(latest['volume']),
                "timestamp": latest.name.isoformat(),
                "change_24h": 0.0  # Would calculate from 24h data
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market/candles/{symbol}", response_model=APIResponse)
async def get_candles(symbol: str, interval: str = "1h", limit: int = 100):
    """Get historical candlestick data."""
    try:
        if limit > 1000:
            limit = 1000
        
        df = client.get_candles(symbol, limit=limit)
        if df.empty:
            df = client.fetch_historical_klines(symbol, interval, limit)
        
        if df.empty:
            raise HTTPException(status_code=404, detail="No data found")
        
        # Convert to API format
        candles = []
        for idx, row in df.iterrows():
            candles.append({
                "timestamp": idx.isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": float(row['volume'])
            })
        
        return APIResponse(
            success=True,
            data={
                "symbol": symbol,
                "interval": interval,
                "candles": candles
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Analysis endpoints
@app.post("/api/v1/analysis/multi-timeframe", response_model=APIResponse)
async def multi_timeframe_analysis(request: AnalysisRequest):
    """Perform multi-timeframe analysis."""
    try:
        report = simple_mtf_analysis(request.symbol)
        
        return APIResponse(
            success=True,
            data={
                "symbol": request.symbol,
                "analysis": report,
                "timeframes": request.timeframes
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis/regime-detection", response_model=APIResponse)
async def regime_detection(request: AnalysisRequest):
    """Perform market regime detection."""
    try:
        report = simple_regime_detection(request.symbol)
        
        return APIResponse(
            success=True,
            data={
                "symbol": request.symbol,
                "regime_analysis": report
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis/features", response_model=APIResponse)
async def generate_features(request: TradingSymbol):
    """Generate advanced features for ML analysis."""
    try:
        df = client.get_candles(request.symbol, limit=request.limit)
        if df.empty:
            df = client.fetch_historical_klines(request.symbol, "1h", request.limit)
        
        if df.empty:
            raise HTTPException(status_code=404, detail="No data found")
        
        # Generate features
        engineer = AdvancedFeatureEngineer()
        df_features = engineer.create_all_features(df)
        
        # Get feature summary
        feature_summary = {
            "total_features": len(df_features.columns),
            "original_features": len(df.columns),
            "engineered_features": len(df_features.columns) - len(df.columns),
            "data_points": len(df_features),
            "feature_names": df_features.columns.tolist()
        }
        
        return APIResponse(
            success=True,
            data={
                "symbol": request.symbol,
                "feature_summary": feature_summary
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Backtesting endpoints
@app.post("/api/v1/backtest/run", response_model=APIResponse)
async def run_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """Run strategy backtest."""
    try:
        # Get data
        df = client.get_candles(request.symbol, limit=request.limit)
        if df.empty:
            df = client.fetch_historical_klines(request.symbol, "1h", request.limit)
        
        if df.empty:
            raise HTTPException(status_code=404, detail="No data found")
        
        # Select strategy
        if request.strategy == "ma_rsi":
            strategy_class = MARSIStrategy
        elif request.strategy == "supertrend":
            strategy_class = SuperTrendStrategy
        else:
            raise HTTPException(status_code=400, detail="Invalid strategy")
        
        # Run backtest
        result = runner.run_backtest(strategy_class, df, request.parameters)
        
        return APIResponse(
            success=True,
            data={
                "symbol": request.symbol,
                "strategy": request.strategy,
                "parameters": request.parameters,
                "results": {
                    "total_return": result['total_return'],
                    "sharpe_ratio": result['metrics'].get('sharpe_ratio', 0),
                    "max_drawdown": result['metrics'].get('max_drawdown', 0),
                    "win_rate": result['metrics'].get('win_rate', 0),
                    "total_trades": result['metrics'].get('total_trades', 0)
                }
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# System endpoints
@app.get("/api/v1/system/status", response_model=APIResponse)
async def system_status(user = Depends(get_current_user)):
    """Get system status and metrics."""
    try:
        # Check database connection
        db_status = "healthy"
        try:
            client.get_candles("BTCUSDT", limit=1)
        except:
            db_status = "unhealthy"
        
        # System metrics
        import psutil
        
        status = {
            "database": db_status,
            "uptime": time.time(),  # Would track actual uptime
            "memory_usage": psutil.virtual_memory().percent,
            "cpu_usage": psutil.cpu_percent(),
            "disk_usage": psutil.disk_usage('/').percent,
            "active_connections": 0,  # Would track actual connections
            "last_update": datetime.now().isoformat()
        }
        
        return APIResponse(success=True, data=status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/system/metrics", response_model=APIResponse)
async def system_metrics():
    """Get system performance metrics for monitoring."""
    try:
        import psutil
        
        metrics = {
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count()
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv
            }
        }
        
        return APIResponse(success=True, data=metrics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    logging.info("Bitcoin Trading API starting up...")
    
    # Initialize database connections
    try:
        client.get_candles("BTCUSDT", limit=1)
        logging.info("Database connection established")
    except Exception as e:
        logging.error(f"Database connection failed: {e}")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logging.info("Bitcoin Trading API shutting down...")

if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
