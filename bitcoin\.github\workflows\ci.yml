name: Bitcoin Trading Signals CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 9 AM UTC for signal generation
    - cron: '0 9 * * *'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirement.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirement.txt
        pip install pytest pytest-cov
    
    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings. GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Format check with black
      run: |
        black --check --diff .
    
    - name: Run tests with pytest
      run: |
        pytest tests/ -v --cov=. --cov-report=xml --cov-report=html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  signal-generation:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirement.txt
    
    - name: Generate trading signals
      env:
        DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
      run: |
        cd bitcoin
        python -c "
        from data.client import BinanceDataClient
        from indicators.ta_wrappers import add_all_indicators, get_trading_signals
        from execution.paper_trader import PaperTrader
        import requests
        import os
        
        # Fetch latest data
        client = BinanceDataClient()
        df = client.fetch_historical_klines('BTCUSDT', '1h', 100)
        df = add_all_indicators(df)
        signals = get_trading_signals(df)
        
        # Get latest values
        latest = df.iloc[-1]
        latest_signals = signals.iloc[-1]
        
        # Create signal message
        rsi_status = 'Overbought' if latest['RSI'] > 70 else 'Oversold' if latest['RSI'] < 30 else 'Neutral'
        ma_signal = 'Buy' if latest_signals['MA_Signal'] == 1 else 'Sell' if latest_signals['MA_Signal'] == -1 else 'Hold'
        
        message = f'''
        🤖 **Automated Bitcoin Signal - {latest.name.strftime('%Y-%m-%d %H:%M')} UTC**
        
        📊 **Current Price:** ${latest['close']:,.2f}
        📈 **RSI:** {latest['RSI']:.1f} ({rsi_status})
        🔄 **MA Signal:** {ma_signal}
        📉 **ATR:** ${latest['ATR']:.2f}
        
        🎯 **SuperTrend:** ${latest['SuperTrend']:,.2f}
        📊 **VWAP:** ${latest['VWAP']:,.2f}
        
        ⚠️ *This is an automated signal for educational purposes only. Not financial advice.*
        '''
        
        # Send to Discord if webhook URL is available
        webhook_url = os.getenv('DISCORD_WEBHOOK_URL')
        if webhook_url:
            payload = {'content': message}
            response = requests.post(webhook_url, json=payload)
            print(f'Discord notification sent: {response.status_code}')
        else:
            print('No Discord webhook configured')
            print(message)
        "

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Bandit Security Scan
      uses: securecodewarrior/github-action-bandit@v1.0.1
      with:
        path: "."
        level: "low"
        confidence: "low"
        exit_zero: true

  dependency-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install safety
      run: pip install safety
    
    - name: Check dependencies for security vulnerabilities
      run: |
        pip install -r requirement.txt
        safety check --json || true

  build-docs:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install documentation dependencies
      run: |
        pip install mkdocs mkdocs-material mkdocstrings[python]
    
    - name: Build documentation
      run: |
        mkdocs build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site
