"""
Real-Time Trading Engine for live trading operations.
Handles order execution, position management, and real-time market data.
"""

import asyncio
import websockets
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import threading
import queue
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exchanges.base_exchange import BaseExchange, OrderType, OrderSide, OrderStatus
from exchanges.exchange_manager import ExchangeManager
from risk.advanced_risk_manager import AdvancedRiskManager
from strategies.base_strategy import BaseStrategy


class EngineState(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


@dataclass
class MarketData:
    symbol: str
    price: float
    bid: float
    ask: float
    volume: float
    timestamp: datetime


@dataclass
class Signal:
    symbol: str
    side: OrderSide
    strength: float  # 0-1
    price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    timestamp: datetime
    strategy: str


@dataclass
class Position:
    symbol: str
    side: OrderSide
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    timestamp: datetime


class RealTimeTradingEngine:
    """
    Real-time trading engine with live market data and order execution.
    """
    
    def __init__(self, 
                 exchange_manager: ExchangeManager,
                 risk_manager: AdvancedRiskManager,
                 config: Optional[Dict] = None):
        
        self.exchange_manager = exchange_manager
        self.risk_manager = risk_manager
        self.config = config or self._default_config()
        
        # Engine state
        self.state = EngineState.STOPPED
        self.logger = logging.getLogger(__name__)
        
        # Data storage
        self.market_data: Dict[str, MarketData] = {}
        self.positions: Dict[str, Position] = {}
        self.pending_orders: Dict[str, Dict] = {}
        self.signals_queue = queue.Queue()
        
        # Strategies
        self.strategies: Dict[str, BaseStrategy] = {}
        
        # Event callbacks
        self.on_market_data: Optional[Callable] = None
        self.on_signal: Optional[Callable] = None
        self.on_order_fill: Optional[Callable] = None
        self.on_position_update: Optional[Callable] = None
        
        # Threading
        self.market_data_thread: Optional[threading.Thread] = None
        self.signal_processing_thread: Optional[threading.Thread] = None
        self.position_monitoring_thread: Optional[threading.Thread] = None
        
        # Performance tracking
        self.start_time: Optional[datetime] = None
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        
    def _default_config(self) -> Dict[str, Any]:
        """Default engine configuration."""
        return {
            'symbols': ['BTCUSDT', 'ETHUSDT'],
            'update_interval': 1.0,  # seconds
            'max_positions': 5,
            'enable_live_trading': False,  # Safety: start with paper trading
            'position_check_interval': 5.0,
            'signal_processing_interval': 0.1,
            'max_slippage': 0.001,  # 0.1%
            'order_timeout': 30,  # seconds
            'emergency_stop_loss': 0.10,  # 10%
            'max_daily_loss': 0.05,  # 5%
            'websocket_url': 'wss://stream.binance.com:9443/ws/btcusdt@ticker'
        }
    
    def add_strategy(self, name: str, strategy: BaseStrategy):
        """Add a trading strategy to the engine."""
        self.strategies[name] = strategy
        self.logger.info(f"Added strategy: {name}")
    
    def remove_strategy(self, name: str):
        """Remove a trading strategy from the engine."""
        if name in self.strategies:
            del self.strategies[name]
            self.logger.info(f"Removed strategy: {name}")
    
    async def start(self):
        """Start the real-time trading engine."""
        if self.state != EngineState.STOPPED:
            raise RuntimeError(f"Engine is not stopped (current state: {self.state})")
        
        self.state = EngineState.STARTING
        self.start_time = datetime.now()
        
        try:
            # Start market data feed
            self.market_data_thread = threading.Thread(
                target=self._market_data_worker,
                daemon=True
            )
            self.market_data_thread.start()
            
            # Start signal processing
            self.signal_processing_thread = threading.Thread(
                target=self._signal_processing_worker,
                daemon=True
            )
            self.signal_processing_thread.start()
            
            # Start position monitoring
            self.position_monitoring_thread = threading.Thread(
                target=self._position_monitoring_worker,
                daemon=True
            )
            self.position_monitoring_thread.start()
            
            self.state = EngineState.RUNNING
            self.logger.info("Real-time trading engine started")
            
        except Exception as e:
            self.state = EngineState.ERROR
            self.logger.error(f"Failed to start engine: {e}")
            raise
    
    def stop(self):
        """Stop the real-time trading engine."""
        if self.state == EngineState.STOPPED:
            return
        
        self.logger.info("Stopping real-time trading engine...")
        self.state = EngineState.STOPPED
        
        # Cancel all pending orders
        self._cancel_all_orders()
        
        # Wait for threads to finish
        if self.market_data_thread and self.market_data_thread.is_alive():
            self.market_data_thread.join(timeout=5)
        
        if self.signal_processing_thread and self.signal_processing_thread.is_alive():
            self.signal_processing_thread.join(timeout=5)
        
        if self.position_monitoring_thread and self.position_monitoring_thread.is_alive():
            self.position_monitoring_thread.join(timeout=5)
        
        self.logger.info("Real-time trading engine stopped")
    
    def pause(self):
        """Pause trading (stop new orders but keep monitoring)."""
        if self.state == EngineState.RUNNING:
            self.state = EngineState.PAUSED
            self.logger.info("Trading engine paused")
    
    def resume(self):
        """Resume trading from paused state."""
        if self.state == EngineState.PAUSED:
            self.state = EngineState.RUNNING
            self.logger.info("Trading engine resumed")
    
    def _market_data_worker(self):
        """Worker thread for processing market data."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                # Get market data from exchanges
                for symbol in self.config['symbols']:
                    for exchange_name, exchange in self.exchange_manager.exchanges.items():
                        try:
                            ticker = exchange.get_ticker(symbol)
                            
                            market_data = MarketData(
                                symbol=symbol,
                                price=ticker['price'],
                                bid=ticker['bid'],
                                ask=ticker['ask'],
                                volume=ticker['volume'],
                                timestamp=datetime.now()
                            )
                            
                            self.market_data[symbol] = market_data
                            
                            # Process strategies
                            if self.state == EngineState.RUNNING:
                                self._process_strategies(symbol, market_data)
                            
                            # Callback
                            if self.on_market_data:
                                self.on_market_data(market_data)
                                
                        except Exception as e:
                            self.logger.warning(f"Failed to get market data for {symbol} from {exchange_name}: {e}")
                
                time.sleep(self.config['update_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in market data worker: {e}")
                time.sleep(1)
    
    def _process_strategies(self, symbol: str, market_data: MarketData):
        """Process all strategies for a symbol."""
        for strategy_name, strategy in self.strategies.items():
            try:
                # Create a simple DataFrame for the strategy
                # In practice, you'd maintain a rolling window of data
                df = pd.DataFrame([{
                    'timestamp': market_data.timestamp,
                    'open': market_data.price,
                    'high': market_data.price,
                    'low': market_data.price,
                    'close': market_data.price,
                    'volume': market_data.volume
                }])
                df.set_index('timestamp', inplace=True)
                
                # Generate signals
                signals = strategy.generate_signals(df)
                
                if not signals.empty:
                    latest_signal = signals.iloc[-1]
                    
                    if latest_signal['signal'] != 0:  # Non-zero signal
                        signal = Signal(
                            symbol=symbol,
                            side=OrderSide.BUY if latest_signal['signal'] > 0 else OrderSide.SELL,
                            strength=abs(latest_signal['signal']),
                            price=market_data.price,
                            stop_loss=latest_signal.get('stop_loss'),
                            take_profit=latest_signal.get('take_profit'),
                            timestamp=datetime.now(),
                            strategy=strategy_name
                        )
                        
                        self.signals_queue.put(signal)
                        
            except Exception as e:
                self.logger.warning(f"Error processing strategy {strategy_name} for {symbol}: {e}")
    
    def _signal_processing_worker(self):
        """Worker thread for processing trading signals."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                # Process signals from queue
                try:
                    signal = self.signals_queue.get(timeout=self.config['signal_processing_interval'])
                    
                    if self.state == EngineState.RUNNING:
                        self._process_signal(signal)
                    
                    # Callback
                    if self.on_signal:
                        self.on_signal(signal)
                        
                except queue.Empty:
                    continue
                    
            except Exception as e:
                self.logger.error(f"Error in signal processing worker: {e}")
                time.sleep(0.1)
    
    def _process_signal(self, signal: Signal):
        """Process a trading signal."""
        try:
            # Check if we already have a position
            if signal.symbol in self.positions:
                current_position = self.positions[signal.symbol]
                
                # Check if signal is opposite to current position
                if current_position.side != signal.side:
                    # Close current position first
                    self._close_position(signal.symbol)
            
            # Check risk limits
            if len(self.positions) >= self.config['max_positions']:
                self.logger.warning(f"Maximum positions reached, ignoring signal for {signal.symbol}")
                return
            
            # Calculate position size
            portfolio_value = self._get_portfolio_value()
            position_sizing = self.risk_manager.calculate_position_size(
                symbol=signal.symbol,
                entry_price=signal.price or 0,
                stop_loss_price=signal.stop_loss or 0,
                portfolio_value=portfolio_value
            )
            
            quantity = position_sizing['recommended_size']
            
            if quantity > 0:
                # Place order
                if self.config['enable_live_trading']:
                    self._place_live_order(signal, quantity)
                else:
                    self._place_paper_order(signal, quantity)
                    
        except Exception as e:
            self.logger.error(f"Error processing signal: {e}")
    
    def _place_live_order(self, signal: Signal, quantity: float):
        """Place a live order on the exchange."""
        # Get the best exchange for this order
        best_price_info = self.exchange_manager.get_best_price(signal.symbol, signal.side)
        
        if not best_price_info['best_exchange']:
            self.logger.warning(f"No exchange available for {signal.symbol}")
            return
        
        exchange = self.exchange_manager.get_exchange(best_price_info['best_exchange'])
        
        try:
            order = exchange.place_order(
                symbol=signal.symbol,
                side=signal.side,
                order_type=OrderType.MARKET,  # Use market orders for simplicity
                quantity=quantity
            )
            
            self.pending_orders[order['order_id']] = {
                'order': order,
                'signal': signal,
                'exchange': best_price_info['best_exchange']
            }
            
            self.logger.info(f"Placed live order: {order}")
            
        except Exception as e:
            self.logger.error(f"Failed to place live order: {e}")
    
    def _place_paper_order(self, signal: Signal, quantity: float):
        """Place a paper trading order (simulation)."""
        # Simulate order execution
        execution_price = signal.price or 0
        
        # Add some slippage
        slippage = np.random.uniform(-self.config['max_slippage'], self.config['max_slippage'])
        execution_price *= (1 + slippage)
        
        # Create position
        position = Position(
            symbol=signal.symbol,
            side=signal.side,
            quantity=quantity,
            entry_price=execution_price,
            current_price=execution_price,
            unrealized_pnl=0.0,
            timestamp=datetime.now()
        )
        
        self.positions[signal.symbol] = position
        self.total_trades += 1
        
        self.logger.info(f"Paper trade executed: {signal.symbol} {signal.side.value} {quantity} @ {execution_price}")
        
        # Callback
        if self.on_position_update:
            self.on_position_update(position)
    
    def _position_monitoring_worker(self):
        """Worker thread for monitoring positions."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                self._update_positions()
                self._check_stop_losses()
                self._check_take_profits()
                self._check_emergency_stops()
                
                time.sleep(self.config['position_check_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in position monitoring worker: {e}")
                time.sleep(1)
    
    def _update_positions(self):
        """Update position values with current market prices."""
        for symbol, position in self.positions.items():
            if symbol in self.market_data:
                current_price = self.market_data[symbol].price
                position.current_price = current_price
                
                # Calculate unrealized PnL
                if position.side == OrderSide.BUY:
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
    
    def _check_stop_losses(self):
        """Check and execute stop losses."""
        for symbol, position in list(self.positions.items()):
            if symbol in self.market_data:
                current_price = self.market_data[symbol].price
                
                # Simple stop loss check (5% by default)
                stop_loss_pct = self.config.get('emergency_stop_loss', 0.05)
                
                if position.side == OrderSide.BUY:
                    stop_price = position.entry_price * (1 - stop_loss_pct)
                    if current_price <= stop_price:
                        self._close_position(symbol, "Stop Loss")
                else:
                    stop_price = position.entry_price * (1 + stop_loss_pct)
                    if current_price >= stop_price:
                        self._close_position(symbol, "Stop Loss")
    
    def _check_take_profits(self):
        """Check and execute take profits."""
        # Implementation would be similar to stop losses
        pass
    
    def _check_emergency_stops(self):
        """Check for emergency stop conditions."""
        # Check daily loss limit
        daily_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        portfolio_value = self._get_portfolio_value()
        
        if portfolio_value > 0:
            daily_loss_pct = abs(daily_pnl) / portfolio_value
            
            if daily_loss_pct > self.config['max_daily_loss']:
                self.logger.critical(f"Daily loss limit exceeded: {daily_loss_pct:.2%}")
                self._emergency_stop()
    
    def _close_position(self, symbol: str, reason: str = "Manual"):
        """Close a position."""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        
        if self.config['enable_live_trading']:
            # Place closing order on exchange
            # Implementation would place opposite order
            pass
        else:
            # Paper trading - just remove position
            self.total_pnl += position.unrealized_pnl
            if position.unrealized_pnl > 0:
                self.winning_trades += 1
        
        del self.positions[symbol]
        self.logger.info(f"Closed position {symbol}: {reason}, PnL: {position.unrealized_pnl:.2f}")
    
    def _emergency_stop(self):
        """Emergency stop - close all positions and pause trading."""
        self.logger.critical("EMERGENCY STOP TRIGGERED")
        
        # Close all positions
        for symbol in list(self.positions.keys()):
            self._close_position(symbol, "Emergency Stop")
        
        # Pause trading
        self.pause()
    
    def _cancel_all_orders(self):
        """Cancel all pending orders."""
        for order_id, order_info in list(self.pending_orders.items()):
            try:
                exchange = self.exchange_manager.get_exchange(order_info['exchange'])
                if exchange:
                    exchange.cancel_order(order_info['order']['symbol'], order_id)
                    
                del self.pending_orders[order_id]
                
            except Exception as e:
                self.logger.warning(f"Failed to cancel order {order_id}: {e}")
    
    def _get_portfolio_value(self) -> float:
        """Calculate total portfolio value."""
        total_value = 100000  # Default starting value
        
        # Add unrealized PnL
        total_value += sum(pos.unrealized_pnl for pos in self.positions.values())
        total_value += self.total_pnl
        
        return total_value
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get engine performance statistics."""
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        return {
            'state': self.state.value,
            'runtime': str(runtime),
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
            'active_positions': len(self.positions),
            'pending_orders': len(self.pending_orders),
            'portfolio_value': self._get_portfolio_value()
        }


def main():
    """Example usage of RealTimeTradingEngine."""
    from exchanges.binance_exchange import BinanceExchange
    
    # Setup
    exchange_manager = ExchangeManager()
    exchange_manager.add_exchange("binance", BinanceExchange("test", "test", testnet=True))
    
    risk_manager = AdvancedRiskManager()
    
    engine = RealTimeTradingEngine(exchange_manager, risk_manager)
    
    try:
        # Start engine
        asyncio.run(engine.start())
        
        # Run for a short time
        time.sleep(10)
        
        # Get stats
        stats = engine.get_performance_stats()
        print(f"Engine stats: {stats}")
        
    except KeyboardInterrupt:
        print("Stopping engine...")
    finally:
        engine.stop()


if __name__ == "__main__":
    main()
