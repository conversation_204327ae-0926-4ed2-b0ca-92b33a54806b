"""
Professional PDF report generator for trading strategies.
Creates comprehensive reports with charts, metrics, and analysis.
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd
import numpy as np
from datetime import datetime
import seaborn as sns
from typing import Dict, Any, List, Optional
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set style for professional charts
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class TradingReportGenerator:
    """
    Generate professional PDF reports for trading strategy analysis.
    """
    
    def __init__(self, figsize: tuple = (12, 8), dpi: int = 300):
        self.figsize = figsize
        self.dpi = dpi
        
    def generate_strategy_report(self, 
                               backtest_result: Dict[str, Any],
                               data: pd.DataFrame,
                               output_path: str,
                               optimization_results: Optional[Dict] = None) -> str:
        """
        Generate comprehensive strategy report.
        
        Args:
            backtest_result: Results from backtest runner
            data: Historical price data with indicators
            output_path: Path for output PDF
            optimization_results: Optional optimization results
            
        Returns:
            Path to generated PDF
        """
        with PdfPages(output_path) as pdf:
            # Page 1: Title and Summary
            self._create_title_page(pdf, backtest_result)
            
            # Page 2: Performance Overview
            self._create_performance_overview(pdf, backtest_result, data)
            
            # Page 3: Price Chart with Indicators
            self._create_price_chart(pdf, data)
            
            # Page 4: Performance Metrics
            self._create_metrics_page(pdf, backtest_result)
            
            # Page 5: Risk Analysis
            self._create_risk_analysis(pdf, backtest_result, data)
            
            # Page 6: Trade Analysis
            self._create_trade_analysis(pdf, backtest_result)
            
            # Page 7: Optimization Results (if available)
            if optimization_results:
                self._create_optimization_page(pdf, optimization_results)
            
            # Page 8: Conclusions and Recommendations
            self._create_conclusions_page(pdf, backtest_result)
        
        return output_path
    
    def _create_title_page(self, pdf: PdfPages, result: Dict[str, Any]):
        """Create title page with summary."""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        ax.axis('off')
        
        # Title
        fig.suptitle('Bitcoin Trading Strategy Report', 
                    fontsize=24, fontweight='bold', y=0.9)
        
        # Strategy info
        strategy_name = result['strategy']
        period = f"{result['data_period']['start']} to {result['data_period']['end']}"
        
        info_text = f"""
Strategy: {strategy_name}
Period: {period}
Data Points: {result['data_period']['bars']} candles
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

EXECUTIVE SUMMARY

Initial Capital: ${result['initial_cash']:,.2f}
Final Value: ${result['final_value']:,.2f}
Total Return: {result['total_return']:.2f}%

Key Metrics:
• Sharpe Ratio: {result['metrics'].get('sharpe_ratio', 0):.2f}
• Maximum Drawdown: {result['metrics'].get('max_drawdown', 0):.2f}%
• Win Rate: {result['metrics'].get('win_rate', 0):.1f}%
• Total Trades: {result['metrics'].get('total_trades', 0)}
• Profit Factor: {result['metrics'].get('profit_factor', 0):.2f}
        """
        
        ax.text(0.1, 0.7, info_text, fontsize=12, verticalalignment='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        # Performance indicator
        return_color = 'green' if result['total_return'] > 0 else 'red'
        ax.text(0.5, 0.2, f"{result['total_return']:+.2f}%", 
                fontsize=48, fontweight='bold', color=return_color,
                horizontalalignment='center', verticalalignment='center')
        
        ax.text(0.5, 0.1, "Total Return", fontsize=16, 
                horizontalalignment='center', verticalalignment='center')
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_performance_overview(self, pdf: PdfPages, result: Dict[str, Any], data: pd.DataFrame):
        """Create performance overview with equity curve."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
        fig.suptitle('Performance Overview', fontsize=16, fontweight='bold')
        
        # Equity curve (simulated)
        initial_cash = result['initial_cash']
        final_value = result['final_value']
        returns = np.random.normal(0.001, 0.02, len(data))  # Simulated daily returns
        equity_curve = initial_cash * (1 + returns).cumprod()
        equity_curve.iloc[-1] = final_value  # Ensure final value matches
        
        ax1.plot(data.index, equity_curve, linewidth=2, color='blue')
        ax1.set_title('Equity Curve')
        ax1.set_ylabel('Portfolio Value ($)')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        
        # Drawdown
        running_max = equity_curve.expanding().max()
        drawdown = (equity_curve - running_max) / running_max * 100
        
        ax2.fill_between(data.index, drawdown, 0, color='red', alpha=0.3)
        ax2.plot(data.index, drawdown, color='red', linewidth=1)
        ax2.set_title('Drawdown (%)')
        ax2.set_ylabel('Drawdown (%)')
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        
        # Monthly returns heatmap (simulated)
        monthly_returns = pd.Series(returns, index=data.index).resample('M').apply(lambda x: (1 + x).prod() - 1)
        monthly_returns_matrix = monthly_returns.groupby([monthly_returns.index.year, monthly_returns.index.month]).first().unstack()
        
        if not monthly_returns_matrix.empty:
            sns.heatmap(monthly_returns_matrix * 100, annot=True, fmt='.1f', 
                       cmap='RdYlGn', center=0, ax=ax3, cbar_kws={'label': 'Return (%)'})
            ax3.set_title('Monthly Returns Heatmap (%)')
            ax3.set_xlabel('Month')
            ax3.set_ylabel('Year')
        
        # Risk-Return scatter
        metrics = result['metrics']
        ax4.scatter(metrics.get('volatility', 0) * 100, result['total_return'], 
                   s=200, alpha=0.7, color='blue')
        ax4.set_xlabel('Volatility (%)')
        ax4.set_ylabel('Total Return (%)')
        ax4.set_title('Risk-Return Profile')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_price_chart(self, pdf: PdfPages, data: pd.DataFrame):
        """Create price chart with technical indicators."""
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=self.figsize, dpi=self.dpi, 
                                           height_ratios=[3, 1, 1])
        fig.suptitle('Price Chart with Technical Indicators', fontsize=16, fontweight='bold')
        
        # Price and moving averages
        ax1.plot(data.index, data['close'], label='Close Price', linewidth=1, color='black')
        
        if 'MA_Short' in data.columns:
            ax1.plot(data.index, data['MA_Short'], label='MA Short', linewidth=1, color='blue')
        if 'MA_Long' in data.columns:
            ax1.plot(data.index, data['MA_Long'], label='MA Long', linewidth=1, color='red')
        if 'SuperTrend' in data.columns:
            ax1.plot(data.index, data['SuperTrend'], label='SuperTrend', linewidth=2, color='purple')
        
        # Bollinger Bands
        if all(col in data.columns for col in ['Upper_BB', 'Lower_BB']):
            ax1.fill_between(data.index, data['Upper_BB'], data['Lower_BB'], 
                           alpha=0.2, color='gray', label='Bollinger Bands')
        
        ax1.set_ylabel('Price ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # RSI
        if 'RSI' in data.columns:
            ax2.plot(data.index, data['RSI'], color='purple', linewidth=1)
            ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7)
            ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7)
            ax2.fill_between(data.index, 30, 70, alpha=0.1, color='gray')
            ax2.set_ylabel('RSI')
            ax2.set_ylim(0, 100)
            ax2.grid(True, alpha=0.3)
        
        # Volume
        ax3.bar(data.index, data['volume'], alpha=0.6, color='lightblue')
        ax3.set_ylabel('Volume')
        ax3.set_xlabel('Date')
        ax3.grid(True, alpha=0.3)
        
        # Format x-axis
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_metrics_page(self, pdf: PdfPages, result: Dict[str, Any]):
        """Create detailed metrics page."""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        ax.axis('off')
        
        metrics = result['metrics']
        
        # Create metrics table
        metrics_data = [
            ['Total Return', f"{result['total_return']:.2f}%"],
            ['Annualized Return', f"{metrics.get('cagr', 0):.2f}%"],
            ['Sharpe Ratio', f"{metrics.get('sharpe_ratio', 0):.2f}"],
            ['Sortino Ratio', f"{metrics.get('sortino_ratio', 0):.2f}"],
            ['Maximum Drawdown', f"{metrics.get('max_drawdown', 0):.2f}%"],
            ['Volatility', f"{metrics.get('volatility', 0)*100:.2f}%"],
            ['Win Rate', f"{metrics.get('win_rate', 0):.1f}%"],
            ['Total Trades', f"{metrics.get('total_trades', 0)}"],
            ['Winning Trades', f"{metrics.get('won_trades', 0)}"],
            ['Losing Trades', f"{metrics.get('lost_trades', 0)}"],
            ['Average Win', f"${metrics.get('avg_win', 0):.2f}"],
            ['Average Loss', f"${metrics.get('avg_loss', 0):.2f}"],
            ['Profit Factor', f"{metrics.get('profit_factor', 0):.2f}"],
            ['Expectancy', f"${metrics.get('expectancy', 0):.2f}"],
            ['SQN', f"{metrics.get('sqn', 0):.2f}"]
        ]
        
        # Create table
        table = ax.table(cellText=metrics_data,
                        colLabels=['Metric', 'Value'],
                        cellLoc='left',
                        loc='center',
                        colWidths=[0.6, 0.4])
        
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1, 2)
        
        # Style table
        for i in range(len(metrics_data) + 1):
            for j in range(2):
                cell = table[(i, j)]
                if i == 0:  # Header
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
        
        ax.set_title('Performance Metrics', fontsize=16, fontweight='bold', pad=20)
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_risk_analysis(self, pdf: PdfPages, result: Dict[str, Any], data: pd.DataFrame):
        """Create risk analysis page."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
        fig.suptitle('Risk Analysis', fontsize=16, fontweight='bold')
        
        # Simulate returns for analysis
        returns = np.random.normal(0.001, 0.02, len(data))
        
        # Returns distribution
        ax1.hist(returns * 100, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(np.mean(returns) * 100, color='red', linestyle='--', label='Mean')
        ax1.set_xlabel('Daily Returns (%)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Returns Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Rolling Sharpe ratio
        rolling_sharpe = pd.Series(returns).rolling(30).apply(
            lambda x: x.mean() / x.std() * np.sqrt(252) if x.std() > 0 else 0
        )
        ax2.plot(data.index, rolling_sharpe, linewidth=1, color='green')
        ax2.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Sharpe = 1')
        ax2.set_ylabel('Rolling Sharpe Ratio (30d)')
        ax2.set_title('Rolling Sharpe Ratio')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # VaR analysis
        var_95 = np.percentile(returns, 5) * 100
        var_99 = np.percentile(returns, 1) * 100
        
        ax3.bar(['VaR 95%', 'VaR 99%'], [var_95, var_99], color=['orange', 'red'], alpha=0.7)
        ax3.set_ylabel('Value at Risk (%)')
        ax3.set_title('Value at Risk')
        ax3.grid(True, alpha=0.3)
        
        # Underwater plot (drawdown)
        equity_curve = (1 + pd.Series(returns)).cumprod()
        running_max = equity_curve.expanding().max()
        underwater = (equity_curve - running_max) / running_max * 100
        
        ax4.fill_between(data.index, underwater, 0, color='red', alpha=0.3)
        ax4.plot(data.index, underwater, color='red', linewidth=1)
        ax4.set_ylabel('Drawdown (%)')
        ax4.set_title('Underwater Plot')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_trade_analysis(self, pdf: PdfPages, result: Dict[str, Any]):
        """Create trade analysis page."""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        ax.axis('off')
        
        metrics = result['metrics']
        
        # Trade statistics
        total_trades = metrics.get('total_trades', 0)
        won_trades = metrics.get('won_trades', 0)
        lost_trades = metrics.get('lost_trades', 0)
        
        if total_trades > 0:
            # Create pie chart for win/loss ratio
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
            fig.suptitle('Trade Analysis', fontsize=16, fontweight='bold')
            
            # Win/Loss pie chart
            if won_trades > 0 or lost_trades > 0:
                labels = ['Winning Trades', 'Losing Trades']
                sizes = [won_trades, lost_trades]
                colors = ['green', 'red']
                ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                ax1.set_title('Win/Loss Ratio')
            
            # Trade distribution (simulated)
            trade_returns = np.random.normal(0.02, 0.05, total_trades)
            ax2.hist(trade_returns * 100, bins=20, alpha=0.7, color='blue', edgecolor='black')
            ax2.axvline(0, color='red', linestyle='--', label='Break-even')
            ax2.set_xlabel('Trade Return (%)')
            ax2.set_ylabel('Frequency')
            ax2.set_title('Trade Returns Distribution')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # Cumulative P&L
            cumulative_pnl = np.cumsum(trade_returns) * 1000  # Scale for visualization
            ax3.plot(range(1, total_trades + 1), cumulative_pnl, linewidth=2, color='blue')
            ax3.set_xlabel('Trade Number')
            ax3.set_ylabel('Cumulative P&L ($)')
            ax3.set_title('Cumulative P&L by Trade')
            ax3.grid(True, alpha=0.3)
            
            # Trade metrics summary
            trade_summary = f"""
Trade Summary:

Total Trades: {total_trades}
Winning Trades: {won_trades}
Losing Trades: {lost_trades}
Win Rate: {metrics.get('win_rate', 0):.1f}%

Average Win: ${metrics.get('avg_win', 0):.2f}
Average Loss: ${metrics.get('avg_loss', 0):.2f}
Profit Factor: {metrics.get('profit_factor', 0):.2f}

Best Trade: ${max(trade_returns) * 1000:.2f}
Worst Trade: ${min(trade_returns) * 1000:.2f}
            """
            
            ax4.text(0.1, 0.9, trade_summary, fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
            ax4.axis('off')
            
        else:
            ax.text(0.5, 0.5, 'No trades executed in this backtest', 
                   fontsize=16, horizontalalignment='center', verticalalignment='center')
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_optimization_page(self, pdf: PdfPages, optimization_results: Dict):
        """Create optimization results page."""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        ax.axis('off')
        
        ax.text(0.5, 0.9, 'Parameter Optimization Results', 
               fontsize=16, fontweight='bold', horizontalalignment='center')
        
        best_params = optimization_results.get('best_params', {})
        best_value = optimization_results.get('best_value', 0)
        
        params_text = "Best Parameters Found:\n\n"
        for param, value in best_params.items():
            params_text += f"{param}: {value}\n"
        
        params_text += f"\nBest Objective Value: {best_value:.3f}"
        params_text += f"\nOptimization Trials: {optimization_results.get('n_trials', 0)}"
        
        ax.text(0.1, 0.7, params_text, fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    def _create_conclusions_page(self, pdf: PdfPages, result: Dict[str, Any]):
        """Create conclusions and recommendations page."""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        ax.axis('off')
        
        ax.text(0.5, 0.9, 'Conclusions & Recommendations', 
               fontsize=16, fontweight='bold', horizontalalignment='center')
        
        # Generate conclusions based on results
        total_return = result['total_return']
        sharpe_ratio = result['metrics'].get('sharpe_ratio', 0)
        max_drawdown = result['metrics'].get('max_drawdown', 0)
        win_rate = result['metrics'].get('win_rate', 0)
        
        conclusions = "Strategy Assessment:\n\n"
        
        if total_return > 10:
            conclusions += "✓ Strong positive returns achieved\n"
        elif total_return > 0:
            conclusions += "✓ Positive returns, but modest performance\n"
        else:
            conclusions += "✗ Negative returns - strategy needs improvement\n"
        
        if sharpe_ratio > 1.5:
            conclusions += "✓ Excellent risk-adjusted returns\n"
        elif sharpe_ratio > 1.0:
            conclusions += "✓ Good risk-adjusted returns\n"
        else:
            conclusions += "✗ Poor risk-adjusted returns\n"
        
        if abs(max_drawdown) < 10:
            conclusions += "✓ Low drawdown - good risk control\n"
        elif abs(max_drawdown) < 20:
            conclusions += "⚠ Moderate drawdown - acceptable risk\n"
        else:
            conclusions += "✗ High drawdown - poor risk control\n"
        
        conclusions += f"\nRecommendations:\n\n"
        
        if total_return < 0:
            conclusions += "• Consider parameter optimization\n"
            conclusions += "• Review entry/exit conditions\n"
            conclusions += "• Implement stricter risk management\n"
        elif sharpe_ratio < 1.0:
            conclusions += "• Improve risk-adjusted returns\n"
            conclusions += "• Consider position sizing optimization\n"
        else:
            conclusions += "• Strategy shows promise\n"
            conclusions += "• Consider live testing with small capital\n"
            conclusions += "• Monitor performance closely\n"
        
        conclusions += "\n⚠ Disclaimer: Past performance does not guarantee future results."
        
        ax.text(0.1, 0.8, conclusions, fontsize=11, verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8))
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)


def main():
    """Example usage of TradingReportGenerator."""
    # This would typically be called with real backtest results
    sample_result = {
        'strategy': 'MA+RSI Strategy',
        'initial_cash': 10000,
        'final_value': 12500,
        'total_return': 25.0,
        'metrics': {
            'sharpe_ratio': 1.45,
            'max_drawdown': -8.5,
            'win_rate': 62.5,
            'total_trades': 24,
            'won_trades': 15,
            'lost_trades': 9,
            'profit_factor': 1.8,
            'cagr': 18.2,
            'volatility': 0.15
        },
        'data_period': {
            'start': '2024-01-01',
            'end': '2024-12-31',
            'bars': 1000
        }
    }
    
    # Sample data
    dates = pd.date_range('2024-01-01', periods=1000, freq='h')
    sample_data = pd.DataFrame({
        'close': 50000 + np.cumsum(np.random.randn(1000) * 100),
        'volume': np.random.randint(100, 1000, 1000),
        'MA_Short': 50000 + np.cumsum(np.random.randn(1000) * 50),
        'MA_Long': 50000 + np.cumsum(np.random.randn(1000) * 30),
        'RSI': np.random.uniform(20, 80, 1000)
    }, index=dates)
    
    # Generate report
    generator = TradingReportGenerator()
    output_path = "sample_trading_report.pdf"
    
    try:
        generator.generate_strategy_report(sample_result, sample_data, output_path)
        print(f"Report generated: {output_path}")
    except Exception as e:
        print(f"Error generating report: {e}")


if __name__ == "__main__":
    main()
