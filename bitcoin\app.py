"""
Streamlit dashboard for Bitcoin trading signals and backtesting.
Provides real-time monitoring, strategy comparison, and performance analysis.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import sqlite3
from datetime import datetime, timedelta
import sys
import os

# Add modules to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.client import BinanceDataClient
from indicators.ta_wrappers import add_all_indicators, get_trading_signals
from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy
from execution.paper_trader import PaperTrader, OrderSide, OrderType
from risk.position_sizing import RiskManager, calculate_risk_metrics

# Page config
st.set_page_config(
    page_title="Bitcoin Trading Dashboard",
    page_icon="₿",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .positive { color: #28a745; }
    .negative { color: #dc3545; }
    .neutral { color: #6c757d; }
</style>
""", unsafe_allow_html=True)


@st.cache_data(ttl=300)  # Cache for 5 minutes
def load_data(symbol="BTCUSDT", limit=500):
    """Load and cache market data."""
    client = BinanceDataClient()
    df = client.get_candles(symbol, limit)
    
    if df.empty:
        # Fetch from API if no local data
        df = client.fetch_historical_klines(symbol, "1h", limit)
        client.store_candles(df, symbol)
        df = client.get_candles(symbol, limit)
    
    return df


@st.cache_data(ttl=600)  # Cache for 10 minutes
def calculate_indicators(df):
    """Calculate technical indicators."""
    return add_all_indicators(df)


def create_price_chart(df):
    """Create interactive price chart with indicators."""
    fig = make_subplots(
        rows=4, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('Price & Moving Averages', 'RSI', 'MACD', 'Volume'),
        row_heights=[0.5, 0.2, 0.2, 0.1]
    )
    
    # Candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name='Price'
        ),
        row=1, col=1
    )
    
    # Moving averages
    if 'MA_Short' in df.columns:
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MA_Short'], name='MA Short', line=dict(color='orange')),
            row=1, col=1
        )
    if 'MA_Long' in df.columns:
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MA_Long'], name='MA Long', line=dict(color='blue')),
            row=1, col=1
        )
    
    # Bollinger Bands
    if all(col in df.columns for col in ['Upper_BB', 'Lower_BB']):
        fig.add_trace(
            go.Scatter(x=df.index, y=df['Upper_BB'], name='BB Upper', 
                      line=dict(color='gray', dash='dash')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['Lower_BB'], name='BB Lower',
                      line=dict(color='gray', dash='dash')),
            row=1, col=1
        )
    
    # SuperTrend
    if 'SuperTrend' in df.columns:
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SuperTrend'], name='SuperTrend',
                      line=dict(color='purple', width=2)),
            row=1, col=1
        )
    
    # RSI
    if 'RSI' in df.columns:
        fig.add_trace(
            go.Scatter(x=df.index, y=df['RSI'], name='RSI', line=dict(color='purple')),
            row=2, col=1
        )
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=2, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=2, col=1)
    
    # MACD
    if all(col in df.columns for col in ['MACD', 'MACD_signal']):
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MACD'], name='MACD', line=dict(color='blue')),
            row=3, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MACD_signal'], name='MACD Signal', line=dict(color='red')),
            row=3, col=1
        )
        if 'MACD_hist' in df.columns:
            fig.add_trace(
                go.Bar(x=df.index, y=df['MACD_hist'], name='MACD Histogram'),
                row=3, col=1
            )
    
    # Volume
    fig.add_trace(
        go.Bar(x=df.index, y=df['volume'], name='Volume', marker_color='lightblue'),
        row=4, col=1
    )
    
    fig.update_layout(
        title="Bitcoin Technical Analysis",
        xaxis_rangeslider_visible=False,
        height=800,
        showlegend=True
    )
    
    return fig


def display_signals(df):
    """Display current trading signals."""
    if df.empty:
        return
        
    latest = df.iloc[-1]
    signals = get_trading_signals(df)
    latest_signals = signals.iloc[-1] if not signals.empty else None
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        rsi_color = "negative" if latest['RSI'] > 70 else "positive" if latest['RSI'] < 30 else "neutral"
        st.markdown(f"""
        <div class="metric-card">
            <h4>RSI Signal</h4>
            <p class="{rsi_color}">
                {latest['RSI']:.1f} - 
                {'Overbought' if latest['RSI'] > 70 else 'Oversold' if latest['RSI'] < 30 else 'Neutral'}
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        if latest_signals is not None:
            ma_signal = "Buy" if latest_signals['MA_Signal'] == 1 else "Sell" if latest_signals['MA_Signal'] == -1 else "Hold"
            ma_color = "positive" if ma_signal == "Buy" else "negative" if ma_signal == "Sell" else "neutral"
        else:
            ma_signal = "N/A"
            ma_color = "neutral"
            
        st.markdown(f"""
        <div class="metric-card">
            <h4>MA Cross Signal</h4>
            <p class="{ma_color}">{ma_signal}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        if latest_signals is not None and 'SuperTrend_Buy' in latest_signals:
            st_signal = "Buy" if latest_signals['SuperTrend_Buy'] else "Sell" if latest_signals['SuperTrend_Sell'] else "Hold"
            st_color = "positive" if st_signal == "Buy" else "negative" if st_signal == "Sell" else "neutral"
        else:
            st_signal = "N/A"
            st_color = "neutral"
            
        st.markdown(f"""
        <div class="metric-card">
            <h4>SuperTrend Signal</h4>
            <p class="{st_color}">{st_signal}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        price_change = ((latest['close'] - df['close'].iloc[-2]) / df['close'].iloc[-2] * 100) if len(df) > 1 else 0
        price_color = "positive" if price_change > 0 else "negative" if price_change < 0 else "neutral"
        
        st.markdown(f"""
        <div class="metric-card">
            <h4>Price Action</h4>
            <p>${latest['close']:,.2f}</p>
            <p class="{price_color}">{price_change:+.2f}%</p>
        </div>
        """, unsafe_allow_html=True)


def run_backtest_interface():
    """Interface for running backtests."""
    st.subheader("Strategy Backtesting")
    
    col1, col2 = st.columns(2)
    
    with col1:
        strategy = st.selectbox("Select Strategy", ["MA + RSI", "SuperTrend"])
        initial_capital = st.number_input("Initial Capital", value=10000, min_value=1000)
        commission = st.number_input("Commission Rate", value=0.001, min_value=0.0, max_value=0.01, format="%.4f")
    
    with col2:
        if strategy == "MA + RSI":
            ma_short = st.number_input("MA Short Period", value=10, min_value=5, max_value=50)
            ma_long = st.number_input("MA Long Period", value=50, min_value=20, max_value=200)
            rsi_period = st.number_input("RSI Period", value=14, min_value=10, max_value=30)
            
            strategy_params = {
                'ma_short': ma_short,
                'ma_long': ma_long,
                'rsi_period': rsi_period
            }
            strategy_class = MARSIStrategy
        else:
            atr_period = st.number_input("ATR Period", value=10, min_value=5, max_value=30)
            atr_mult = st.number_input("ATR Multiplier", value=3.0, min_value=1.0, max_value=5.0)
            
            strategy_params = {
                'atr_period': atr_period,
                'atr_multiplier': atr_mult
            }
            strategy_class = SuperTrendStrategy
    
    if st.button("Run Backtest"):
        with st.spinner("Running backtest..."):
            try:
                df = load_data(limit=1000)
                runner = BacktestRunner(initial_capital, commission)
                result = runner.run_backtest(strategy_class, df, strategy_params)
                
                # Display results
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Return", f"{result['total_return']:.2f}%")
                with col2:
                    st.metric("Sharpe Ratio", f"{result['metrics']['sharpe_ratio']:.2f}")
                with col3:
                    st.metric("Max Drawdown", f"{result['metrics']['max_drawdown']:.2f}%")
                with col4:
                    st.metric("Win Rate", f"{result['metrics']['win_rate']:.1f}%")
                
                # Detailed metrics
                st.subheader("Detailed Metrics")
                metrics_df = pd.DataFrame([result['metrics']]).T
                metrics_df.columns = ['Value']
                st.dataframe(metrics_df)
                
                # Store result in session state for comparison
                if 'backtest_results' not in st.session_state:
                    st.session_state.backtest_results = []
                st.session_state.backtest_results.append(result)
                
            except Exception as e:
                st.error(f"Backtest failed: {str(e)}")


def paper_trading_interface():
    """Interface for paper trading."""
    st.subheader("Paper Trading")
    
    # Initialize paper trader in session state
    if 'paper_trader' not in st.session_state:
        st.session_state.paper_trader = PaperTrader(initial_balance=10000)
    
    trader = st.session_state.paper_trader
    
    # Portfolio summary
    summary = trader.get_portfolio_summary()
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Balance", f"${summary['balance']:,.2f}")
    with col2:
        st.metric("Unrealized P&L", f"${summary['unrealized_pnl']:,.2f}")
    with col3:
        st.metric("Total Equity", f"${summary['total_equity']:,.2f}")
    with col4:
        st.metric("Total Return", f"{summary['total_return_pct']:.2f}%")
    
    # Order placement
    st.subheader("Place Order")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        side = st.selectbox("Side", ["BUY", "SELL"])
        order_type = st.selectbox("Type", ["MARKET", "LIMIT"])
    with col2:
        quantity = st.number_input("Quantity", value=0.01, min_value=0.001, step=0.001, format="%.3f")
        if order_type == "LIMIT":
            price = st.number_input("Price", value=50000.0, min_value=1.0)
        else:
            price = None
    with col3:
        if st.button("Place Order"):
            try:
                order_id = trader.create_order(
                    "BTCUSDT",
                    OrderSide.BUY if side == "BUY" else OrderSide.SELL,
                    OrderType.MARKET if order_type == "MARKET" else OrderType.LIMIT,
                    quantity,
                    price
                )
                st.success(f"Order placed: {order_id}")
                
                # Simulate market data to execute market orders
                if order_type == "MARKET":
                    df = load_data(limit=1)
                    if not df.empty:
                        current_price = df['close'].iloc[-1]
                        trader.process_market_data("BTCUSDT", current_price)
                        st.success("Market order executed")
                        
            except Exception as e:
                st.error(f"Order failed: {str(e)}")
    
    # Current positions
    positions = trader.get_positions()
    if positions:
        st.subheader("Current Positions")
        positions_df = pd.DataFrame(positions)
        st.dataframe(positions_df)
    
    # Recent orders
    orders = trader.get_orders()
    if orders:
        st.subheader("Recent Orders")
        orders_df = pd.DataFrame(orders)
        st.dataframe(orders_df.tail(10))


def main():
    """Main dashboard application."""
    st.title("₿ Bitcoin Trading Dashboard")
    st.markdown("Real-time analysis, backtesting, and paper trading for Bitcoin")
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox("Choose a page", [
        "Market Analysis", 
        "Backtesting", 
        "Paper Trading",
        "Strategy Comparison"
    ])
    
    # Data loading
    with st.spinner("Loading market data..."):
        df = load_data()
        if not df.empty:
            df = calculate_indicators(df)
    
    if df.empty:
        st.error("No data available. Please check your connection.")
        return
    
    # Page routing
    if page == "Market Analysis":
        st.header("Market Analysis")
        
        # Display current signals
        display_signals(df)
        
        # Price chart
        fig = create_price_chart(df)
        st.plotly_chart(fig, use_container_width=True)
        
        # Recent data table
        st.subheader("Recent Data")
        st.dataframe(df.tail(10))
        
    elif page == "Backtesting":
        run_backtest_interface()
        
    elif page == "Paper Trading":
        paper_trading_interface()
        
    elif page == "Strategy Comparison":
        st.header("Strategy Comparison")
        
        if 'backtest_results' in st.session_state and st.session_state.backtest_results:
            results_df = pd.DataFrame([
                {
                    'Strategy': r['strategy'],
                    'Total Return (%)': r['total_return'],
                    'Sharpe Ratio': r['metrics']['sharpe_ratio'],
                    'Max Drawdown (%)': r['metrics']['max_drawdown'],
                    'Win Rate (%)': r['metrics']['win_rate'],
                    'Total Trades': r['metrics']['total_trades']
                }
                for r in st.session_state.backtest_results
            ])
            
            st.dataframe(results_df)
            
            # Performance comparison chart
            fig = px.scatter(
                results_df, 
                x='Max Drawdown (%)', 
                y='Total Return (%)',
                size='Sharpe Ratio',
                color='Strategy',
                title="Risk vs Return Comparison"
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("Run some backtests first to see comparisons here.")
    
    # Footer
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Data Source:** Binance API")
    st.sidebar.markdown("**Update Frequency:** 5 minutes")
    st.sidebar.markdown(f"**Last Update:** {datetime.now().strftime('%H:%M:%S')}")


if __name__ == "__main__":
    main()
