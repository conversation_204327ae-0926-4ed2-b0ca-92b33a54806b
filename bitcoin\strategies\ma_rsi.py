"""
Moving Average + RSI Strategy for Backtrader.
Combines MA crossover with RSI confirmation for better entry/exit signals.
"""

import backtrader as bt
import pandas as pd
from typing import Dict, Any


class MARSIStrategy(bt.Strategy):
    """
    Strategy combining Moving Average crossover with RSI confirmation.
    
    Entry Rules:
    - Long: MA_Short > MA_Long AND RSI < 70 (not overbought)
    - Short: MA_Short < MA_Long AND RSI > 30 (not oversold)
    
    Exit Rules:
    - Stop Loss: ATR-based
    - Take Profit: 2x ATR
    - RSI extreme levels (< 30 for long exit, > 70 for short exit)
    """
    
    params = (
        ('ma_short', 10),
        ('ma_long', 50),
        ('rsi_period', 14),
        ('rsi_overbought', 70),
        ('rsi_oversold', 30),
        ('atr_period', 14),
        ('atr_stop_mult', 1.5),
        ('atr_target_mult', 3.0),
        ('risk_per_trade', 0.01),  # 1% risk per trade
        ('printlog', False),
    )
    
    def __init__(self):
        # Indicators
        self.ma_short = bt.indicators.SMA(self.data.close, period=self.params.ma_short)
        self.ma_long = bt.indicators.SMA(self.data.close, period=self.params.ma_long)
        self.rsi = bt.indicators.RSI(self.data.close, period=self.params.rsi_period)
        self.atr = bt.indicators.ATR(self.data, period=self.params.atr_period)
        
        # Signals
        self.ma_cross = bt.indicators.CrossOver(self.ma_short, self.ma_long)
        
        # Track orders and positions
        self.order = None
        self.stop_order = None
        self.target_order = None
        self.entry_price = None
        self.entry_bar = None
        
        # Performance tracking
        self.trades_count = 0
        self.wins = 0
        self.losses = 0
        
    def log(self, txt, dt=None):
        """Logging function for strategy."""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}: {txt}')
            
    def notify_order(self, order):
        """Handle order notifications."""
        if order.status in [order.Submitted, order.Accepted]:
            return
            
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'BUY EXECUTED: Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
                self.entry_price = order.executed.price
                self.entry_bar = len(self)
                
                # Set stop loss and take profit
                stop_price = self.entry_price - (self.params.atr_stop_mult * self.atr[0])
                target_price = self.entry_price + (self.params.atr_target_mult * self.atr[0])
                
                self.stop_order = self.sell(exectype=bt.Order.Stop, price=stop_price)
                self.target_order = self.sell(exectype=bt.Order.Limit, price=target_price)
                
            else:  # Sell
                self.log(f'SELL EXECUTED: Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
                
                # Calculate P&L
                if self.entry_price:
                    pnl = order.executed.price - self.entry_price
                    pnl_pct = (pnl / self.entry_price) * 100
                    self.log(f'TRADE P&L: {pnl:.2f} ({pnl_pct:.2f}%)')
                    
                    self.trades_count += 1
                    if pnl > 0:
                        self.wins += 1
                    else:
                        self.losses += 1
                
                # Cancel remaining orders
                if self.stop_order:
                    self.cancel(self.stop_order)
                if self.target_order:
                    self.cancel(self.target_order)
                    
                self.entry_price = None
                self.entry_bar = None
                
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')
            
        self.order = None
        
    def notify_trade(self, trade):
        """Handle trade notifications."""
        if not trade.isclosed:
            return
            
        self.log(f'TRADE PROFIT: Gross {trade.pnl:.2f}, Net {trade.pnlcomm:.2f}')
        
    def next(self):
        """Main strategy logic executed on each bar."""
        # Skip if we have pending orders
        if self.order:
            return
            
        # Check if we have enough data
        if len(self) < self.params.ma_long:
            return
            
        # Current values
        current_price = self.data.close[0]
        
        # Position sizing based on risk management
        if not self.position:
            # Entry conditions
            
            # Long entry: MA crossover up + RSI not overbought
            if (self.ma_cross[0] > 0 and 
                self.rsi[0] < self.params.rsi_overbought and
                self.rsi[0] > self.params.rsi_oversold):
                
                # Calculate position size based on ATR stop
                stop_distance = self.params.atr_stop_mult * self.atr[0]
                risk_amount = self.broker.getcash() * self.params.risk_per_trade
                size = risk_amount / stop_distance
                
                # Ensure we don't exceed available cash
                max_size = self.broker.getcash() / current_price * 0.95  # 95% of available cash
                size = min(size, max_size)
                
                if size > 0:
                    self.log(f'BUY CREATE: Price {current_price:.2f}, Size {size:.2f}')
                    self.order = self.buy(size=size)
                    
        else:
            # Exit conditions for long positions
            
            # RSI overbought exit
            if self.rsi[0] > self.params.rsi_overbought:
                self.log(f'SELL CREATE (RSI Overbought): Price {current_price:.2f}')
                self.order = self.sell()
                
            # MA crossover down exit
            elif self.ma_cross[0] < 0:
                self.log(f'SELL CREATE (MA Cross Down): Price {current_price:.2f}')
                self.order = self.sell()
                
    def stop(self):
        """Called when strategy finishes."""
        win_rate = (self.wins / self.trades_count * 100) if self.trades_count > 0 else 0
        
        self.log(f'Strategy Results:')
        self.log(f'Total Trades: {self.trades_count}')
        self.log(f'Wins: {self.wins}, Losses: {self.losses}')
        self.log(f'Win Rate: {win_rate:.1f}%')
        self.log(f'Final Portfolio Value: {self.broker.getvalue():.2f}')


class SuperTrendStrategy(bt.Strategy):
    """
    SuperTrend-based strategy with clear entry/exit signals.
    """
    
    params = (
        ('atr_period', 10),
        ('atr_multiplier', 3.0),
        ('risk_per_trade', 0.01),
        ('printlog', False),
    )
    
    def __init__(self):
        # We'll need to implement SuperTrend as a custom indicator
        # or calculate it in the strategy
        self.atr = bt.indicators.ATR(self.data, period=self.params.atr_period)
        
        # Track state
        self.order = None
        self.supertrend_direction = 1  # 1 = bullish, -1 = bearish
        self.supertrend_value = None
        
    def next(self):
        """SuperTrend strategy logic."""
        if self.order:
            return
            
        # Calculate SuperTrend (simplified version)
        hl2 = (self.data.high[0] + self.data.low[0]) / 2
        upper_band = hl2 + (self.params.atr_multiplier * self.atr[0])
        lower_band = hl2 - (self.params.atr_multiplier * self.atr[0])
        
        # Determine trend direction
        if self.data.close[0] > upper_band:
            new_direction = -1  # Bearish
            self.supertrend_value = upper_band
        elif self.data.close[0] < lower_band:
            new_direction = 1   # Bullish
            self.supertrend_value = lower_band
        else:
            new_direction = self.supertrend_direction
            
        # Check for trend change (signal)
        if new_direction != self.supertrend_direction:
            if new_direction == 1 and not self.position:
                # Buy signal
                size = self.broker.getcash() / self.data.close[0] * 0.95
                self.order = self.buy(size=size)
                self.log(f'BUY SIGNAL: SuperTrend bullish at {self.data.close[0]:.2f}')
                
            elif new_direction == -1 and self.position:
                # Sell signal
                self.order = self.sell()
                self.log(f'SELL SIGNAL: SuperTrend bearish at {self.data.close[0]:.2f}')
                
        self.supertrend_direction = new_direction
        
    def log(self, txt, dt=None):
        """Logging function."""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}: {txt}')


def get_strategy_params() -> Dict[str, Dict[str, Any]]:
    """Return default parameters for all strategies."""
    return {
        'MARSIStrategy': {
            'ma_short': 10,
            'ma_long': 50,
            'rsi_period': 14,
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'atr_period': 14,
            'atr_stop_mult': 1.5,
            'atr_target_mult': 3.0,
            'risk_per_trade': 0.01,
        },
        'SuperTrendStrategy': {
            'atr_period': 10,
            'atr_multiplier': 3.0,
            'risk_per_trade': 0.01,
        }
    }
