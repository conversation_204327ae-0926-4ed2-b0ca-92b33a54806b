"""
Simple market regime detection that works reliably.
"""

import pandas as pd
import numpy as np
from typing import Dict
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.client import BinanceDataClient


def simple_regime_detection(symbol: str = "BTCUSDT") -> str:
    """Simple regime detection using moving averages and volatility."""
    client = BinanceDataClient()
    
    # Load data
    df = client.get_candles(symbol, limit=1000)
    
    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines(symbol, "1h", 1000)
        client.store_candles(df, symbol)
        df = client.get_candles(symbol, limit=1000)
    
    if df.empty:
        return "❌ No data available for regime detection"
    
    # Calculate indicators
    df['returns'] = df['close'].pct_change()
    df['ma_20'] = df['close'].rolling(20).mean()
    df['ma_50'] = df['close'].rolling(50).mean()
    df['volatility'] = df['returns'].rolling(20).std()
    
    # Current values
    latest = df.iloc[-1]
    price = latest['close']
    ma_20 = latest['ma_20']
    ma_50 = latest['ma_50']
    volatility = latest['volatility']
    
    # Recent performance
    recent_return = (price - df['close'].iloc[-20]) / df['close'].iloc[-20] * 100
    
    # Regime classification
    if pd.notna(ma_20) and pd.notna(ma_50):
        if ma_20 > ma_50 * 1.02 and recent_return > 5:
            regime = "Bull Market"
            confidence = "High" if volatility < 0.03 else "Medium"
            implications = [
                "• Favor long positions and momentum strategies",
                "• Consider trend-following indicators",
                "• Reduce position sizing during high volatility"
            ]
        elif ma_20 < ma_50 * 0.98 and recent_return < -5:
            regime = "Bear Market"
            confidence = "High" if volatility > 0.04 else "Medium"
            implications = [
                "• Consider defensive positioning",
                "• Short-term mean reversion strategies",
                "• Increase cash allocation"
            ]
        else:
            regime = "Sideways Market"
            confidence = "Medium"
            implications = [
                "• Range-bound trading strategies",
                "• Mean reversion approaches",
                "• Reduced position sizes"
            ]
    else:
        regime = "Unknown"
        confidence = "Low"
        implications = ["• Insufficient data for analysis"]
    
    # Volatility regime
    vol_percentile = df['volatility'].rank(pct=True).iloc[-1] * 100
    if vol_percentile > 80:
        vol_regime = "High Volatility"
    elif vol_percentile < 20:
        vol_regime = "Low Volatility"
    else:
        vol_regime = "Normal Volatility"
    
    # Generate report
    report = f"""
🔍 SIMPLE REGIME DETECTION REPORT
{'='*50}

Symbol: {symbol}
Analysis Date: {latest.name.strftime('%Y-%m-%d %H:%M:%S')}
Current Price: ${price:,.2f}

📊 MARKET REGIME ANALYSIS
{'-'*30}
Current Regime: {regime}
Confidence: {confidence}
Volatility Regime: {vol_regime}

📈 KEY METRICS
{'-'*30}
MA 20: ${ma_20:,.2f}
MA 50: ${ma_50:,.2f}
Recent Return (20 periods): {recent_return:+.2f}%
Current Volatility: {volatility*100:.2f}%
Volatility Percentile: {vol_percentile:.0f}%

💡 TRADING IMPLICATIONS
{'-'*30}
"""
    
    for implication in implications:
        report += f"{implication}\n"
    
    # Risk assessment
    if regime == "Bull Market" and vol_regime == "Low Volatility":
        risk_level = "Low"
    elif regime == "Bear Market" or vol_regime == "High Volatility":
        risk_level = "High"
    else:
        risk_level = "Medium"
    
    report += f"""
⚠️ RISK ASSESSMENT
{'-'*30}
Current Risk Level: {risk_level}

🎯 RECOMMENDATIONS
{'-'*30}
"""
    
    if risk_level == "Low":
        report += "• Consider increasing position sizes\n"
        report += "• Good environment for trend following\n"
    elif risk_level == "High":
        report += "• Reduce position sizes\n"
        report += "• Implement strict stop losses\n"
        report += "• Consider defensive strategies\n"
    else:
        report += "• Maintain moderate position sizes\n"
        report += "• Use balanced approach\n"
    
    report += f"""

⚠️ DISCLAIMER
{'-'*30}
This analysis is based on simple technical indicators and historical patterns.
Market conditions can change rapidly. Use as one factor among many in trading decisions.
Not financial advice. Always do your own research.
"""
    
    return report


def main():
    """Run simple regime detection."""
    try:
        report = simple_regime_detection("BTCUSDT")
        print(report)
        
        # Save to file
        filename = f"simple_regime_analysis_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")
        
    except Exception as e:
        print(f"❌ Regime detection failed: {e}")


if __name__ == "__main__":
    main()
