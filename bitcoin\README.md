# ₿ ZeroTradePro

> **Sistema completo de análise técnica, backtesting e paper trading para Bitcoin com custo zero**

[![CI](https://github.com/user/bitcoin-signals/workflows/Bitcoin%20Trading%20Signals%20CI/badge.svg)](https://github.com/user/bitcoin-signals/actions)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 🚀 Características Principais

### ✅ Implementado (Semana 1 - Data & Infra)
- **WebSocket Binance** → Dados em tempo real com reconexão automática
- **SQLite Database** → Armazenamento local com deduplicação
- **Indicadores Avançados** → MA, RSI, SuperTrend, ADX, VWAP, Bollinger Bands
- **Backtesting Profissional** → Engine completo com Backtrader
- **Gestão de Risco** → Position sizing, ATR stops, Kelly Criterion
- **Paper Trading** → Simulação realista com slippage e comissões
- **Dashboard Streamlit** → Interface web interativa
- **Testes Unitários** → Cobertura completa com pytest
- **CI/CD GitHub Actions** → Automação e sinais diários

### 🎯 Próximas Semanas
- **Semana 2**: Otimização de estratégias e relatórios HTML
- **Semana 3**: Modelos ML aprimorados e validação cruzada
- **Semana 4**: Live trading e monitoramento avançado

## 📊 Arquitetura do Sistema

```mermaid
graph TB
    A[Binance WebSocket] --> B[Data Client]
    B --> C[SQLite Database]
    C --> D[Technical Indicators]
    D --> E[Trading Strategies]
    E --> F[Backtest Engine]
    E --> G[Paper Trader]
    F --> H[Risk Manager]
    G --> H
    H --> I[Streamlit Dashboard]
    I --> J[Discord Alerts]
    
    subgraph "Core Modules"
        B
        D
        E
        F
        G
        H
    end
    
    subgraph "Interfaces"
        I
        J
    end
```

## 🛠️ Instalação Rápida

```bash
# Clone o repositório
git clone https://github.com/user/bitcoin-signals.git
cd bitcoin-signals/bitcoin

# Instale dependências
pip install -r requirement.txt

# Execute testes
pytest tests/ -v

# Inicie o dashboard
streamlit run app.py
```

## 📈 Uso Básico

### 1. Coleta de Dados
```python
from data.client import BinanceDataClient

# Inicializar cliente
client = BinanceDataClient()

# Buscar dados históricos
df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
client.store_candles(df, "BTCUSDT")

# Iniciar WebSocket para dados em tempo real
client.start_websocket("btcusdt", "1h")
```

### 2. Análise Técnica
```python
from indicators.ta_wrappers import add_all_indicators, get_trading_signals

# Adicionar todos os indicadores
df = add_all_indicators(df)

# Gerar sinais de trading
signals = get_trading_signals(df)
print(signals.tail())
```

### 3. Backtesting
```python
from backtest.runner import BacktestRunner
from strategies.ma_rsi import MARSIStrategy

# Configurar backtest
runner = BacktestRunner(initial_cash=10000, commission=0.001)

# Executar estratégia
result = runner.run_backtest(
    MARSIStrategy, 
    df,
    strategy_params={'ma_short': 10, 'ma_long': 50}
)

print(f"Retorno Total: {result['total_return']:.2f}%")
print(f"Sharpe Ratio: {result['metrics']['sharpe_ratio']:.2f}")
```

### 4. Paper Trading
```python
from execution.paper_trader import PaperTrader, OrderSide, OrderType

# Inicializar paper trader
trader = PaperTrader(initial_balance=10000)

# Criar ordem
order_id = trader.create_order(
    "BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1
)

# Simular dados de mercado
trader.process_market_data("BTCUSDT", 50000)

# Ver portfolio
summary = trader.get_portfolio_summary()
print(summary)
```

## 📊 Dashboard Streamlit

Execute `streamlit run app.py` para acessar:

- **Market Analysis**: Gráficos interativos com indicadores
- **Backtesting**: Interface para testar estratégias
- **Paper Trading**: Simulação de trading em tempo real
- **Strategy Comparison**: Comparação de performance

## 🧪 Estratégias Implementadas

### 1. MA + RSI Strategy
- **Entrada**: Cruzamento de médias móveis + confirmação RSI
- **Saída**: Stop ATR-based ou reversão de sinal
- **Gestão de Risco**: 1% do capital por trade

### 2. SuperTrend Strategy
- **Entrada**: Mudança de direção do SuperTrend
- **Saída**: Reversão do SuperTrend
- **Gestão de Risco**: ATR-based position sizing

## 📊 Métricas de Performance

O sistema calcula automaticamente:

- **Retorno Total** e **CAGR**
- **Sharpe Ratio** e **Sortino Ratio**
- **Maximum Drawdown**
- **Win Rate** e **Profit Factor**
- **Expectancy** e **SQN**

## 🔧 Configuração Avançada

### Variáveis de Ambiente
```bash
# Discord webhook para alertas
export DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/..."

# Chaves API Binance (opcional)
export BINANCE_API_KEY="your_api_key"
export BINANCE_SECRET_KEY="your_secret_key"
```

### Configuração de Risco
```python
from risk.position_sizing import RiskManager

risk_mgr = RiskManager(
    max_risk_per_trade=0.02,  # 2% por trade
    max_portfolio_risk=0.10,  # 10% portfolio
    max_drawdown_limit=0.15   # 15% stop trading
)
```

## 🧪 Testes

```bash
# Executar todos os testes
pytest tests/ -v --cov=.

# Testes específicos
pytest tests/test_data_client.py -v
pytest tests/test_indicators.py -v

# Relatório de cobertura
pytest --cov=. --cov-report=html
```

## 📈 Resultados de Exemplo

### Backtest MA+RSI (1000 candles)
- **Retorno Total**: +15.3%
- **Sharpe Ratio**: 1.42
- **Max Drawdown**: -8.7%
- **Win Rate**: 58.3%
- **Total Trades**: 24

### SuperTrend Strategy
- **Retorno Total**: +22.1%
- **Sharpe Ratio**: 1.67
- **Max Drawdown**: -12.4%
- **Win Rate**: 52.1%
- **Total Trades**: 18

## 🚨 Alertas Automáticos

O sistema envia alertas via Discord com:
- Sinais de compra/venda
- Níveis de RSI extremos
- Mudanças de tendência SuperTrend
- Resumo diário de performance

## 🔄 CI/CD Pipeline

GitHub Actions automaticamente:
- ✅ Executa testes em Python 3.9, 3.10, 3.11
- ✅ Verifica qualidade do código (flake8, black)
- ✅ Gera sinais diários às 9h UTC
- ✅ Envia alertas para Discord
- ✅ Verifica vulnerabilidades de segurança

## 📚 Estrutura do Projeto

```
bitcoin/
├── data/                 # Coleta e armazenamento
│   ├── client.py        # WebSocket + SQLite
│   └── schema.sql       # Database schema
├── indicators/          # Análise técnica
│   └── ta_wrappers.py   # Indicadores + sinais
├── strategies/          # Estratégias de trading
│   └── ma_rsi.py        # MA+RSI, SuperTrend
├── backtest/           # Engine de backtesting
│   └── runner.py        # Backtrader integration
├── risk/               # Gestão de risco
│   └── position_sizing.py # Kelly, ATR, etc.
├── execution/          # Paper/Live trading
│   └── paper_trader.py  # Simulação realista
├── tests/              # Testes unitários
├── app.py              # Dashboard Streamlit
└── requirement.txt     # Dependências
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-estrategia`)
3. Commit suas mudanças (`git commit -am 'Add: nova estratégia'`)
4. Push para a branch (`git push origin feature/nova-estrategia`)
5. Abra um Pull Request

## ⚠️ Disclaimer

Este sistema é para fins **educacionais e de pesquisa**. Não constitui aconselhamento financeiro. Trading de criptomoedas envolve riscos significativos. Sempre faça sua própria pesquisa e considere consultar um consultor financeiro qualificado.

## 📄 Licença

MIT License - veja [LICENSE](LICENSE) para detalhes.

---

**Desenvolvido com ❤️ para a comunidade de trading algorítmico**
