"""
Backtest runner using Backtrader framework.
Provides comprehensive backtesting with proper metrics and risk management.
"""

import backtrader as bt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import sqlite3
import os
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.ma_rsi import MARSIStrategy, SuperTrendStrategy
from data.client import BinanceDataClient


class BacktestRunner:
    """
    Comprehensive backtesting engine with proper metrics and reporting.
    """

    def __init__(self, initial_cash: float = 10000.0, commission: float = 0.001):
        self.initial_cash = initial_cash
        self.commission = commission
        self.results = {}

    def prepare_data(self, df: pd.DataFrame) -> bt.feeds.PandasData:
        """
        Prepare pandas DataFrame for Backtrader.
        """
        # Ensure required columns exist
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"Missing required column: {col}")

        # Ensure datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("DataFrame must have datetime index")

        # Create Backtrader data feed
        data = bt.feeds.PandasData(
            dataname=df,
            datetime=None,  # Use index
            open='open',
            high='high',
            low='low',
            close='close',
            volume='volume',
            openinterest=None
        )

        return data

    def run_backtest(self,
                    strategy_class: bt.Strategy,
                    data: pd.DataFrame,
                    strategy_params: Dict[str, Any] = None,
                    start_date: str = None,
                    end_date: str = None) -> Dict[str, Any]:
        """
        Run backtest for a given strategy and data.
        """
        # Filter data by date range if specified
        if start_date or end_date:
            if start_date:
                data = data[data.index >= start_date]
            if end_date:
                data = data[data.index <= end_date]

        if len(data) < 100:
            raise ValueError("Insufficient data for backtesting (minimum 100 bars)")

        # Initialize Cerebro
        cerebro = bt.Cerebro()

        # Add strategy
        if strategy_params:
            cerebro.addstrategy(strategy_class, **strategy_params)
        else:
            cerebro.addstrategy(strategy_class)

        # Add data
        bt_data = self.prepare_data(data)
        cerebro.adddata(bt_data)

        # Set initial cash
        cerebro.broker.setcash(self.initial_cash)

        # Set commission
        cerebro.broker.setcommission(commission=self.commission)

        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.TimeReturn, _name='timereturn')

        # Run backtest
        print(f"Starting backtest with {len(data)} bars...")
        print(f"Initial Portfolio Value: ${self.initial_cash:,.2f}")

        results = cerebro.run()
        strategy_result = results[0]

        final_value = cerebro.broker.getvalue()
        total_return = (final_value - self.initial_cash) / self.initial_cash * 100

        print(f"Final Portfolio Value: ${final_value:,.2f}")
        print(f"Total Return: {total_return:.2f}%")

        # Extract metrics
        metrics = self._extract_metrics(strategy_result, data, final_value)

        # Store results
        result = {
            'strategy': strategy_class.__name__,
            'initial_cash': self.initial_cash,
            'final_value': final_value,
            'total_return': total_return,
            'metrics': metrics,
            'data_period': {
                'start': data.index[0].strftime('%Y-%m-%d'),
                'end': data.index[-1].strftime('%Y-%m-%d'),
                'bars': len(data)
            },
            'strategy_params': strategy_params or {}
        }

        return result

    def _extract_metrics(self, strategy_result, data: pd.DataFrame, final_value: float) -> Dict[str, Any]:
        """Extract comprehensive metrics from backtest results."""
        metrics = {}

        # Sharpe Ratio
        sharpe = strategy_result.analyzers.sharpe.get_analysis()
        metrics['sharpe_ratio'] = sharpe.get('sharperatio', 0)

        # Drawdown
        drawdown = strategy_result.analyzers.drawdown.get_analysis()
        metrics['max_drawdown'] = drawdown.get('max', {}).get('drawdown', 0)
        metrics['max_drawdown_period'] = drawdown.get('max', {}).get('len', 0)

        # Trade Analysis
        trades = strategy_result.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)

        metrics['total_trades'] = total_trades
        metrics['won_trades'] = won_trades
        metrics['lost_trades'] = lost_trades
        metrics['win_rate'] = (won_trades / total_trades * 100) if total_trades > 0 else 0

        # Profit/Loss metrics
        if total_trades > 0:
            avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
            avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
            metrics['avg_win'] = avg_win
            metrics['avg_loss'] = abs(avg_loss)
            metrics['profit_factor'] = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # Expectancy
            win_rate = metrics['win_rate'] / 100
            loss_rate = 1 - win_rate
            metrics['expectancy'] = (win_rate * avg_win) - (loss_rate * abs(avg_loss))
        else:
            metrics.update({
                'avg_win': 0, 'avg_loss': 0, 'profit_factor': 0, 'expectancy': 0
            })

        # SQN (System Quality Number)
        sqn = strategy_result.analyzers.sqn.get_analysis()
        metrics['sqn'] = sqn.get('sqn', 0)

        # Time-based metrics
        returns = strategy_result.analyzers.returns.get_analysis()
        metrics['annual_return'] = returns.get('rnorm100', 0)

        # Calculate additional metrics
        trading_days = len(data)
        years = trading_days / 365.25

        if years > 0:
            cagr = (final_value / self.initial_cash) ** (1/years) - 1
            metrics['cagr'] = cagr * 100
        else:
            metrics['cagr'] = 0

        # Sortino Ratio (downside deviation)
        timereturn = strategy_result.analyzers.timereturn.get_analysis()
        if timereturn:
            returns_series = pd.Series(list(timereturn.values()))
            downside_returns = returns_series[returns_series < 0]
            if len(downside_returns) > 0:
                downside_std = downside_returns.std()
                metrics['sortino_ratio'] = returns_series.mean() / downside_std if downside_std > 0 else 0
            else:
                metrics['sortino_ratio'] = float('inf')
        else:
            metrics['sortino_ratio'] = 0

        return metrics

    def compare_strategies(self,
                          strategies: List[Tuple[bt.Strategy, Dict[str, Any]]],
                          data: pd.DataFrame) -> pd.DataFrame:
        """
        Compare multiple strategies on the same dataset.
        """
        results = []

        for strategy_class, params in strategies:
            try:
                result = self.run_backtest(strategy_class, data, params)

                # Flatten result for comparison
                flat_result = {
                    'Strategy': result['strategy'],
                    'Total Return (%)': result['total_return'],
                    'Sharpe Ratio': result['metrics']['sharpe_ratio'],
                    'Max Drawdown (%)': result['metrics']['max_drawdown'],
                    'Win Rate (%)': result['metrics']['win_rate'],
                    'Total Trades': result['metrics']['total_trades'],
                    'Profit Factor': result['metrics']['profit_factor'],
                    'CAGR (%)': result['metrics']['cagr'],
                    'Sortino Ratio': result['metrics']['sortino_ratio'],
                    'SQN': result['metrics']['sqn']
                }

                results.append(flat_result)

            except Exception as e:
                print(f"Error running {strategy_class.__name__}: {e}")
                continue

        return pd.DataFrame(results)

    def generate_report(self, result: Dict[str, Any], save_path: str = None) -> str:
        """
        Generate HTML report for backtest results.
        """
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Backtest Report - {strategy}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-box {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #007acc; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #007acc; }}
                .metric-label {{ font-size: 14px; color: #666; }}
                .positive {{ color: #28a745; }}
                .negative {{ color: #dc3545; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f0f0f0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Backtest Report: {strategy}</h1>
                <p><strong>Period:</strong> {start_date} to {end_date} ({bars} bars)</p>
                <p><strong>Initial Capital:</strong> ${initial_cash:,.2f}</p>
                <p><strong>Final Value:</strong> ${final_value:,.2f}</p>
                <p><strong>Total Return:</strong> <span class="{return_class}">{total_return:.2f}%</span></p>
            </div>

            <div class="metrics">
                <div class="metric-box">
                    <div class="metric-value">{sharpe:.2f}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
                <div class="metric-box">
                    <div class="metric-value negative">{max_drawdown:.2f}%</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-box">
                    <div class="metric-value">{win_rate:.1f}%</div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-box">
                    <div class="metric-value">{total_trades}</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-box">
                    <div class="metric-value">{profit_factor:.2f}</div>
                    <div class="metric-label">Profit Factor</div>
                </div>
                <div class="metric-box">
                    <div class="metric-value">{cagr:.2f}%</div>
                    <div class="metric-label">CAGR</div>
                </div>
            </div>

            <h2>Strategy Parameters</h2>
            <table>
                <tr><th>Parameter</th><th>Value</th></tr>
                {params_rows}
            </table>

            <h2>Detailed Metrics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                {metrics_rows}
            </table>
        </body>
        </html>
        """

        # Prepare data for template
        metrics = result['metrics']
        params = result['strategy_params']

        # Generate parameter rows
        params_rows = ""
        for key, value in params.items():
            params_rows += f"<tr><td>{key}</td><td>{value}</td></tr>"

        # Generate metrics rows
        metrics_rows = ""
        for key, value in metrics.items():
            if isinstance(value, float):
                formatted_value = f"{value:.4f}"
            else:
                formatted_value = str(value)
            metrics_rows += f"<tr><td>{key.replace('_', ' ').title()}</td><td>{formatted_value}</td></tr>"

        # Fill template with safe formatting
        def safe_format(value, default="N/A"):
            """Safely format values, handling None and complex numbers."""
            if value is None:
                return default
            if isinstance(value, complex):
                return f"{value.real:.2f}"
            if isinstance(value, (int, float)):
                return f"{value:.2f}" if isinstance(value, float) else str(value)
            return str(value)

        html_content = html_template.format(
            strategy=result['strategy'],
            start_date=result['data_period']['start'],
            end_date=result['data_period']['end'],
            bars=result['data_period']['bars'],
            initial_cash=result['initial_cash'],
            final_value=result['final_value'],
            total_return=result['total_return'],
            return_class='positive' if result['total_return'] > 0 else 'negative',
            sharpe=safe_format(metrics.get('sharpe_ratio')),
            max_drawdown=safe_format(metrics.get('max_drawdown')),
            win_rate=safe_format(metrics.get('win_rate')),
            total_trades=safe_format(metrics.get('total_trades')),
            profit_factor=safe_format(metrics.get('profit_factor')),
            cagr=safe_format(metrics.get('cagr')),
            params_rows=params_rows,
            metrics_rows=metrics_rows
        )

        # Save to file if path provided
        if save_path:
            with open(save_path, 'w') as f:
                f.write(html_content)
            print(f"Report saved to: {save_path}")

        return html_content


def main():
    """Example usage of BacktestRunner."""
    # Initialize data client and fetch data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=1000)

    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=1000)

    print(f"Loaded {len(df)} candles for backtesting")

    # Initialize backtest runner
    runner = BacktestRunner(initial_cash=10000, commission=0.001)

    # Run single strategy backtest
    result = runner.run_backtest(
        MARSIStrategy,
        df,
        strategy_params={'printlog': True}
    )

    print("\n" + "="*50)
    print("BACKTEST RESULTS")
    print("="*50)
    print(f"Strategy: {result['strategy']}")
    print(f"Total Return: {result['total_return']:.2f}%")
    print(f"Sharpe Ratio: {result['metrics']['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {result['metrics']['max_drawdown']:.2f}%")
    print(f"Win Rate: {result['metrics']['win_rate']:.1f}%")
    print(f"Total Trades: {result['metrics']['total_trades']}")

    # Generate HTML report
    report_path = "backtest_report.html"
    runner.generate_report(result, report_path)

    # Compare multiple strategies
    strategies = [
        (MARSIStrategy, {'ma_short': 10, 'ma_long': 50}),
        (MARSIStrategy, {'ma_short': 5, 'ma_long': 20}),
        (SuperTrendStrategy, {'atr_period': 10, 'atr_multiplier': 3.0})
    ]

    comparison = runner.compare_strategies(strategies, df)
    print("\n" + "="*50)
    print("STRATEGY COMPARISON")
    print("="*50)
    print(comparison.to_string(index=False))


if __name__ == "__main__":
    main()
