app:
  debug: false
  name: Bitcoin Trading System
  version: 1.0.0
database:
  max_overflow: 20
  pool_size: 10
  url: sqlite:///data/trading.db
ml:
  cross_validation_folds: 5
  feature_selection_threshold: 0.01
  model_retrain_interval: 24
notifications:
  channels:
  - slack
  - email
  enabled: true
  rate_limit_per_minute: 10
security:
  api_rate_limit: 1000
  jwt_expiration_hours: 24
  max_login_attempts: 5
trading:
  default_symbol: BTCUSDT
  default_timeframe: 1h
  enable_live_trading: false
  max_positions: 5
  risk_per_trade: 0.02
