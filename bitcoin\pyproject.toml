[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".env"
]

[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "bitcoin-trading-signals"
description = "Professional Bitcoin trading signals system with backtesting and risk management"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Bitcoin Signals Team", email = "<EMAIL>"},
]
keywords = ["bitcoin", "trading", "signals", "backtesting", "cryptocurrency"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Investment",
]
dynamic = ["version"]

dependencies = [
    "requests>=2.25.1",
    "pandas>=1.2.0",
    "matplotlib>=3.3.0",
    "numpy>=1.19.5",
    "scikit-learn>=0.24.0",
    "joblib>=1.0.0",
    "tensorflow>=2.4.0",
    "torch>=1.7.0",
    "python-binance>=1.0.16",
    "ta>=0.10.2",
    "pandas-ta>=0.3.14b",
    "backtrader>=1.9.78.123",
    "vectorbt>=0.25.2",
    "streamlit>=1.28.0",
    "plotly>=5.17.0",
    "websocket-client>=1.6.0",
    "python-socketio>=5.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.23.0",
]

[project.urls]
Homepage = "https://github.com/user/bitcoin-signals"
Documentation = "https://user.github.io/bitcoin-signals"
Repository = "https://github.com/user/bitcoin-signals.git"
"Bug Tracker" = "https://github.com/user/bitcoin-signals/issues"

[project.scripts]
bitcoin-signals = "main:main"

[tool.setuptools_scm]
write_to = "_version.py"
