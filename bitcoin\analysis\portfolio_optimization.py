"""
Portfolio optimization using Modern Portfolio Theory and advanced techniques.
Implements mean-variance optimization, risk parity, and Black-Litterman models.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from scipy.optimize import minimize
    from scipy import linalg
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("SciPy not available. Install with: pip install scipy")

import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


class PortfolioOptimizer:
    """
    Advanced portfolio optimization with multiple strategies.
    """
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize portfolio optimizer.
        
        Args:
            risk_free_rate: Annual risk-free rate
        """
        self.risk_free_rate = risk_free_rate
        self.returns_data = None
        self.cov_matrix = None
        self.expected_returns = None
        
    def prepare_returns_data(self, price_data: Dict[str, pd.Series]) -> pd.DataFrame:
        """Prepare returns data from price series."""
        returns_df = pd.DataFrame()
        
        for asset, prices in price_data.items():
            returns = prices.pct_change().dropna()
            returns_df[asset] = returns
        
        # Remove any remaining NaN values
        returns_df = returns_df.dropna()
        
        self.returns_data = returns_df
        self.cov_matrix = returns_df.cov() * 252  # Annualized
        self.expected_returns = returns_df.mean() * 252  # Annualized
        
        return returns_df
    
    def calculate_portfolio_metrics(self, weights: np.ndarray) -> Dict[str, float]:
        """Calculate portfolio return, volatility, and Sharpe ratio."""
        if self.expected_returns is None or self.cov_matrix is None:
            raise ValueError("Returns data not prepared. Call prepare_returns_data first.")
        
        portfolio_return = np.sum(weights * self.expected_returns)
        portfolio_volatility = np.sqrt(np.dot(weights.T, np.dot(self.cov_matrix, weights)))
        sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
        
        return {
            'return': portfolio_return,
            'volatility': portfolio_volatility,
            'sharpe_ratio': sharpe_ratio
        }
    
    def mean_variance_optimization(self, 
                                 target_return: Optional[float] = None,
                                 target_volatility: Optional[float] = None) -> Dict[str, Any]:
        """
        Perform mean-variance optimization.
        
        Args:
            target_return: Target portfolio return (if None, maximize Sharpe ratio)
            target_volatility: Target portfolio volatility
        """
        if not SCIPY_AVAILABLE:
            return {"error": "SciPy not available"}
        
        n_assets = len(self.expected_returns)
        
        # Constraints
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]  # Weights sum to 1
        
        if target_return is not None:
            constraints.append({
                'type': 'eq', 
                'fun': lambda x: np.sum(x * self.expected_returns) - target_return
            })
        
        if target_volatility is not None:
            constraints.append({
                'type': 'eq',
                'fun': lambda x: np.sqrt(np.dot(x.T, np.dot(self.cov_matrix, x))) - target_volatility
            })
        
        # Bounds (no short selling)
        bounds = tuple((0, 1) for _ in range(n_assets))
        
        # Initial guess
        x0 = np.array([1/n_assets] * n_assets)
        
        if target_return is None and target_volatility is None:
            # Maximize Sharpe ratio
            def objective(x):
                metrics = self.calculate_portfolio_metrics(x)
                return -metrics['sharpe_ratio']  # Negative for minimization
        else:
            # Minimize volatility
            def objective(x):
                return np.sqrt(np.dot(x.T, np.dot(self.cov_matrix, x)))
        
        # Optimize
        result = minimize(
            objective, x0, method='SLSQP',
            bounds=bounds, constraints=constraints
        )
        
        if result.success:
            optimal_weights = result.x
            metrics = self.calculate_portfolio_metrics(optimal_weights)
            
            return {
                'weights': dict(zip(self.expected_returns.index, optimal_weights)),
                'metrics': metrics,
                'optimization_result': result
            }
        else:
            return {"error": f"Optimization failed: {result.message}"}
    
    def risk_parity_optimization(self) -> Dict[str, Any]:
        """
        Implement risk parity (equal risk contribution) optimization.
        """
        if not SCIPY_AVAILABLE:
            return {"error": "SciPy not available"}
        
        n_assets = len(self.expected_returns)
        
        def risk_budget_objective(weights):
            """Objective function for risk parity."""
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(self.cov_matrix, weights)))
            marginal_contrib = np.dot(self.cov_matrix, weights) / portfolio_vol
            contrib = weights * marginal_contrib
            
            # Target equal risk contribution
            target_contrib = portfolio_vol / n_assets
            return np.sum((contrib - target_contrib) ** 2)
        
        # Constraints and bounds
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
        bounds = tuple((0.01, 1) for _ in range(n_assets))  # Minimum 1% allocation
        
        # Initial guess
        x0 = np.array([1/n_assets] * n_assets)
        
        # Optimize
        result = minimize(
            risk_budget_objective, x0, method='SLSQP',
            bounds=bounds, constraints=constraints
        )
        
        if result.success:
            optimal_weights = result.x
            metrics = self.calculate_portfolio_metrics(optimal_weights)
            
            return {
                'weights': dict(zip(self.expected_returns.index, optimal_weights)),
                'metrics': metrics,
                'optimization_result': result
            }
        else:
            return {"error": f"Risk parity optimization failed: {result.message}"}
    
    def minimum_variance_optimization(self) -> Dict[str, Any]:
        """Find minimum variance portfolio."""
        if not SCIPY_AVAILABLE:
            return {"error": "SciPy not available"}
        
        n_assets = len(self.expected_returns)
        
        # Objective: minimize portfolio variance
        def objective(x):
            return np.dot(x.T, np.dot(self.cov_matrix, x))
        
        # Constraints and bounds
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
        bounds = tuple((0, 1) for _ in range(n_assets))
        
        # Initial guess
        x0 = np.array([1/n_assets] * n_assets)
        
        # Optimize
        result = minimize(
            objective, x0, method='SLSQP',
            bounds=bounds, constraints=constraints
        )
        
        if result.success:
            optimal_weights = result.x
            metrics = self.calculate_portfolio_metrics(optimal_weights)
            
            return {
                'weights': dict(zip(self.expected_returns.index, optimal_weights)),
                'metrics': metrics,
                'optimization_result': result
            }
        else:
            return {"error": f"Minimum variance optimization failed: {result.message}"}
    
    def efficient_frontier(self, n_points: int = 50) -> Dict[str, Any]:
        """Generate efficient frontier."""
        if not SCIPY_AVAILABLE:
            return {"error": "SciPy not available"}
        
        min_ret = self.expected_returns.min()
        max_ret = self.expected_returns.max()
        target_returns = np.linspace(min_ret, max_ret, n_points)
        
        efficient_portfolios = []
        
        for target_ret in target_returns:
            try:
                result = self.mean_variance_optimization(target_return=target_ret)
                if 'error' not in result:
                    efficient_portfolios.append({
                        'target_return': target_ret,
                        'weights': result['weights'],
                        'metrics': result['metrics']
                    })
            except:
                continue
        
        if efficient_portfolios:
            returns = [p['metrics']['return'] for p in efficient_portfolios]
            volatilities = [p['metrics']['volatility'] for p in efficient_portfolios]
            sharpe_ratios = [p['metrics']['sharpe_ratio'] for p in efficient_portfolios]
            
            return {
                'portfolios': efficient_portfolios,
                'returns': returns,
                'volatilities': volatilities,
                'sharpe_ratios': sharpe_ratios
            }
        else:
            return {"error": "Could not generate efficient frontier"}
    
    def black_litterman_optimization(self, 
                                   views: Dict[str, float],
                                   view_confidences: Dict[str, float],
                                   tau: float = 0.025) -> Dict[str, Any]:
        """
        Implement Black-Litterman model.
        
        Args:
            views: Dictionary of asset views (expected returns)
            view_confidences: Confidence in each view (0-1)
            tau: Scaling factor for uncertainty
        """
        if not SCIPY_AVAILABLE:
            return {"error": "SciPy not available"}
        
        # Market cap weights (equal weight as proxy)
        n_assets = len(self.expected_returns)
        market_weights = np.array([1/n_assets] * n_assets)
        
        # Implied equilibrium returns
        risk_aversion = 3.0  # Typical value
        pi = risk_aversion * np.dot(self.cov_matrix, market_weights)
        
        # Views matrix
        P = np.zeros((len(views), n_assets))
        Q = np.zeros(len(views))
        
        for i, (asset, view) in enumerate(views.items()):
            if asset in self.expected_returns.index:
                asset_idx = self.expected_returns.index.get_loc(asset)
                P[i, asset_idx] = 1
                Q[i] = view
        
        # Uncertainty matrix
        omega = np.diag([1/view_confidences.get(asset, 0.5) for asset in views.keys()])
        
        # Black-Litterman formula
        tau_cov = tau * self.cov_matrix
        
        try:
            M1 = linalg.inv(tau_cov)
            M2 = np.dot(P.T, np.dot(linalg.inv(omega), P))
            M3 = np.dot(linalg.inv(tau_cov), pi)
            M4 = np.dot(P.T, np.dot(linalg.inv(omega), Q))
            
            # New expected returns
            mu_bl = np.dot(linalg.inv(M1 + M2), M3 + M4)
            
            # New covariance matrix
            cov_bl = linalg.inv(M1 + M2)
            
            # Optimize with Black-Litterman inputs
            original_expected_returns = self.expected_returns.copy()
            original_cov_matrix = self.cov_matrix.copy()
            
            self.expected_returns = pd.Series(mu_bl, index=self.expected_returns.index)
            self.cov_matrix = pd.DataFrame(cov_bl, 
                                         index=self.cov_matrix.index, 
                                         columns=self.cov_matrix.columns)
            
            result = self.mean_variance_optimization()
            
            # Restore original data
            self.expected_returns = original_expected_returns
            self.cov_matrix = original_cov_matrix
            
            if 'error' not in result:
                result['bl_expected_returns'] = dict(zip(self.expected_returns.index, mu_bl))
            
            return result
            
        except Exception as e:
            return {"error": f"Black-Litterman optimization failed: {e}"}
    
    def generate_portfolio_report(self, optimization_results: Dict[str, Any]) -> str:
        """Generate comprehensive portfolio optimization report."""
        report = f"""
📊 PORTFOLIO OPTIMIZATION REPORT
{'='*50}

📈 OPTIMIZATION RESULTS
{'-'*30}
"""
        
        for strategy, result in optimization_results.items():
            if 'error' in result:
                report += f"\n{strategy.upper()}: ❌ {result['error']}\n"
                continue
            
            weights = result['weights']
            metrics = result['metrics']
            
            report += f"""
{strategy.upper()} PORTFOLIO:
  Expected Return: {metrics['return']*100:.2f}%
  Volatility: {metrics['volatility']*100:.2f}%
  Sharpe Ratio: {metrics['sharpe_ratio']:.2f}
  
  Asset Allocation:
"""
            for asset, weight in weights.items():
                report += f"    {asset}: {weight*100:.1f}%\n"
        
        report += f"""

💡 INTERPRETATION
{'-'*30}
• Mean-Variance: Maximizes risk-adjusted returns
• Risk Parity: Equal risk contribution from all assets
• Minimum Variance: Lowest possible portfolio risk
• Black-Litterman: Incorporates market views

🎯 RECOMMENDATIONS
{'-'*30}
• Consider transaction costs in implementation
• Rebalance periodically to maintain target weights
• Monitor correlations as they change over time
• Use multiple optimization approaches for robustness

⚠️ DISCLAIMER
{'-'*30}
Portfolio optimization is based on historical data and assumptions.
Future performance may differ significantly from optimized expectations.
"""
        
        return report


def main():
    """Example usage of PortfolioOptimizer."""
    from data.client import BinanceDataClient
    
    # For demonstration, we'll create synthetic multi-asset data
    # In practice, you'd load real data for multiple assets
    
    client = BinanceDataClient()
    btc_data = client.get_candles("BTCUSDT", limit=1000)
    
    if btc_data.empty:
        print("No data available. Fetching from API...")
        btc_data = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
        client.store_candles(btc_data, "BTCUSDT")
        btc_data = client.get_candles("BTCUSDT", limit=1000)
    
    # Create synthetic assets for demonstration
    np.random.seed(42)
    
    # BTC prices
    btc_prices = btc_data['close']
    
    # Synthetic ETH (correlated with BTC)
    eth_returns = btc_prices.pct_change().dropna()
    eth_noise = np.random.normal(0, 0.01, len(eth_returns))
    eth_returns_synthetic = eth_returns * 0.8 + eth_noise
    eth_prices = (1 + eth_returns_synthetic).cumprod() * 2000  # Start at $2000
    
    # Synthetic BOND (low correlation)
    bond_returns = np.random.normal(0.0001, 0.005, len(btc_prices))
    bond_prices = (1 + pd.Series(bond_returns, index=btc_prices.index)).cumprod() * 100
    
    # Prepare price data
    price_data = {
        'BTC': btc_prices,
        'ETH': eth_prices,
        'BOND': bond_prices
    }
    
    print("📊 Running portfolio optimization...")
    
    # Initialize optimizer
    optimizer = PortfolioOptimizer(risk_free_rate=0.02)
    
    # Prepare returns data
    returns_df = optimizer.prepare_returns_data(price_data)
    print(f"Prepared returns data: {len(returns_df)} periods, {len(returns_df.columns)} assets")
    
    # Run different optimization strategies
    optimization_results = {}
    
    # Mean-variance optimization (max Sharpe)
    print("🎯 Running mean-variance optimization...")
    optimization_results['mean_variance'] = optimizer.mean_variance_optimization()
    
    # Risk parity
    print("⚖️ Running risk parity optimization...")
    optimization_results['risk_parity'] = optimizer.risk_parity_optimization()
    
    # Minimum variance
    print("📉 Running minimum variance optimization...")
    optimization_results['minimum_variance'] = optimizer.minimum_variance_optimization()
    
    # Black-Litterman with views
    print("🔮 Running Black-Litterman optimization...")
    views = {'BTC': 0.15, 'ETH': 0.12}  # Expected returns
    view_confidences = {'BTC': 0.7, 'ETH': 0.6}  # Confidence levels
    optimization_results['black_litterman'] = optimizer.black_litterman_optimization(
        views, view_confidences
    )
    
    # Generate report
    report = optimizer.generate_portfolio_report(optimization_results)
    print(report)
    
    # Save results
    filename = f"portfolio_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(filename, 'w') as f:
        f.write(report)
    print(f"\n📄 Report saved to: {filename}")


if __name__ == "__main__":
    main()
