"""
Unit tests for technical indicators.
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from indicators.ta_wrappers import (
    add_moving_averages, add_rsi, add_bollinger_bands, add_atr,
    add_macd, add_obv, add_adx, add_supertrend, add_vwap,
    add_all_indicators, get_trading_signals, create_labels
)


class TestTechnicalIndicators:
    """Test cases for technical indicators."""

    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        np.random.seed(42)  # For reproducible tests

        dates = pd.date_range('2024-01-01', periods=100, freq='H')

        # Generate realistic price data with trend and volatility
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 100)  # 1% volatility
        prices = [base_price]

        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        # Create OHLCV data
        data = {
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': [1000 + np.random.randint(-100, 100) for _ in range(100)]
        }

        df = pd.DataFrame(data, index=dates)
        return df

    def test_moving_averages(self, sample_data):
        """Test moving averages calculation."""
        df = add_moving_averages(sample_data, short_window=5, long_window=20)

        # Check that columns are added
        assert 'MA_Short' in df.columns
        assert 'MA_Long' in df.columns
        assert 'Signal' in df.columns
        assert 'Position' in df.columns

        # Check that MA values are reasonable
        assert not df['MA_Short'].isna().all()
        assert not df['MA_Long'].isna().all()

        # Short MA should be more responsive (higher variance)
        ma_short_var = df['MA_Short'].dropna().var()
        ma_long_var = df['MA_Long'].dropna().var()
        assert ma_short_var >= ma_long_var

        # Signal should be 0 or 1
        signals = df['Signal'].dropna().unique()
        assert all(s in [0, 1] for s in signals)

    def test_rsi(self, sample_data):
        """Test RSI calculation."""
        df = add_rsi(sample_data, window=14)

        # Check that RSI column is added
        assert 'RSI' in df.columns

        # RSI should be between 0 and 100
        rsi_values = df['RSI'].dropna()
        assert (rsi_values >= 0).all()
        assert (rsi_values <= 100).all()

        # Should have some variation
        assert rsi_values.std() > 0

    def test_bollinger_bands(self, sample_data):
        """Test Bollinger Bands calculation."""
        df = add_bollinger_bands(sample_data, window=20, std_dev=2)

        # Check that columns are added
        assert 'Upper_BB' in df.columns
        assert 'Lower_BB' in df.columns
        assert 'Middle_BB' in df.columns

        # Upper band should be above lower band
        valid_data = df.dropna()
        assert (valid_data['Upper_BB'] > valid_data['Lower_BB']).all()

        # Middle band should be between upper and lower
        assert (valid_data['Middle_BB'] >= valid_data['Lower_BB']).all()
        assert (valid_data['Middle_BB'] <= valid_data['Upper_BB']).all()

    def test_atr(self, sample_data):
        """Test Average True Range calculation."""
        df = add_atr(sample_data, window=14)

        # Check that ATR column is added
        assert 'ATR' in df.columns

        # ATR should be positive
        atr_values = df['ATR'].dropna()
        assert (atr_values > 0).all()

    def test_macd(self, sample_data):
        """Test MACD calculation."""
        df = add_macd(sample_data, fast=12, slow=26, signal=9)

        # Check that columns are added
        assert 'MACD' in df.columns
        assert 'MACD_signal' in df.columns
        assert 'MACD_hist' in df.columns

        # MACD histogram should be MACD - Signal
        valid_data = df.dropna()
        calculated_hist = valid_data['MACD'] - valid_data['MACD_signal']
        np.testing.assert_array_almost_equal(
            valid_data['MACD_hist'].values,
            calculated_hist.values,
            decimal=5
        )

    def test_obv(self, sample_data):
        """Test On-Balance Volume calculation."""
        df = add_obv(sample_data)

        # Check that OBV column is added
        assert 'OBV' in df.columns

        # OBV should have some variation
        obv_values = df['OBV'].dropna()
        assert obv_values.std() > 0

        # First value should be 0
        assert df['OBV'].iloc[0] == 0

    def test_adx(self, sample_data):
        """Test ADX calculation."""
        df = add_adx(sample_data, window=14)

        # Check that columns are added
        assert 'ADX' in df.columns
        assert 'DI_plus' in df.columns
        assert 'DI_minus' in df.columns

        # ADX should be between 0 and 100
        adx_values = df['ADX'].dropna()
        assert (adx_values >= 0).all()
        assert (adx_values <= 100).all()

    def test_supertrend(self, sample_data):
        """Test SuperTrend calculation."""
        df = add_supertrend(sample_data, period=10, multiplier=3.0)

        # Check that columns are added
        assert 'SuperTrend' in df.columns
        assert 'SuperTrend_Direction' in df.columns
        assert 'SuperTrend_Signal' in df.columns

        # Direction should be 1 or -1
        direction_values = df['SuperTrend_Direction'].dropna().unique()
        assert all(d in [1, -1] for d in direction_values)

        # SuperTrend values should be reasonable (close to price)
        st_values = df['SuperTrend'].dropna()
        price_values = df['close'].loc[st_values.index]

        # SuperTrend should be within reasonable range of prices
        price_range = price_values.max() - price_values.min()
        st_range = st_values.max() - st_values.min()
        assert st_range <= price_range * 2  # Allow some flexibility

    def test_vwap(self, sample_data):
        """Test VWAP calculation."""
        df = add_vwap(sample_data)

        # Check that VWAP column is added
        assert 'VWAP' in df.columns

        # VWAP should be reasonable relative to prices
        vwap_values = df['VWAP'].dropna()
        price_values = df['close'].loc[vwap_values.index]

        # VWAP should be within reasonable range of prices
        assert vwap_values.min() >= price_values.min() * 0.9
        assert vwap_values.max() <= price_values.max() * 1.1

    def test_add_all_indicators(self, sample_data):
        """Test adding all indicators at once."""
        df = add_all_indicators(sample_data)

        # Check that key indicators are present
        expected_indicators = [
            'MA_Short', 'MA_Long', 'RSI', 'ATR', 'OBV',
            'Upper_BB', 'Lower_BB', 'MACD', 'MACD_signal',
            'ADX', 'SuperTrend', 'VWAP'
        ]

        for indicator in expected_indicators:
            assert indicator in df.columns, f"Missing indicator: {indicator}"

        # Check that we have some non-null values for each indicator
        for indicator in expected_indicators:
            assert not df[indicator].isna().all(), f"All values are NaN for {indicator}"

    def test_create_labels(self, sample_data):
        """Test label creation for ML training."""
        df = create_labels(sample_data, lookahead=1)

        # Check that target column is added
        assert 'target' in df.columns
        assert 'close_future' in df.columns

        # Target should be 0 or 1
        target_values = df['target'].unique()
        assert all(t in [0, 1] for t in target_values)

        # Should have fewer rows due to lookahead and dropna
        assert len(df) < len(sample_data)

    def test_get_trading_signals(self, sample_data):
        """Test trading signals generation."""
        # First add indicators
        df = add_all_indicators(sample_data)

        # Get signals
        signals = get_trading_signals(df)

        # Check that signal columns are present
        expected_signals = [
            'MA_Signal', 'RSI_Oversold', 'RSI_Overbought',
            'Buy_Signal', 'Sell_Signal'
        ]

        for signal in expected_signals:
            assert signal in signals.columns, f"Missing signal: {signal}"

        # Signals should be 0 or 1
        for signal in expected_signals:
            signal_values = signals[signal].unique()
            assert all(s in [0, 1] for s in signal_values), f"Invalid values in {signal}"

    def test_edge_cases(self, sample_data):
        """Test edge cases and error handling."""
        # Test with minimal data
        minimal_data = sample_data.head(5)

        # Should not raise errors, but may have many NaN values
        df = add_moving_averages(minimal_data, short_window=3, long_window=10)
        assert 'MA_Short' in df.columns
        assert 'MA_Long' in df.columns

        # Test with constant prices
        constant_data = sample_data.copy()
        constant_data['close'] = 50000
        constant_data['open'] = 50000
        constant_data['high'] = 50000
        constant_data['low'] = 50000

        df = add_rsi(constant_data)
        # RSI with constant prices should be around 50 or NaN
        rsi_values = df['RSI'].dropna()
        if len(rsi_values) > 0:
            assert (rsi_values >= 0).all() and (rsi_values <= 100).all()

    def test_data_integrity(self, sample_data):
        """Test that indicators don't corrupt original data."""
        original_columns = sample_data.columns.tolist()
        original_shape = sample_data.shape

        # Add indicators
        df = add_all_indicators(sample_data)

        # Original columns should still be present and unchanged
        for col in original_columns:
            assert col in df.columns
            pd.testing.assert_series_equal(
                sample_data[col],
                df[col],
                check_names=False
            )

        # Should have same number of rows
        assert df.shape[0] == original_shape[0]

        # Should have more columns
        assert df.shape[1] > original_shape[1]


    def test_integration_with_optimized(self, sample_data):
        """Test integration with optimized indicators."""
        try:
            from indicators.performance_optimized import OptimizedIndicators

            # Compare standard vs optimized
            df_standard = add_all_indicators(sample_data.copy())
            df_optimized = OptimizedIndicators.add_optimized_indicators(sample_data.copy())

            # Should have similar indicator counts
            standard_indicators = [col for col in df_standard.columns if col not in sample_data.columns]
            optimized_indicators = [col for col in df_optimized.columns if col not in sample_data.columns]

            # Should have reasonable number of indicators
            assert len(standard_indicators) >= 10
            assert len(optimized_indicators) >= 10

        except ImportError:
            pytest.skip("Optimized indicators not available")


if __name__ == "__main__":
    pytest.main([__file__])
