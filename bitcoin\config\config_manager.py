"""
Advanced Configuration Management System.
Handles environment-specific configs, secrets, and dynamic configuration updates.
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class Environment(Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class DatabaseConfig:
    url: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600


@dataclass
class ExchangeConfig:
    name: str
    api_key: str
    secret_key: str
    testnet: bool = True
    rate_limit: int = 1200
    timeout: int = 30


@dataclass
class TradingConfig:
    default_symbol: str = "BTCUSDT"
    default_timeframe: str = "1h"
    max_positions: int = 5
    risk_per_trade: float = 0.02
    max_drawdown: float = 0.15
    enable_live_trading: bool = False
    paper_trading_balance: float = 100000


@dataclass
class MLConfig:
    model_retrain_interval: int = 24  # hours
    feature_selection_threshold: float = 0.01
    cross_validation_folds: int = 5
    hyperparameter_trials: int = 100
    model_cache_size: int = 10


@dataclass
class NotificationConfig:
    enabled: bool = True
    channels: List[str] = None
    rate_limit_per_minute: int = 10
    rate_limit_per_hour: int = 100
    
    def __post_init__(self):
        if self.channels is None:
            self.channels = ["slack", "email"]


@dataclass
class SecurityConfig:
    jwt_secret_key: str
    jwt_expiration_hours: int = 24
    api_rate_limit: int = 1000
    max_login_attempts: int = 5
    session_timeout_minutes: int = 60
    enable_2fa: bool = False


class ConfigManager:
    """
    Advanced configuration management with environment support.
    """
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: Optional[Environment] = None):
        
        self.config_dir = Path(config_dir)
        self.environment = environment or self._detect_environment()
        self.logger = logging.getLogger(__name__)
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Load configurations
        self._config_cache: Dict[str, Any] = {}
        self._load_all_configs()
    
    def _detect_environment(self) -> Environment:
        """Detect current environment from environment variables."""
        env_name = os.getenv('TRADING_ENV', 'development').lower()
        
        try:
            return Environment(env_name)
        except ValueError:
            self.logger.warning(f"Unknown environment '{env_name}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _load_all_configs(self):
        """Load all configuration files."""
        # Load base config
        self._load_config_file("base.yaml")
        
        # Load environment-specific config
        env_config_file = f"{self.environment.value}.yaml"
        self._load_config_file(env_config_file)
        
        # Load secrets (if exists)
        self._load_secrets()
        
        # Override with environment variables
        self._load_env_overrides()
    
    def _load_config_file(self, filename: str):
        """Load configuration from YAML file."""
        config_path = self.config_dir / filename
        
        if not config_path.exists():
            self.logger.info(f"Config file {filename} not found, creating default")
            self._create_default_config(config_path)
        
        try:
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f) or {}
                self._merge_config(config_data)
                
        except Exception as e:
            self.logger.error(f"Failed to load config file {filename}: {e}")
    
    def _create_default_config(self, config_path: Path):
        """Create default configuration file."""
        if config_path.name == "base.yaml":
            default_config = self._get_default_base_config()
        else:
            default_config = self._get_default_env_config()
        
        try:
            with open(config_path, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to create default config {config_path}: {e}")
    
    def _get_default_base_config(self) -> Dict[str, Any]:
        """Get default base configuration."""
        return {
            'app': {
                'name': 'Bitcoin Trading System',
                'version': '1.0.0',
                'debug': False
            },
            'database': {
                'url': 'sqlite:///data/trading.db',
                'pool_size': 10,
                'max_overflow': 20
            },
            'trading': {
                'default_symbol': 'BTCUSDT',
                'default_timeframe': '1h',
                'max_positions': 5,
                'risk_per_trade': 0.02,
                'enable_live_trading': False
            },
            'ml': {
                'model_retrain_interval': 24,
                'feature_selection_threshold': 0.01,
                'cross_validation_folds': 5
            },
            'notifications': {
                'enabled': True,
                'channels': ['slack', 'email'],
                'rate_limit_per_minute': 10
            },
            'security': {
                'jwt_expiration_hours': 24,
                'api_rate_limit': 1000,
                'max_login_attempts': 5
            }
        }
    
    def _get_default_env_config(self) -> Dict[str, Any]:
        """Get default environment-specific configuration."""
        if self.environment == Environment.PRODUCTION:
            return {
                'app': {'debug': False},
                'database': {'pool_size': 20},
                'trading': {'enable_live_trading': True},
                'security': {'enable_2fa': True}
            }
        elif self.environment == Environment.STAGING:
            return {
                'app': {'debug': False},
                'trading': {'enable_live_trading': False},
                'database': {'url': 'postgresql://staging_user:password@localhost/trading_staging'}
            }
        else:  # Development/Testing
            return {
                'app': {'debug': True},
                'trading': {'enable_live_trading': False},
                'notifications': {'enabled': False}
            }
    
    def _load_secrets(self):
        """Load secrets from secure location."""
        secrets_path = self.config_dir / "secrets.yaml"
        
        if secrets_path.exists():
            try:
                with open(secrets_path, 'r') as f:
                    secrets = yaml.safe_load(f) or {}
                    self._merge_config(secrets)
                    
            except Exception as e:
                self.logger.error(f"Failed to load secrets: {e}")
        else:
            self.logger.info("No secrets file found")
    
    def _load_env_overrides(self):
        """Load configuration overrides from environment variables."""
        env_mappings = {
            'TRADING_DATABASE_URL': 'database.url',
            'TRADING_API_KEY': 'exchanges.binance.api_key',
            'TRADING_SECRET_KEY': 'exchanges.binance.secret_key',
            'TRADING_SLACK_WEBHOOK': 'notifications.slack.webhook_url',
            'TRADING_JWT_SECRET': 'security.jwt_secret_key',
            'TRADING_ENABLE_LIVE': 'trading.enable_live_trading',
            'TRADING_MAX_POSITIONS': 'trading.max_positions',
            'TRADING_RISK_PER_TRADE': 'trading.risk_per_trade'
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert string values to appropriate types
                if config_path.endswith(('enable_live_trading', 'debug', 'enabled')):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif config_path.endswith(('max_positions', 'pool_size', 'rate_limit')):
                    value = int(value)
                elif config_path.endswith(('risk_per_trade', 'max_drawdown')):
                    value = float(value)
                
                self._set_nested_config(config_path, value)
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """Merge new configuration into existing config."""
        self._deep_merge(self._config_cache, new_config)
    
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]):
        """Deep merge two dictionaries."""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _set_nested_config(self, path: str, value: Any):
        """Set nested configuration value using dot notation."""
        keys = path.split('.')
        config = self._config_cache
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get(self, path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        keys = path.split('.')
        config = self._config_cache
        
        try:
            for key in keys:
                config = config[key]
            return config
        except (KeyError, TypeError):
            return default
    
    def set(self, path: str, value: Any):
        """Set configuration value using dot notation."""
        self._set_nested_config(path, value)
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration."""
        db_config = self.get('database', {})
        return DatabaseConfig(**db_config)
    
    def get_trading_config(self) -> TradingConfig:
        """Get trading configuration."""
        trading_config = self.get('trading', {})
        return TradingConfig(**trading_config)
    
    def get_ml_config(self) -> MLConfig:
        """Get ML configuration."""
        ml_config = self.get('ml', {})
        return MLConfig(**ml_config)
    
    def get_notification_config(self) -> NotificationConfig:
        """Get notification configuration."""
        notif_config = self.get('notifications', {})
        return NotificationConfig(**notif_config)
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration."""
        security_config = self.get('security', {})
        # Generate JWT secret if not provided
        if 'jwt_secret_key' not in security_config:
            import secrets
            security_config['jwt_secret_key'] = secrets.token_urlsafe(32)
        
        return SecurityConfig(**security_config)
    
    def get_exchange_configs(self) -> List[ExchangeConfig]:
        """Get all exchange configurations."""
        exchanges_config = self.get('exchanges', {})
        configs = []
        
        for name, config in exchanges_config.items():
            configs.append(ExchangeConfig(name=name, **config))
        
        return configs
    
    def save_config(self, filename: Optional[str] = None):
        """Save current configuration to file."""
        if filename is None:
            filename = f"{self.environment.value}.yaml"
        
        config_path = self.config_dir / filename
        
        try:
            with open(config_path, 'w') as f:
                yaml.dump(self._config_cache, f, default_flow_style=False, indent=2)
                
            self.logger.info(f"Configuration saved to {config_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
    
    def reload_config(self):
        """Reload configuration from files."""
        self._config_cache.clear()
        self._load_all_configs()
        self.logger.info("Configuration reloaded")
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Check required configurations
        required_configs = [
            'app.name',
            'database.url',
            'trading.default_symbol'
        ]
        
        for config_path in required_configs:
            if self.get(config_path) is None:
                issues.append(f"Missing required configuration: {config_path}")
        
        # Validate trading configuration
        risk_per_trade = self.get('trading.risk_per_trade', 0)
        if risk_per_trade <= 0 or risk_per_trade > 0.1:
            issues.append("trading.risk_per_trade should be between 0 and 0.1")
        
        max_positions = self.get('trading.max_positions', 0)
        if max_positions <= 0 or max_positions > 20:
            issues.append("trading.max_positions should be between 1 and 20")
        
        # Validate database URL
        db_url = self.get('database.url', '')
        if not db_url.startswith(('sqlite://', 'postgresql://', 'mysql://')):
            issues.append("Invalid database URL format")
        
        return issues
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for debugging."""
        return {
            'environment': self.environment.value,
            'config_dir': str(self.config_dir),
            'app_name': self.get('app.name'),
            'app_version': self.get('app.version'),
            'debug_mode': self.get('app.debug'),
            'live_trading': self.get('trading.enable_live_trading'),
            'database_type': self.get('database.url', '').split('://')[0] if self.get('database.url') else 'unknown',
            'notification_channels': self.get('notifications.channels', []),
            'loaded_at': datetime.now().isoformat()
        }


# Global config manager instance
config_manager = ConfigManager()


def main():
    """Example usage of ConfigManager."""
    # Initialize config manager
    config = ConfigManager()
    
    # Get different configuration objects
    db_config = config.get_database_config()
    trading_config = config.get_trading_config()
    ml_config = config.get_ml_config()
    
    print(f"Database URL: {db_config.url}")
    print(f"Default Symbol: {trading_config.default_symbol}")
    print(f"ML Retrain Interval: {ml_config.model_retrain_interval} hours")
    
    # Validate configuration
    issues = config.validate_config()
    if issues:
        print(f"Configuration issues: {issues}")
    else:
        print("Configuration is valid")
    
    # Get configuration summary
    summary = config.get_config_summary()
    print(f"Config summary: {summary}")


if __name__ == "__main__":
    main()
