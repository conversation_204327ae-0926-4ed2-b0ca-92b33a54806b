"""
Paper trading implementation for testing strategies without real money.
Simulates order execution with realistic slippage and fees.
"""

import pandas as pd
import numpy as np
import sqlite3
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"


class OrderStatus(Enum):
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


@dataclass
class Order:
    """Represents a trading order."""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = None
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: float = 0.0
    commission: float = 0.0
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)


@dataclass
class Position:
    """Represents a trading position."""
    symbol: str
    side: str  # 'LONG' or 'SHORT'
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    entry_time: datetime = None
    
    def __post_init__(self):
        if self.entry_time is None:
            self.entry_time = datetime.now(timezone.utc)
            
    def update_price(self, new_price: float):
        """Update current price and unrealized P&L."""
        self.current_price = new_price
        if self.side == 'LONG':
            self.unrealized_pnl = (new_price - self.entry_price) * self.quantity
        else:  # SHORT
            self.unrealized_pnl = (self.entry_price - new_price) * self.quantity


class PaperTrader:
    """
    Paper trading engine with realistic execution simulation.
    """
    
    def __init__(self, 
                 initial_balance: float = 10000.0,
                 commission_rate: float = 0.001,  # 0.1% commission
                 slippage_bps: float = 2.0,       # 2 basis points slippage
                 db_path: str = "paper_trading.db"):
        
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.commission_rate = commission_rate
        self.slippage_bps = slippage_bps / 10000  # Convert to decimal
        self.db_path = db_path
        
        # Trading state
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.order_counter = 0
        self.trade_history: List[Dict] = []
        
        # Performance tracking
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.max_equity = initial_balance
        self.max_drawdown = 0.0
        
        self._init_database()
        
    def _init_database(self):
        """Initialize SQLite database for paper trading records."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS paper_orders (
                    id TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    order_type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL,
                    stop_price REAL,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    filled_at TIMESTAMP,
                    filled_price REAL,
                    filled_quantity REAL,
                    commission REAL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS paper_positions (
                    symbol TEXT PRIMARY KEY,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    current_price REAL NOT NULL,
                    unrealized_pnl REAL,
                    realized_pnl REAL,
                    entry_time TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS paper_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    commission REAL NOT NULL,
                    pnl REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS paper_equity (
                    timestamp TIMESTAMP PRIMARY KEY,
                    equity REAL NOT NULL,
                    balance REAL NOT NULL,
                    unrealized_pnl REAL NOT NULL
                )
            """)
            
    def create_order(self, 
                    symbol: str,
                    side: OrderSide,
                    order_type: OrderType,
                    quantity: float,
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None) -> str:
        """Create a new order."""
        
        self.order_counter += 1
        order_id = f"PAPER_{self.order_counter:06d}"
        
        order = Order(
            id=order_id,
            symbol=symbol,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price
        )
        
        # Validate order
        if not self._validate_order(order):
            order.status = OrderStatus.REJECTED
            logger.warning(f"Order rejected: {order_id}")
            return order_id
            
        self.orders[order_id] = order
        self._save_order(order)
        
        logger.info(f"Order created: {order_id} - {side.value} {quantity} {symbol}")
        return order_id
        
    def _validate_order(self, order: Order) -> bool:
        """Validate order before execution."""
        
        # Check available balance for buy orders
        if order.side == OrderSide.BUY:
            required_amount = order.quantity * (order.price or 0)
            if required_amount > self.balance:
                logger.warning(f"Insufficient balance: {self.balance} < {required_amount}")
                return False
                
        # Check available position for sell orders
        elif order.side == OrderSide.SELL:
            position = self.positions.get(order.symbol)
            if not position or position.quantity < order.quantity:
                available = position.quantity if position else 0
                logger.warning(f"Insufficient position: {available} < {order.quantity}")
                return False
                
        return True
        
    def process_market_data(self, symbol: str, price: float, timestamp: datetime = None):
        """Process new market data and execute pending orders."""
        
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
            
        # Update positions with current price
        if symbol in self.positions:
            self.positions[symbol].update_price(price)
            
        # Check pending orders for execution
        orders_to_execute = []
        
        for order_id, order in self.orders.items():
            if order.symbol == symbol and order.status == OrderStatus.PENDING:
                
                should_execute = False
                execution_price = price
                
                if order.order_type == OrderType.MARKET:
                    should_execute = True
                    # Apply slippage
                    if order.side == OrderSide.BUY:
                        execution_price = price * (1 + self.slippage_bps)
                    else:
                        execution_price = price * (1 - self.slippage_bps)
                        
                elif order.order_type == OrderType.LIMIT:
                    if order.side == OrderSide.BUY and price <= order.price:
                        should_execute = True
                        execution_price = order.price
                    elif order.side == OrderSide.SELL and price >= order.price:
                        should_execute = True
                        execution_price = order.price
                        
                elif order.order_type == OrderType.STOP:
                    if order.side == OrderSide.BUY and price >= order.stop_price:
                        should_execute = True
                        execution_price = price * (1 + self.slippage_bps)
                    elif order.side == OrderSide.SELL and price <= order.stop_price:
                        should_execute = True
                        execution_price = price * (1 - self.slippage_bps)
                        
                if should_execute:
                    orders_to_execute.append((order, execution_price, timestamp))
                    
        # Execute orders
        for order, exec_price, exec_time in orders_to_execute:
            self._execute_order(order, exec_price, exec_time)
            
        # Update equity curve
        self._update_equity(timestamp)
        
    def _execute_order(self, order: Order, execution_price: float, timestamp: datetime):
        """Execute an order at the given price."""
        
        commission = order.quantity * execution_price * self.commission_rate
        
        if order.side == OrderSide.BUY:
            self._execute_buy(order, execution_price, commission, timestamp)
        else:
            self._execute_sell(order, execution_price, commission, timestamp)
            
        # Update order status
        order.status = OrderStatus.FILLED
        order.filled_at = timestamp
        order.filled_price = execution_price
        order.filled_quantity = order.quantity
        order.commission = commission
        
        self._save_order(order)
        
        logger.info(f"Order executed: {order.id} at ${execution_price:.2f}")
        
    def _execute_buy(self, order: Order, price: float, commission: float, timestamp: datetime):
        """Execute a buy order."""
        
        total_cost = (order.quantity * price) + commission
        self.balance -= total_cost
        
        # Update or create position
        if order.symbol in self.positions:
            pos = self.positions[order.symbol]
            # Calculate weighted average entry price
            total_quantity = pos.quantity + order.quantity
            total_cost_basis = (pos.quantity * pos.entry_price) + (order.quantity * price)
            new_entry_price = total_cost_basis / total_quantity
            
            pos.quantity = total_quantity
            pos.entry_price = new_entry_price
            pos.current_price = price
        else:
            self.positions[order.symbol] = Position(
                symbol=order.symbol,
                side='LONG',
                quantity=order.quantity,
                entry_price=price,
                current_price=price,
                entry_time=timestamp
            )
            
        self._save_trade(order.symbol, 'BUY', order.quantity, price, commission, timestamp)
        
    def _execute_sell(self, order: Order, price: float, commission: float, timestamp: datetime):
        """Execute a sell order."""
        
        total_proceeds = (order.quantity * price) - commission
        self.balance += total_proceeds
        
        # Calculate realized P&L
        if order.symbol in self.positions:
            pos = self.positions[order.symbol]
            realized_pnl = (price - pos.entry_price) * order.quantity - commission
            pos.realized_pnl += realized_pnl
            
            # Update position
            pos.quantity -= order.quantity
            if pos.quantity <= 0:
                del self.positions[order.symbol]
            else:
                pos.current_price = price
                
            self._save_trade(order.symbol, 'SELL', order.quantity, price, commission, timestamp, realized_pnl)
            
    def _save_order(self, order: Order):
        """Save order to database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO paper_orders 
                (id, symbol, side, order_type, quantity, price, stop_price, status, 
                 created_at, filled_at, filled_price, filled_quantity, commission)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                order.id, order.symbol, order.side.value, order.order_type.value,
                order.quantity, order.price, order.stop_price, order.status.value,
                order.created_at, order.filled_at, order.filled_price,
                order.filled_quantity, order.commission
            ))
            
    def _save_trade(self, symbol: str, side: str, quantity: float, price: float, 
                   commission: float, timestamp: datetime, pnl: float = None):
        """Save trade to database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO paper_trades 
                (symbol, side, quantity, price, commission, pnl, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (symbol, side, quantity, price, commission, pnl, timestamp))
            
    def _update_equity(self, timestamp: datetime):
        """Update equity curve."""
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_equity = self.balance + unrealized_pnl
        
        self.equity_curve.append((timestamp, total_equity))
        
        # Update max equity and drawdown
        if total_equity > self.max_equity:
            self.max_equity = total_equity
            
        current_drawdown = (self.max_equity - total_equity) / self.max_equity
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
            
        # Save to database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO paper_equity 
                (timestamp, equity, balance, unrealized_pnl)
                VALUES (?, ?, ?, ?)
            """, (timestamp, total_equity, self.balance, unrealized_pnl))
            
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary."""
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_equity = self.balance + unrealized_pnl
        total_return = (total_equity - self.initial_balance) / self.initial_balance * 100
        
        return {
            'balance': self.balance,
            'unrealized_pnl': unrealized_pnl,
            'total_equity': total_equity,
            'total_return_pct': total_return,
            'max_drawdown_pct': self.max_drawdown * 100,
            'positions_count': len(self.positions),
            'open_orders_count': len([o for o in self.orders.values() if o.status == OrderStatus.PENDING])
        }
        
    def get_positions(self) -> List[Dict]:
        """Get current positions."""
        return [asdict(pos) for pos in self.positions.values()]
        
    def get_orders(self, status: OrderStatus = None) -> List[Dict]:
        """Get orders, optionally filtered by status."""
        orders = self.orders.values()
        if status:
            orders = [o for o in orders if o.status == status]
        return [asdict(o) for o in orders]
        
    def cancel_order(self, order_id: str) -> bool:
        """Cancel a pending order."""
        if order_id in self.orders and self.orders[order_id].status == OrderStatus.PENDING:
            self.orders[order_id].status = OrderStatus.CANCELLED
            self._save_order(self.orders[order_id])
            logger.info(f"Order cancelled: {order_id}")
            return True
        return False
        
    def get_performance_metrics(self) -> Dict:
        """Calculate performance metrics."""
        if len(self.equity_curve) < 2:
            return {}
            
        # Convert equity curve to returns
        equity_df = pd.DataFrame(self.equity_curve, columns=['timestamp', 'equity'])
        equity_df.set_index('timestamp', inplace=True)
        returns = equity_df['equity'].pct_change().dropna()
        
        if len(returns) == 0:
            return {}
            
        # Calculate metrics
        total_return = (equity_df['equity'].iloc[-1] - self.initial_balance) / self.initial_balance
        
        if len(returns) > 1:
            sharpe = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            sortino = returns.mean() / returns[returns < 0].std() * np.sqrt(252) if len(returns[returns < 0]) > 0 else 0
        else:
            sharpe = sortino = 0
            
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe,
            'sortino_ratio': sortino,
            'max_drawdown': self.max_drawdown,
            'volatility': returns.std() * np.sqrt(252) if len(returns) > 1 else 0,
            'total_trades': len(self.trade_history)
        }


def main():
    """Example usage of PaperTrader."""
    trader = PaperTrader(initial_balance=10000)
    
    # Create some orders
    order1 = trader.create_order("BTCUSDT", OrderSide.BUY, OrderType.MARKET, 0.1)
    print(f"Created order: {order1}")
    
    # Simulate market data
    trader.process_market_data("BTCUSDT", 50000)
    trader.process_market_data("BTCUSDT", 51000)
    trader.process_market_data("BTCUSDT", 49000)
    
    # Check portfolio
    summary = trader.get_portfolio_summary()
    print(f"Portfolio Summary: {summary}")
    
    positions = trader.get_positions()
    print(f"Positions: {positions}")
    
    metrics = trader.get_performance_metrics()
    print(f"Performance: {metrics}")


if __name__ == "__main__":
    main()
