"""
Market regime detection using Hidden Markov Models.
Identifies bull/bear markets and volatility regimes for adaptive strategies.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from hmmlearn import hmm
    HMM_AVAILABLE = True
except ImportError:
    HMM_AVAILABLE = False
    print("HMM not available. Install with: pip install hmmlearn")

from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


class MarketRegimeDetector:
    """
    Detect market regimes using Hidden Markov Models and other techniques.
    """

    def __init__(self, n_regimes: int = 3):
        """
        Initialize regime detector.

        Args:
            n_regimes: Number of market regimes to detect
        """
        self.n_regimes = n_regimes
        self.model = None
        self.scaler = StandardScaler()
        self.regime_names = {
            0: "Bear Market",
            1: "Sideways Market",
            2: "Bull Market"
        }

    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for regime detection."""
        df = data.copy()

        # Returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # Volatility
        df['volatility'] = df['returns'].rolling(20).std()
        df['realized_vol'] = np.sqrt(252) * df['returns'].rolling(20).std()

        # Momentum indicators
        df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['momentum_20'] = df['close'] / df['close'].shift(20) - 1

        # Moving averages
        df['ma_20'] = df['close'].rolling(20).mean()
        df['ma_50'] = df['close'].rolling(50).mean()
        df['ma_ratio'] = df['ma_20'] / df['ma_50'] - 1

        # Price position
        df['high_20'] = df['high'].rolling(20).max()
        df['low_20'] = df['low'].rolling(20).min()
        df['price_position'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])

        # Volume
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']

        # Trend strength
        df['trend_strength'] = abs(df['ma_ratio'])

        return df

    def detect_regimes_hmm(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Detect market regimes using Hidden Markov Models.
        """
        if not HMM_AVAILABLE:
            return self._fallback_regime_detection(data)

        print("🔍 Detecting market regimes using HMM...")

        # Prepare features
        df_features = self.prepare_features(data)

        # Select features for HMM
        feature_cols = [
            'returns', 'volatility', 'momentum_20',
            'ma_ratio', 'price_position', 'trend_strength'
        ]

        # Clean data
        df_clean = df_features[feature_cols].dropna()

        if len(df_clean) < 100:
            print("❌ Insufficient data for regime detection")
            return {}

        # Scale features
        X_scaled = self.scaler.fit_transform(df_clean)

        # Fit HMM model
        try:
            self.model = hmm.GaussianHMM(
                n_components=self.n_regimes,
                covariance_type="full",
                n_iter=1000,
                random_state=42
            )

            self.model.fit(X_scaled)

            # Predict regimes
            regimes = self.model.predict(X_scaled)

            # Map regimes to original dataframe
            regime_series = pd.Series(index=df_clean.index, data=regimes)

            # Analyze regimes
            regime_analysis = self._analyze_regimes(df_features, regime_series)

            print(f"✅ Detected {self.n_regimes} market regimes")

            return {
                'regimes': regime_series,
                'analysis': regime_analysis,
                'model': self.model,
                'features': df_clean,
                'feature_names': feature_cols,
                'regime_names': self.regime_names
            }

        except Exception as e:
            print(f"❌ HMM failed: {e}")
            return self._fallback_regime_detection(data)

    def _fallback_regime_detection(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fallback regime detection using simple rules."""
        print("🔄 Using fallback regime detection...")

        df = self.prepare_features(data)

        # Simple regime classification
        regimes = []

        for i in range(len(df)):
            if pd.isna(df['ma_ratio'].iloc[i]) or pd.isna(df['volatility'].iloc[i]):
                regimes.append(1)  # Neutral
                continue

            ma_ratio = df['ma_ratio'].iloc[i]
            volatility = df['volatility'].iloc[i]
            momentum = df['momentum_20'].iloc[i] if not pd.isna(df['momentum_20'].iloc[i]) else 0

            # Bull market: uptrend + low volatility
            if ma_ratio > 0.02 and momentum > 0.05 and volatility < 0.03:
                regimes.append(2)  # Bull
            # Bear market: downtrend + high volatility
            elif ma_ratio < -0.02 and momentum < -0.05:
                regimes.append(0)  # Bear
            else:
                regimes.append(1)  # Sideways

        regime_series = pd.Series(index=df.index, data=regimes)
        regime_analysis = self._analyze_regimes(df, regime_series)

        return {
            'regimes': regime_series,
            'analysis': regime_analysis,
            'model': None,
            'features': df[['returns', 'volatility', 'momentum_20', 'ma_ratio']].dropna(),
            'feature_names': ['returns', 'volatility', 'momentum_20', 'ma_ratio'],
            'regime_names': self.regime_names
        }

    def _analyze_regimes(self, data: pd.DataFrame, regimes: pd.Series) -> Dict[str, Any]:
        """Analyze characteristics of each regime."""
        analysis = {}

        for regime in range(self.n_regimes):
            regime_mask = regimes == regime
            regime_data = data[regime_mask]

            if len(regime_data) == 0:
                continue

            analysis[regime] = {
                'name': self.regime_names.get(regime, f"Regime {regime}"),
                'periods': len(regime_data),
                'percentage': len(regime_data) / len(regimes) * 100,
                'avg_return': regime_data['returns'].mean() * 100 if 'returns' in regime_data else 0,
                'avg_volatility': regime_data['volatility'].mean() * 100 if 'volatility' in regime_data else 0,
                'avg_momentum': regime_data['momentum_20'].mean() * 100 if 'momentum_20' in regime_data else 0,
                'sharpe_ratio': (regime_data['returns'].mean() / regime_data['returns'].std()) * np.sqrt(252) if 'returns' in regime_data and regime_data['returns'].std() > 0 else 0
            }

        return analysis

    def detect_volatility_regimes(self, data: pd.DataFrame, n_vol_regimes: int = 2) -> Dict[str, Any]:
        """Detect volatility regimes using clustering."""
        print("📊 Detecting volatility regimes...")

        df = self.prepare_features(data)

        # Volatility features
        vol_features = ['volatility', 'realized_vol']
        df_vol = df[vol_features].dropna()

        if len(df_vol) < 50:
            print("❌ Insufficient data for volatility regime detection")
            return {}

        # K-means clustering
        kmeans = KMeans(n_clusters=n_vol_regimes, random_state=42, n_init=10)
        vol_regimes = kmeans.fit_predict(df_vol)

        # Create regime series
        vol_regime_series = pd.Series(index=df_vol.index, data=vol_regimes)

        # Analyze volatility regimes
        vol_analysis = {}
        for regime in range(n_vol_regimes):
            regime_mask = vol_regime_series == regime
            # Use the same index for filtering
            regime_data = df.loc[vol_regime_series.index[regime_mask]]

            vol_analysis[regime] = {
                'name': f"Vol Regime {regime}",
                'periods': len(regime_data),
                'percentage': len(regime_data) / len(vol_regime_series) * 100,
                'avg_volatility': regime_data['volatility'].mean() * 100 if 'volatility' in regime_data else 0,
                'avg_return': regime_data['returns'].mean() * 100 if 'returns' in regime_data else 0
            }

        return {
            'vol_regimes': vol_regime_series,
            'vol_analysis': vol_analysis,
            'vol_model': kmeans
        }

    def get_current_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get current market regime."""
        if self.model is None:
            # Use fallback method
            df = self.prepare_features(data)
            latest = df.iloc[-1]

            ma_ratio = latest.get('ma_ratio', 0)
            momentum = latest.get('momentum_20', 0)
            volatility = latest.get('volatility', 0)

            if ma_ratio > 0.02 and momentum > 0.05 and volatility < 0.03:
                current_regime = 2  # Bull
            elif ma_ratio < -0.02 and momentum < -0.05:
                current_regime = 0  # Bear
            else:
                current_regime = 1  # Sideways
        else:
            # Use HMM model
            df_features = self.prepare_features(data)
            feature_cols = ['returns', 'volatility', 'momentum_20', 'ma_ratio', 'price_position', 'trend_strength']
            latest_features = df_features[feature_cols].iloc[-1:].dropna()

            if len(latest_features) == 0:
                current_regime = 1  # Default to sideways
            else:
                X_scaled = self.scaler.transform(latest_features)
                current_regime = self.model.predict(X_scaled)[0]

        return {
            'regime': current_regime,
            'regime_name': self.regime_names.get(current_regime, f"Regime {current_regime}"),
            'confidence': 0.8  # Placeholder
        }

    def generate_regime_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive regime analysis report."""
        if not results:
            return "❌ No regime detection results available"

        analysis = results.get('analysis', {})
        regimes = results.get('regimes', pd.Series())

        report = f"""
🔍 MARKET REGIME ANALYSIS REPORT
{'='*50}

📊 REGIME OVERVIEW
{'-'*30}
Analysis Period: {regimes.index[0].strftime('%Y-%m-%d')} to {regimes.index[-1].strftime('%Y-%m-%d')}
Total Periods: {len(regimes)}
Number of Regimes: {len(analysis)}

📈 REGIME BREAKDOWN
{'-'*30}
"""

        for regime_id, regime_data in analysis.items():
            report += f"""
{regime_data['name']}:
  Duration: {regime_data['periods']} periods ({regime_data['percentage']:.1f}%)
  Avg Return: {regime_data['avg_return']:.2f}%
  Avg Volatility: {regime_data['avg_volatility']:.2f}%
  Avg Momentum: {regime_data['avg_momentum']:.2f}%
  Sharpe Ratio: {regime_data['sharpe_ratio']:.2f}
"""

        # Current regime
        if regimes.empty:
            current_regime = "Unknown"
        else:
            current_regime_id = regimes.iloc[-1]
            current_regime = analysis.get(current_regime_id, {}).get('name', 'Unknown')

        report += f"""
🎯 CURRENT MARKET STATE
{'-'*30}
Current Regime: {current_regime}
Last Update: {regimes.index[-1].strftime('%Y-%m-%d %H:%M:%S')}

💡 TRADING IMPLICATIONS
{'-'*30}
"""

        if current_regime == "Bull Market":
            report += """• Favor long positions and momentum strategies
• Reduce position sizing during high volatility
• Consider trend-following indicators"""
        elif current_regime == "Bear Market":
            report += """• Consider defensive positioning
• Short-term mean reversion strategies
• Increase cash allocation"""
        else:
            report += """• Range-bound trading strategies
• Mean reversion approaches
• Reduced position sizes"""

        report += f"""

⚠️ DISCLAIMER
{'-'*30}
Regime detection is based on historical patterns and may not predict future market conditions.
Use as one factor among many in trading decisions.
"""

        return report


def main():
    """Example usage of MarketRegimeDetector."""
    from data.client import BinanceDataClient

    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=2000)

    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 2000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=2000)

    print(f"Loaded {len(df)} candles for regime detection")

    # Initialize detector
    detector = MarketRegimeDetector(n_regimes=3)

    # Detect regimes
    results = detector.detect_regimes_hmm(df)

    if results:
        # Generate report
        report = detector.generate_regime_report(results)
        print(report)

        # Get current regime
        current = detector.get_current_regime(df)
        print(f"\n🎯 Current Market Regime: {current['regime_name']}")

        # Detect volatility regimes
        vol_results = detector.detect_volatility_regimes(df)
        if vol_results:
            print(f"\n📊 Volatility Regimes Detected:")
            for regime_id, regime_data in vol_results['vol_analysis'].items():
                print(f"  {regime_data['name']}: {regime_data['percentage']:.1f}% of time")

        # Save results
        filename = f"regime_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {filename}")

    else:
        print("❌ Regime detection failed")


if __name__ == "__main__":
    main()
