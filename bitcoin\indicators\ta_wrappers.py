"""
Technical Analysis indicators using pandas-ta and custom implementations.
Includes all indicators from the original code plus new ones suggested in the plan.
"""

import pandas as pd
import numpy as np
import ta
from typing import Optional


def add_moving_averages(df: pd.DataFrame, short_window: int = 10, long_window: int = 50) -> pd.DataFrame:
    """Add short and long moving averages."""
    df = df.copy()
    df['MA_Short'] = df['close'].rolling(window=short_window).mean()
    df['MA_Long'] = df['close'].rolling(window=long_window).mean()

    # Generate signals
    df['Signal'] = 0
    df.loc[df.index[short_window:], 'Signal'] = (
        df['MA_Short'][short_window:] > df['MA_Long'][short_window:]
    ).astype(int)
    df['Position'] = df['Signal'].diff()

    return df


def add_rsi(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add RSI indicator."""
    df = df.copy()
    df['RSI'] = ta.momentum.RSIIndicator(df['close'], window=window).rsi()
    return df


def add_bollinger_bands(df: pd.DataFrame, window: int = 20, std_dev: int = 2) -> pd.DataFrame:
    """Add Bollinger Bands."""
    df = df.copy()
    bb = ta.volatility.BollingerBands(df['close'], window=window, window_dev=std_dev)
    df['Upper_BB'] = bb.bollinger_hband()
    df['Lower_BB'] = bb.bollinger_lband()
    df['Middle_BB'] = bb.bollinger_mavg()
    return df


def add_atr(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Average True Range."""
    df = df.copy()
    df['ATR'] = ta.volatility.AverageTrueRange(
        df['high'], df['low'], df['close'], window=window
    ).average_true_range()
    return df


def add_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
    """Add MACD indicator."""
    df = df.copy()
    macd = ta.trend.MACD(df['close'], window_fast=fast, window_slow=slow, window_sign=signal)
    df['MACD'] = macd.macd()
    df['MACD_signal'] = macd.macd_signal()
    df['MACD_hist'] = macd.macd_diff()
    return df


def add_obv(df: pd.DataFrame) -> pd.DataFrame:
    """Add On-Balance Volume."""
    df = df.copy()
    df['OBV'] = ta.volume.OnBalanceVolumeIndicator(df['close'], df['volume']).on_balance_volume()
    return df


def add_adx(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """
    Add ADX (Average Directional Index) and Directional Indicators.
    ADX measures trend strength, DI+ and DI- show trend direction.
    """
    df = df.copy()
    adx = ta.trend.ADXIndicator(df['high'], df['low'], df['close'], window=window)
    df['ADX'] = adx.adx()
    df['DI_plus'] = adx.adx_pos()
    df['DI_minus'] = adx.adx_neg()
    return df


def add_supertrend(df: pd.DataFrame, period: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
    """
    Add SuperTrend indicator for clear entry/exit signals.
    SuperTrend is based on ATR and provides dynamic support/resistance levels.
    """
    df = df.copy()

    # Calculate ATR if not present
    if 'ATR' not in df.columns:
        df = add_atr(df, period)

    hl2 = (df['high'] + df['low']) / 2

    # Calculate basic upper and lower bands
    upper_band = hl2 + (multiplier * df['ATR'])
    lower_band = hl2 - (multiplier * df['ATR'])

    # Initialize SuperTrend
    supertrend = pd.Series(index=df.index, dtype=float)
    direction = pd.Series(index=df.index, dtype=int)

    for i in range(1, len(df)):
        # Current values
        curr_upper = upper_band.iloc[i]
        curr_lower = lower_band.iloc[i]
        prev_close = df['close'].iloc[i-1]
        curr_close = df['close'].iloc[i]

        # Calculate final upper and lower bands
        if i == 1:
            final_upper = curr_upper
            final_lower = curr_lower
        else:
            prev_final_upper = upper_band.iloc[i-1] if i == 1 else final_upper
            prev_final_lower = lower_band.iloc[i-1] if i == 1 else final_lower

            final_upper = curr_upper if curr_upper < prev_final_upper or prev_close > prev_final_upper else prev_final_upper
            final_lower = curr_lower if curr_lower > prev_final_lower or prev_close < prev_final_lower else prev_final_lower

        # Determine SuperTrend direction
        if i == 1:
            direction.iloc[i] = 1 if curr_close <= final_lower else -1
            supertrend.iloc[i] = final_lower if direction.iloc[i] == 1 else final_upper
        else:
            prev_direction = direction.iloc[i-1]
            prev_supertrend = supertrend.iloc[i-1]

            if prev_direction == 1 and curr_close > final_upper:
                direction.iloc[i] = -1
                supertrend.iloc[i] = final_upper
            elif prev_direction == -1 and curr_close < final_lower:
                direction.iloc[i] = 1
                supertrend.iloc[i] = final_lower
            else:
                direction.iloc[i] = prev_direction
                supertrend.iloc[i] = final_upper if direction.iloc[i] == -1 else final_lower

    df['SuperTrend'] = supertrend
    df['SuperTrend_Direction'] = direction
    df['SuperTrend_Signal'] = direction.diff()  # 2 = buy signal, -2 = sell signal

    return df


def add_vwap(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add Volume Weighted Average Price (VWAP).
    Useful for intraday support/resistance levels.
    """
    df = df.copy()

    # Reset index to work with daily periods
    df_reset = df.reset_index()

    # Handle different index names
    time_col = None
    if 'open_time' in df_reset.columns:
        time_col = 'open_time'
    elif 'index' in df_reset.columns and hasattr(df_reset['index'].iloc[0], 'date'):
        time_col = 'index'
    else:
        # Use index directly if it's datetime
        df_reset['datetime'] = df_reset.index if hasattr(df.index, 'date') else pd.to_datetime(df_reset.index)
        time_col = 'datetime'

    df_reset['date'] = df_reset[time_col].dt.date

    # Calculate VWAP for each day
    vwap_values = []
    for date in df_reset['date'].unique():
        day_data = df_reset[df_reset['date'] == date].copy()

        # Typical price
        day_data['typical_price'] = (day_data['high'] + day_data['low'] + day_data['close']) / 3
        day_data['price_volume'] = day_data['typical_price'] * day_data['volume']

        # Cumulative values
        day_data['cum_price_volume'] = day_data['price_volume'].cumsum()
        day_data['cum_volume'] = day_data['volume'].cumsum()

        # VWAP
        day_data['VWAP'] = day_data['cum_price_volume'] / day_data['cum_volume']

        vwap_values.extend(day_data['VWAP'].tolist())

    df['VWAP'] = vwap_values
    return df


def add_stochastic(df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
    """Add Stochastic Oscillator."""
    df = df.copy()
    stoch = ta.momentum.StochasticOscillator(
        df['high'], df['low'], df['close'],
        window=k_period, smooth_window=d_period
    )
    df['Stoch_K'] = stoch.stoch()
    df['Stoch_D'] = stoch.stoch_signal()
    return df


def add_williams_r(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Williams %R."""
    df = df.copy()
    try:
        df['Williams_R'] = ta.momentum.WilliamsRIndicator(
            df['high'], df['low'], df['close'], lbp=window
        ).williams_r()
    except TypeError:
        # Fallback: manual calculation
        highest_high = df['high'].rolling(window=window).max()
        lowest_low = df['low'].rolling(window=window).min()
        df['Williams_R'] = -100 * (highest_high - df['close']) / (highest_high - lowest_low)
    return df


def add_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add all technical indicators to the dataframe.
    This is the main function to use for complete analysis.
    """
    df = df.copy()

    # Trend indicators
    df = add_moving_averages(df)
    df = add_macd(df)
    df = add_adx(df)
    df = add_supertrend(df)

    # Momentum indicators
    df = add_rsi(df)
    df = add_stochastic(df)
    df = add_williams_r(df)

    # Volatility indicators
    df = add_bollinger_bands(df)
    df = add_atr(df)

    # Volume indicators
    df = add_obv(df)
    df = add_vwap(df)

    return df


def get_feature_columns() -> list:
    """Return list of feature columns for ML models."""
    return [
        'MA_Short', 'MA_Long', 'RSI', 'ATR', 'OBV',
        'Upper_BB', 'Lower_BB', 'MACD', 'MACD_signal',
        'ADX', 'DI_plus', 'DI_minus', 'SuperTrend',
        'VWAP', 'Stoch_K', 'Stoch_D', 'Williams_R'
    ]


def create_labels(df: pd.DataFrame, lookahead: int = 1) -> pd.DataFrame:
    """
    Create target labels for ML training.
    1 = price will go up, 0 = price will go down/stay same
    """
    df = df.copy()
    df['close_future'] = df['close'].shift(-lookahead)
    df['target'] = (df['close_future'] > df['close']).astype(int)
    df.dropna(inplace=True)
    return df


def get_trading_signals(df: pd.DataFrame) -> pd.DataFrame:
    """
    Generate comprehensive trading signals based on multiple indicators.
    """
    df = df.copy()
    signals = pd.DataFrame(index=df.index)

    # MA Cross signals
    signals['MA_Signal'] = df['Position']

    # RSI signals
    signals['RSI_Oversold'] = (df['RSI'] < 30).astype(int)
    signals['RSI_Overbought'] = (df['RSI'] > 70).astype(int)

    # SuperTrend signals
    signals['SuperTrend_Buy'] = (df['SuperTrend_Signal'] == 2).astype(int)
    signals['SuperTrend_Sell'] = (df['SuperTrend_Signal'] == -2).astype(int)

    # Bollinger Bands signals
    signals['BB_Oversold'] = (df['close'] < df['Lower_BB']).astype(int)
    signals['BB_Overbought'] = (df['close'] > df['Upper_BB']).astype(int)

    # ADX trend strength
    signals['Strong_Trend'] = (df['ADX'] > 25).astype(int)

    # Composite signals
    signals['Buy_Signal'] = (
        (signals['MA_Signal'] == 1) |
        (signals['SuperTrend_Buy'] == 1) |
        (signals['RSI_Oversold'] == 1)
    ).astype(int)

    signals['Sell_Signal'] = (
        (signals['MA_Signal'] == -1) |
        (signals['SuperTrend_Sell'] == 1) |
        (signals['RSI_Overbought'] == 1)
    ).astype(int)

    return signals
