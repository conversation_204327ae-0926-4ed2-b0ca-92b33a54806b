groups:
  - name: trading_system_alerts
    rules:
      # Application health alerts
      - alert: TradingAppDown
        expr: up{job="bitcoin-trading-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Trading application is down"
          description: "The Bitcoin trading application has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }} seconds."

      # Trading specific alerts
      - alert: TradingStrategyFailure
        expr: trading_strategy_errors_total > 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Trading strategy experiencing failures"
          description: "Strategy {{ $labels.strategy }} has {{ $value }} errors."

      - alert: HighDrawdown
        expr: trading_portfolio_drawdown_percent > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High portfolio drawdown"
          description: "Portfolio drawdown is {{ $value }}%."

      - alert: ExcessiveRisk
        expr: trading_portfolio_risk_percent > 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Portfolio risk exceeds limits"
          description: "Current portfolio risk is {{ $value }}%, exceeding 5% limit."

      - alert: APIRateLimitApproaching
        expr: binance_api_requests_remaining < 100
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Binance API rate limit approaching"
          description: "Only {{ $value }} API requests remaining."

      # Infrastructure alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }}."

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}%."

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full."

      - alert: DatabaseConnectionFailure
        expr: postgres_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "Cannot connect to PostgreSQL database."

      - alert: RedisConnectionFailure
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failure"
          description: "Cannot connect to Redis cache."

  - name: business_logic_alerts
    rules:
      # Market data alerts
      - alert: StaleMarketData
        expr: time() - trading_last_price_update_timestamp > 300
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Stale market data"
          description: "Market data hasn't been updated for {{ $value }} seconds."

      - alert: UnusualVolumeSpike
        expr: trading_volume_current / trading_volume_average_24h > 5
        for: 2m
        labels:
          severity: info
        annotations:
          summary: "Unusual volume spike detected"
          description: "Current volume is {{ $value }}x the 24h average."

      - alert: PriceVolatilityHigh
        expr: trading_price_volatility_percent > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High price volatility"
          description: "Price volatility is {{ $value }}%."

      # Performance alerts
      - alert: LowWinRate
        expr: trading_strategy_win_rate_percent < 40
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low strategy win rate"
          description: "Strategy {{ $labels.strategy }} win rate is {{ $value }}%."

      - alert: NegativeReturns
        expr: trading_portfolio_return_percent < -5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Negative portfolio returns"
          description: "Portfolio returns are {{ $value }}%."
