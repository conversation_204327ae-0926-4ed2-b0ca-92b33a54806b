"""
Data client for fetching and storing Bitcoin market data.
Implements WebSocket connection to Binance and SQLite storage.
"""

import sqlite3
import pandas as pd
import json
import time
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, List
try:
    from binance import ThreadedWebSocketManager
    from binance.client import Client
except ImportError:
    # Fallback for different versions of python-binance
    try:
        from binance.websockets import BinanceSocketManager as ThreadedWebSocketManager
        from binance.client import Client
    except ImportError:
        # If binance library is not available, create mock classes
        class ThreadedWebSocketManager:
            def __init__(self):
                pass
            def start(self):
                pass
            def stop(self):
                pass
            def start_kline_socket(self, callback, symbol, interval):
                pass

        class Client:
            def __init__(self, api_key=None, api_secret=None):
                pass
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BinanceDataClient:
    """
    Robust data client with WebSocket support and SQLite storage.
    Handles reconnection, deduplication, and data integrity.
    """

    def __init__(self, db_path: str = "bitcoin_data.db", api_key: str = None, api_secret: str = None):
        self.db_path = db_path
        self.client = Client(api_key, api_secret) if api_key else None
        self.twm = None
        self.is_running = False
        self._init_database()

    def _init_database(self):
        """Initialize SQLite database with schema."""
        with sqlite3.connect(self.db_path) as conn:
            # Read and execute schema
            schema_path = "data/schema.sql"
            try:
                with open(schema_path, 'r') as f:
                    schema = f.read()
                conn.executescript(schema)
                logger.info("Database initialized successfully")
            except FileNotFoundError:
                logger.warning(f"Schema file not found: {schema_path}")
                self._create_basic_schema(conn)

    def _create_basic_schema(self, conn):
        """Create basic schema if schema.sql not found."""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS candles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                open_time INTEGER NOT NULL,
                close_time INTEGER NOT NULL,
                open_price REAL NOT NULL,
                high_price REAL NOT NULL,
                low_price REAL NOT NULL,
                close_price REAL NOT NULL,
                volume REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, open_time)
            )
        """)
        conn.execute("CREATE INDEX IF NOT EXISTS idx_candles_symbol_time ON candles(symbol, open_time)")

    def fetch_historical_klines(self, symbol: str = "BTCUSDT", interval: str = "1h", limit: int = 1000) -> pd.DataFrame:
        """
        Fetch historical klines from Binance REST API.
        """
        url = f'https://api.binance.com/api/v3/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()

            df = pd.DataFrame(data, columns=[
                "open_time", "open", "high", "low", "close", "volume",
                "close_time", "quote_asset_volume", "number_of_trades",
                "taker_buy_base", "taker_buy_quote", "ignore"
            ])

            # Convert data types
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)

            df.set_index('open_time', inplace=True)
            logger.info(f"Fetched {len(df)} candles for {symbol}")
            return df

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()

    def store_candles(self, df: pd.DataFrame, symbol: str = "BTCUSDT"):
        """
        Store candles in SQLite with deduplication.
        """
        if df.empty:
            return

        with sqlite3.connect(self.db_path) as conn:
            stored_count = 0
            for idx, row in df.iterrows():
                try:
                    conn.execute("""
                        INSERT OR IGNORE INTO candles
                        (symbol, open_time, close_time, open_price, high_price,
                         low_price, close_price, volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol,
                        int(idx.timestamp() * 1000),  # Convert to milliseconds
                        int(row.get('close_time', idx).timestamp() * 1000) if hasattr(row.get('close_time', idx), 'timestamp') else int(idx.timestamp() * 1000),
                        float(row['open']),
                        float(row['high']),
                        float(row['low']),
                        float(row['close']),
                        float(row['volume'])
                    ))
                    stored_count += 1
                except Exception as e:
                    logger.warning(f"Error storing candle: {e}")

            logger.info(f"Stored {stored_count} new candles for {symbol}")

    def get_candles(self, symbol: str = "BTCUSDT", limit: int = 200) -> pd.DataFrame:
        """
        Retrieve candles from SQLite database.
        """
        with sqlite3.connect(self.db_path) as conn:
            query = """
                SELECT open_time, open_price, high_price, low_price, close_price, volume
                FROM candles
                WHERE symbol = ?
                ORDER BY open_time DESC
                LIMIT ?
            """
            df = pd.read_sql_query(query, conn, params=(symbol, limit))

            if not df.empty:
                df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                df.set_index('open_time', inplace=True)
                df = df.sort_index()  # Sort ascending for analysis

                # Rename columns to match expected format
                df.rename(columns={
                    'open_price': 'open',
                    'high_price': 'high',
                    'low_price': 'low',
                    'close_price': 'close'
                }, inplace=True)

            return df

    def start_websocket(self, symbol: str = "btcusdt", interval: str = "1h"):
        """
        Start WebSocket connection for real-time data.
        """
        if self.is_running:
            logger.warning("WebSocket already running")
            return

        def handle_socket_message(msg):
            """Handle incoming WebSocket messages."""
            try:
                kline = msg['k']
                if kline['x']:  # Only process closed candles
                    candle_data = {
                        'open_time': pd.to_datetime(kline['t'], unit='ms'),
                        'open': float(kline['o']),
                        'high': float(kline['h']),
                        'low': float(kline['l']),
                        'close': float(kline['c']),
                        'volume': float(kline['v'])
                    }

                    df = pd.DataFrame([candle_data])
                    df.set_index('open_time', inplace=True)
                    self.store_candles(df, symbol.upper())
                    logger.info(f"Stored new candle: {candle_data['close']}")

            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")

        try:
            self.twm = ThreadedWebSocketManager()
            self.twm.start()
            self.twm.start_kline_socket(
                callback=handle_socket_message,
                symbol=symbol,
                interval=interval
            )
            self.is_running = True
            logger.info(f"WebSocket started for {symbol} {interval}")

        except Exception as e:
            logger.error(f"Error starting WebSocket: {e}")

    def stop_websocket(self):
        """Stop WebSocket connection."""
        if self.twm:
            self.twm.stop()
            self.is_running = False
            logger.info("WebSocket stopped")

    def get_latest_price(self, symbol: str = "BTCUSDT") -> Optional[float]:
        """Get latest price from database or API."""
        df = self.get_candles(symbol, limit=1)
        if not df.empty:
            return float(df['close'].iloc[-1])
        return None

    def health_check(self) -> Dict:
        """Check data client health."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM candles")
            total_candles = cursor.fetchone()[0]

            cursor = conn.execute("""
                SELECT symbol, COUNT(*) as count,
                       MAX(open_time) as latest_time
                FROM candles
                GROUP BY symbol
            """)
            symbols_info = cursor.fetchall()

        return {
            'total_candles': total_candles,
            'symbols': symbols_info,
            'websocket_running': self.is_running,
            'database_path': self.db_path
        }


def main():
    """Example usage of BinanceDataClient."""
    client = BinanceDataClient()

    # Fetch and store historical data
    df = client.fetch_historical_klines("BTCUSDT", "1h", 1000)
    client.store_candles(df, "BTCUSDT")

    # Get stored data
    stored_df = client.get_candles("BTCUSDT", 100)
    print(f"Retrieved {len(stored_df)} candles from database")
    print(stored_df.tail())

    # Health check
    health = client.health_check()
    print("Health check:", health)


if __name__ == "__main__":
    main()
