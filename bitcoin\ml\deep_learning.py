"""
Deep Learning models for trading predictions using LSTM and Transformer architectures.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, MultiHeadAttention, LayerNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow not available. Install with: pip install tensorflow")

from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
from datetime import datetime


class TradingDeepLearning:
    """
    Deep Learning models for trading signal prediction.
    """
    
    def __init__(self, sequence_length: int = 60, random_state: int = 42):
        """
        Initialize deep learning models.
        
        Args:
            sequence_length: Length of input sequences
            random_state: Random seed for reproducibility
        """
        self.sequence_length = sequence_length
        self.random_state = random_state
        self.scaler = MinMaxScaler()
        self.models = {}
        
        if TENSORFLOW_AVAILABLE:
            tf.random.set_seed(random_state)
        
    def prepare_sequences(self, data: pd.DataFrame, target_col: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM training."""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for deep learning models")
        
        # Scale data
        scaled_data = self.scaler.fit_transform(data)
        
        X, y = [], []
        
        for i in range(self.sequence_length, len(scaled_data)):
            X.append(scaled_data[i-self.sequence_length:i])
            
            # Target is next period's price change
            if target_col in data.columns:
                target_idx = data.columns.get_loc(target_col)
                current_price = data.iloc[i-1, target_idx]
                next_price = data.iloc[i, target_idx]
                price_change = (next_price - current_price) / current_price
                y.append(price_change)
            else:
                y.append(scaled_data[i, 0])  # Default to first column
        
        return np.array(X), np.array(y)
    
    def create_lstm_model(self, input_shape: Tuple[int, int], model_type: str = 'simple') -> Model:
        """Create LSTM model architecture."""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for LSTM models")
        
        if model_type == 'simple':
            model = Sequential([
                LSTM(50, return_sequences=True, input_shape=input_shape),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
            
        elif model_type == 'deep':
            model = Sequential([
                LSTM(100, return_sequences=True, input_shape=input_shape),
                Dropout(0.3),
                LSTM(100, return_sequences=True),
                Dropout(0.3),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(50),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
            
        elif model_type == 'bidirectional':
            from tensorflow.keras.layers import Bidirectional
            
            model = Sequential([
                Bidirectional(LSTM(50, return_sequences=True), input_shape=input_shape),
                Dropout(0.2),
                Bidirectional(LSTM(50, return_sequences=False)),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
        
        else:
            raise ValueError(f"Unknown model type: {model_type}")
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def create_transformer_model(self, input_shape: Tuple[int, int]) -> Model:
        """Create Transformer model for time series prediction."""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for Transformer models")
        
        # Input layer
        inputs = Input(shape=input_shape)
        
        # Multi-head attention
        attention_output = MultiHeadAttention(
            num_heads=8,
            key_dim=64
        )(inputs, inputs)
        
        # Add & Norm
        attention_output = LayerNormalization()(inputs + attention_output)
        
        # Feed forward
        ffn_output = Dense(128, activation='relu')(attention_output)
        ffn_output = Dense(input_shape[-1])(ffn_output)
        
        # Add & Norm
        ffn_output = LayerNormalization()(attention_output + ffn_output)
        
        # Global average pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn_output)
        
        # Output layers
        outputs = Dense(50, activation='relu')(pooled)
        outputs = Dropout(0.2)(outputs)
        outputs = Dense(1)(outputs)
        
        model = Model(inputs=inputs, outputs=outputs)
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train_model(self, 
                   X_train: np.ndarray, 
                   y_train: np.ndarray,
                   X_val: np.ndarray = None,
                   y_val: np.ndarray = None,
                   model_type: str = 'simple',
                   epochs: int = 100,
                   batch_size: int = 32) -> Dict[str, Any]:
        """Train deep learning model."""
        if not TENSORFLOW_AVAILABLE:
            return {"error": "TensorFlow not available"}
        
        print(f"🤖 Training {model_type} model...")
        
        # Create model
        if model_type == 'transformer':
            model = self.create_transformer_model((X_train.shape[1], X_train.shape[2]))
        else:
            model = self.create_lstm_model((X_train.shape[1], X_train.shape[2]), model_type)
        
        # Callbacks
        callbacks = [
            EarlyStopping(patience=20, restore_best_weights=True),
            ReduceLROnPlateau(patience=10, factor=0.5)
        ]
        
        # Validation data
        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)
        
        # Train model
        history = model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )
        
        # Store model
        self.models[model_type] = model
        
        # Evaluate
        train_loss = model.evaluate(X_train, y_train, verbose=0)
        
        results = {
            'model': model,
            'history': history,
            'train_loss': train_loss[0],
            'train_mae': train_loss[1],
            'model_type': model_type
        }
        
        if validation_data:
            val_loss = model.evaluate(X_val, y_val, verbose=0)
            results.update({
                'val_loss': val_loss[0],
                'val_mae': val_loss[1]
            })
        
        print(f"✅ {model_type} model trained - Loss: {train_loss[0]:.4f}")
        
        return results
    
    def predict_next_periods(self, 
                           model: Model, 
                           last_sequence: np.ndarray, 
                           n_periods: int = 5) -> np.ndarray:
        """Predict next n periods using trained model."""
        if not TENSORFLOW_AVAILABLE:
            return np.array([])
        
        predictions = []
        current_sequence = last_sequence.copy()
        
        for _ in range(n_periods):
            # Predict next value
            pred = model.predict(current_sequence.reshape(1, *current_sequence.shape), verbose=0)
            predictions.append(pred[0, 0])
            
            # Update sequence (simple approach - append prediction)
            new_row = current_sequence[-1].copy()
            new_row[0] = pred[0, 0]  # Update first feature with prediction
            
            # Shift sequence
            current_sequence = np.vstack([current_sequence[1:], new_row])
        
        return np.array(predictions)
    
    def evaluate_model(self, 
                      model: Model, 
                      X_test: np.ndarray, 
                      y_test: np.ndarray) -> Dict[str, float]:
        """Evaluate model performance."""
        if not TENSORFLOW_AVAILABLE:
            return {}
        
        # Predictions
        y_pred = model.predict(X_test, verbose=0)
        
        # Metrics
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        
        # Direction accuracy
        y_test_direction = np.sign(y_test)
        y_pred_direction = np.sign(y_pred.flatten())
        direction_accuracy = np.mean(y_test_direction == y_pred_direction)
        
        return {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'direction_accuracy': direction_accuracy
        }
    
    def train_ensemble_models(self, 
                            X_train: np.ndarray, 
                            y_train: np.ndarray,
                            X_val: np.ndarray = None,
                            y_val: np.ndarray = None) -> Dict[str, Any]:
        """Train ensemble of different deep learning models."""
        if not TENSORFLOW_AVAILABLE:
            return {"error": "TensorFlow not available"}
        
        model_types = ['simple', 'deep', 'bidirectional']
        results = {}
        
        for model_type in model_types:
            try:
                result = self.train_model(
                    X_train, y_train, X_val, y_val, 
                    model_type=model_type, epochs=50
                )
                results[model_type] = result
            except Exception as e:
                print(f"❌ Failed to train {model_type}: {e}")
                continue
        
        return results
    
    def generate_predictions_report(self, 
                                  model_results: Dict[str, Any], 
                                  test_data: Tuple[np.ndarray, np.ndarray]) -> str:
        """Generate comprehensive predictions report."""
        if not model_results:
            return "❌ No model results available"
        
        X_test, y_test = test_data
        
        report = f"""
🤖 DEEP LEARNING PREDICTIONS REPORT
{'='*50}

📊 MODEL PERFORMANCE
{'-'*30}
"""
        
        for model_name, result in model_results.items():
            if 'error' in result:
                continue
            
            model = result['model']
            metrics = self.evaluate_model(model, X_test, y_test)
            
            report += f"""
{model_name.upper()} MODEL:
  Training Loss: {result['train_loss']:.4f}
  Training MAE: {result['train_mae']:.4f}
  Test RMSE: {metrics.get('rmse', 0):.4f}
  Test MAE: {metrics.get('mae', 0):.4f}
  Direction Accuracy: {metrics.get('direction_accuracy', 0)*100:.1f}%
"""
        
        report += f"""

🎯 ENSEMBLE PREDICTIONS
{'-'*30}
"""
        
        # Ensemble prediction
        if len(model_results) > 1:
            ensemble_preds = []
            for model_name, result in model_results.items():
                if 'error' not in result:
                    model = result['model']
                    pred = model.predict(X_test, verbose=0)
                    ensemble_preds.append(pred.flatten())
            
            if ensemble_preds:
                ensemble_pred = np.mean(ensemble_preds, axis=0)
                ensemble_metrics = {
                    'mse': mean_squared_error(y_test, ensemble_pred),
                    'mae': mean_absolute_error(y_test, ensemble_pred),
                    'direction_accuracy': np.mean(np.sign(y_test) == np.sign(ensemble_pred))
                }
                
                report += f"""
ENSEMBLE PERFORMANCE:
  RMSE: {np.sqrt(ensemble_metrics['mse']):.4f}
  MAE: {ensemble_metrics['mae']:.4f}
  Direction Accuracy: {ensemble_metrics['direction_accuracy']*100:.1f}%
"""
        
        report += f"""

💡 INTERPRETATION
{'-'*30}
• Direction accuracy >55% indicates predictive value
• Lower RMSE/MAE indicates better price prediction
• Ensemble typically provides more robust predictions

⚠️ DISCLAIMER
{'-'*30}
Deep learning predictions are based on historical patterns.
Market conditions can change rapidly. Use as one factor in trading decisions.
"""
        
        return report


def main():
    """Example usage of TradingDeepLearning."""
    if not TENSORFLOW_AVAILABLE:
        print("❌ TensorFlow not available. Install with: pip install tensorflow")
        return
    
    from data.client import BinanceDataClient
    from ml.feature_engineering import AdvancedFeatureEngineer
    
    # Load data
    client = BinanceDataClient()
    df = client.get_candles("BTCUSDT", limit=2000)
    
    if df.empty:
        print("No data available. Fetching from API...")
        df = client.fetch_historical_klines("BTCUSDT", "1h", 2000)
        client.store_candles(df, "BTCUSDT")
        df = client.get_candles("BTCUSDT", limit=2000)
    
    print(f"Loaded {len(df)} candles for deep learning")
    
    # Feature engineering
    engineer = AdvancedFeatureEngineer()
    df_features = engineer.create_all_features(df)
    
    # Select key features for deep learning
    feature_cols = [
        'close', 'volume', 'returns', 'volatility_20', 
        'RSI', 'MA_Short', 'MA_Long', 'momentum_5'
    ]
    
    df_clean = df_features[feature_cols].dropna()
    
    if len(df_clean) < 500:
        print("❌ Insufficient data for deep learning")
        return
    
    # Initialize deep learning
    dl = TradingDeepLearning(sequence_length=60)
    
    # Prepare sequences
    X, y = dl.prepare_sequences(df_clean, target_col='close')
    
    print(f"Prepared {len(X)} sequences for training")
    
    # Split data
    train_size = int(len(X) * 0.7)
    val_size = int(len(X) * 0.85)
    
    X_train, y_train = X[:train_size], y[:train_size]
    X_val, y_val = X[train_size:val_size], y[train_size:val_size]
    X_test, y_test = X[val_size:], y[val_size:]
    
    print(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
    
    # Train models
    results = dl.train_ensemble_models(X_train, y_train, X_val, y_val)
    
    # Generate report
    report = dl.generate_predictions_report(results, (X_test, y_test))
    print(report)
    
    # Save results
    filename = f"deep_learning_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(filename, 'w') as f:
        f.write(report)
    print(f"\n📄 Report saved to: {filename}")


if __name__ == "__main__":
    main()
