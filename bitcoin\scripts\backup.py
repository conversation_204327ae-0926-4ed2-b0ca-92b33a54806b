#!/usr/bin/env python3
"""
Automated backup system for Bitcoin Trading System.
Supports local, S3, and Google Cloud Storage backends.
"""

import os
import sys
import gzip
import shutil
import sqlite3
import subprocess
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_AVAILABLE = True
except ImportError:
    AWS_AVAILABLE = False

try:
    from google.cloud import storage as gcs
    GCP_AVAILABLE = True
except ImportError:
    GCP_AVAILABLE = False


class BackupManager:
    """
    Comprehensive backup manager for the trading system.
    """
    
    def __init__(self, config_path: str = "config/backup.json"):
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        
    def _load_config(self, config_path: str) -> Dict:
        """Load backup configuration."""
        default_config = {
            "retention_days": 30,
            "compression": True,
            "backends": {
                "local": {
                    "enabled": True,
                    "path": "/backups"
                },
                "s3": {
                    "enabled": False,
                    "bucket": "",
                    "region": "us-east-1",
                    "prefix": "trading-backups/"
                },
                "gcs": {
                    "enabled": False,
                    "bucket": "",
                    "prefix": "trading-backups/"
                }
            },
            "databases": {
                "sqlite": {
                    "enabled": True,
                    "path": "data/trading.db"
                },
                "postgres": {
                    "enabled": False,
                    "host": "localhost",
                    "port": 5432,
                    "database": "trading",
                    "username": "trading_user"
                }
            },
            "directories": [
                "data/",
                "logs/",
                "reports/",
                "config/"
            ]
        }
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for backup operations."""
        logger = logging.getLogger('backup_manager')
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # File handler
        fh = logging.FileHandler('logs/backup.log')
        fh.setLevel(logging.INFO)
        
        # Console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)
        
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        return logger
    
    def create_backup(self) -> Dict[str, str]:
        """Create comprehensive backup."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"trading_backup_{timestamp}"
        
        self.logger.info(f"Starting backup: {backup_name}")
        
        # Create temporary backup directory
        temp_dir = Path(f"/tmp/{backup_name}")
        temp_dir.mkdir(exist_ok=True)
        
        backup_info = {
            'name': backup_name,
            'timestamp': timestamp,
            'files': [],
            'size': 0,
            'status': 'in_progress'
        }
        
        try:
            # Backup databases
            if self.config['databases']['sqlite']['enabled']:
                self._backup_sqlite(temp_dir, backup_info)
            
            if self.config['databases']['postgres']['enabled']:
                self._backup_postgres(temp_dir, backup_info)
            
            # Backup directories
            for directory in self.config['directories']:
                if os.path.exists(directory):
                    self._backup_directory(directory, temp_dir, backup_info)
            
            # Create metadata file
            self._create_metadata(temp_dir, backup_info)
            
            # Compress if enabled
            if self.config['compression']:
                compressed_file = self._compress_backup(temp_dir)
                backup_info['compressed_file'] = str(compressed_file)
                backup_info['size'] = compressed_file.stat().st_size
            
            # Upload to configured backends
            self._upload_backup(backup_info)
            
            # Cleanup old backups
            self._cleanup_old_backups()
            
            backup_info['status'] = 'completed'
            self.logger.info(f"Backup completed successfully: {backup_name}")
            
        except Exception as e:
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
            self.logger.error(f"Backup failed: {e}")
            
        finally:
            # Cleanup temporary directory
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
        
        return backup_info
    
    def _backup_sqlite(self, temp_dir: Path, backup_info: Dict):
        """Backup SQLite database."""
        sqlite_config = self.config['databases']['sqlite']
        db_path = sqlite_config['path']
        
        if not os.path.exists(db_path):
            self.logger.warning(f"SQLite database not found: {db_path}")
            return
        
        backup_path = temp_dir / "trading.db"
        
        # Use SQLite backup API for consistent backup
        source_conn = sqlite3.connect(db_path)
        backup_conn = sqlite3.connect(str(backup_path))
        
        source_conn.backup(backup_conn)
        
        source_conn.close()
        backup_conn.close()
        
        backup_info['files'].append(str(backup_path))
        self.logger.info(f"SQLite backup created: {backup_path}")
    
    def _backup_postgres(self, temp_dir: Path, backup_info: Dict):
        """Backup PostgreSQL database."""
        pg_config = self.config['databases']['postgres']
        
        backup_path = temp_dir / "postgres_dump.sql"
        
        # Use pg_dump for PostgreSQL backup
        cmd = [
            'pg_dump',
            '-h', pg_config['host'],
            '-p', str(pg_config['port']),
            '-U', pg_config['username'],
            '-d', pg_config['database'],
            '-f', str(backup_path),
            '--no-password'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            backup_info['files'].append(str(backup_path))
            self.logger.info(f"PostgreSQL backup created: {backup_path}")
        except subprocess.CalledProcessError as e:
            self.logger.error(f"PostgreSQL backup failed: {e}")
    
    def _backup_directory(self, source_dir: str, temp_dir: Path, backup_info: Dict):
        """Backup directory contents."""
        if not os.path.exists(source_dir):
            self.logger.warning(f"Directory not found: {source_dir}")
            return
        
        dest_dir = temp_dir / source_dir.replace('/', '_').strip('_')
        shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)
        
        backup_info['files'].append(str(dest_dir))
        self.logger.info(f"Directory backup created: {source_dir} -> {dest_dir}")
    
    def _create_metadata(self, temp_dir: Path, backup_info: Dict):
        """Create backup metadata file."""
        metadata = {
            'backup_name': backup_info['name'],
            'timestamp': backup_info['timestamp'],
            'created_at': datetime.now().isoformat(),
            'system_info': {
                'hostname': os.uname().nodename,
                'platform': os.uname().sysname,
                'python_version': sys.version
            },
            'files': backup_info['files'],
            'config': self.config
        }
        
        metadata_path = temp_dir / "backup_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        backup_info['files'].append(str(metadata_path))
        self.logger.info(f"Metadata created: {metadata_path}")
    
    def _compress_backup(self, temp_dir: Path) -> Path:
        """Compress backup directory."""
        archive_path = temp_dir.parent / f"{temp_dir.name}.tar.gz"
        
        shutil.make_archive(
            str(archive_path.with_suffix('')),
            'gztar',
            str(temp_dir.parent),
            str(temp_dir.name)
        )
        
        self.logger.info(f"Backup compressed: {archive_path}")
        return archive_path
    
    def _upload_backup(self, backup_info: Dict):
        """Upload backup to configured backends."""
        if 'compressed_file' in backup_info:
            file_path = backup_info['compressed_file']
        else:
            # If not compressed, create tar archive
            file_path = f"/tmp/{backup_info['name']}.tar"
            # Implementation for uncompressed archive
        
        # Local backend
        if self.config['backends']['local']['enabled']:
            self._upload_to_local(file_path, backup_info)
        
        # S3 backend
        if self.config['backends']['s3']['enabled'] and AWS_AVAILABLE:
            self._upload_to_s3(file_path, backup_info)
        
        # GCS backend
        if self.config['backends']['gcs']['enabled'] and GCP_AVAILABLE:
            self._upload_to_gcs(file_path, backup_info)
    
    def _upload_to_local(self, file_path: str, backup_info: Dict):
        """Upload backup to local storage."""
        local_config = self.config['backends']['local']
        dest_dir = Path(local_config['path'])
        dest_dir.mkdir(parents=True, exist_ok=True)
        
        dest_path = dest_dir / Path(file_path).name
        shutil.copy2(file_path, dest_path)
        
        self.logger.info(f"Backup uploaded to local storage: {dest_path}")
    
    def _upload_to_s3(self, file_path: str, backup_info: Dict):
        """Upload backup to AWS S3."""
        s3_config = self.config['backends']['s3']
        
        try:
            s3_client = boto3.client('s3', region_name=s3_config['region'])
            
            key = f"{s3_config['prefix']}{Path(file_path).name}"
            
            s3_client.upload_file(
                file_path,
                s3_config['bucket'],
                key,
                ExtraArgs={
                    'ServerSideEncryption': 'AES256',
                    'Metadata': {
                        'backup_name': backup_info['name'],
                        'timestamp': backup_info['timestamp']
                    }
                }
            )
            
            self.logger.info(f"Backup uploaded to S3: s3://{s3_config['bucket']}/{key}")
            
        except ClientError as e:
            self.logger.error(f"S3 upload failed: {e}")
    
    def _upload_to_gcs(self, file_path: str, backup_info: Dict):
        """Upload backup to Google Cloud Storage."""
        gcs_config = self.config['backends']['gcs']
        
        try:
            client = gcs.Client()
            bucket = client.bucket(gcs_config['bucket'])
            
            blob_name = f"{gcs_config['prefix']}{Path(file_path).name}"
            blob = bucket.blob(blob_name)
            
            blob.metadata = {
                'backup_name': backup_info['name'],
                'timestamp': backup_info['timestamp']
            }
            
            blob.upload_from_filename(file_path)
            
            self.logger.info(f"Backup uploaded to GCS: gs://{gcs_config['bucket']}/{blob_name}")
            
        except Exception as e:
            self.logger.error(f"GCS upload failed: {e}")
    
    def _cleanup_old_backups(self):
        """Remove old backups based on retention policy."""
        retention_days = self.config['retention_days']
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        # Cleanup local backups
        if self.config['backends']['local']['enabled']:
            local_path = Path(self.config['backends']['local']['path'])
            if local_path.exists():
                for backup_file in local_path.glob('trading_backup_*.tar.gz'):
                    if backup_file.stat().st_mtime < cutoff_date.timestamp():
                        backup_file.unlink()
                        self.logger.info(f"Removed old local backup: {backup_file}")
        
        # Cleanup S3 backups
        if self.config['backends']['s3']['enabled'] and AWS_AVAILABLE:
            self._cleanup_s3_backups(cutoff_date)
        
        # Cleanup GCS backups
        if self.config['backends']['gcs']['enabled'] and GCP_AVAILABLE:
            self._cleanup_gcs_backups(cutoff_date)
    
    def _cleanup_s3_backups(self, cutoff_date: datetime):
        """Cleanup old S3 backups."""
        s3_config = self.config['backends']['s3']
        
        try:
            s3_client = boto3.client('s3', region_name=s3_config['region'])
            
            response = s3_client.list_objects_v2(
                Bucket=s3_config['bucket'],
                Prefix=s3_config['prefix']
            )
            
            for obj in response.get('Contents', []):
                if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                    s3_client.delete_object(
                        Bucket=s3_config['bucket'],
                        Key=obj['Key']
                    )
                    self.logger.info(f"Removed old S3 backup: {obj['Key']}")
                    
        except ClientError as e:
            self.logger.error(f"S3 cleanup failed: {e}")
    
    def _cleanup_gcs_backups(self, cutoff_date: datetime):
        """Cleanup old GCS backups."""
        gcs_config = self.config['backends']['gcs']
        
        try:
            client = gcs.Client()
            bucket = client.bucket(gcs_config['bucket'])
            
            for blob in bucket.list_blobs(prefix=gcs_config['prefix']):
                if blob.time_created.replace(tzinfo=None) < cutoff_date:
                    blob.delete()
                    self.logger.info(f"Removed old GCS backup: {blob.name}")
                    
        except Exception as e:
            self.logger.error(f"GCS cleanup failed: {e}")


def main():
    """Main backup execution."""
    backup_manager = BackupManager()
    
    try:
        result = backup_manager.create_backup()
        
        if result['status'] == 'completed':
            print(f"✅ Backup completed successfully: {result['name']}")
            print(f"📊 Backup size: {result.get('size', 0) / 1024 / 1024:.2f} MB")
        else:
            print(f"❌ Backup failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Backup system error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
