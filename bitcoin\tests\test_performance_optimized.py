"""
Tests for performance-optimized indicators.
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from indicators.performance_optimized import (
    fast_sma, fast_ema, fast_rsi, fast_atr, fast_bollinger_bands,
    fast_macd, OptimizedIndicators
)


class TestOptimizedIndicators:
    """Test cases for optimized technical indicators."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        np.random.seed(42)
        n = 100
        dates = pd.date_range('2024-01-01', periods=n, freq='H')
        
        prices = 50000 + np.cumsum(np.random.randn(n) * 100)
        data = {
            'open': prices,
            'high': prices * (1 + np.abs(np.random.randn(n) * 0.01)),
            'low': prices * (1 - np.abs(np.random.randn(n) * 0.01)),
            'close': prices,
            'volume': 1000 + np.random.randint(-100, 100, n)
        }
        
        return pd.DataFrame(data, index=dates)
    
    def test_fast_sma(self):
        """Test fast SMA calculation."""
        prices = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], dtype=float)
        window = 3
        
        sma = fast_sma(prices, window)
        
        # Check that first values are NaN
        assert np.isnan(sma[0])
        assert np.isnan(sma[1])
        
        # Check calculated values
        assert abs(sma[2] - 2.0) < 1e-10  # (1+2+3)/3 = 2
        assert abs(sma[3] - 3.0) < 1e-10  # (2+3+4)/3 = 3
        assert abs(sma[9] - 9.0) < 1e-10  # (8+9+10)/3 = 9
    
    def test_fast_ema(self):
        """Test fast EMA calculation."""
        prices = np.array([1, 2, 3, 4, 5], dtype=float)
        window = 3
        
        ema = fast_ema(prices, window)
        
        # EMA should start with first price
        assert ema[0] == 1.0
        
        # Should have no NaN values
        assert not np.isnan(ema).any()
        
        # Should be increasing for increasing prices
        assert ema[4] > ema[0]
    
    def test_fast_rsi(self):
        """Test fast RSI calculation."""
        # Create trending up data
        prices = np.array([50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60] * 2, dtype=float)
        
        rsi = fast_rsi(prices, window=14)
        
        # RSI should be between 0 and 100
        valid_rsi = rsi[~np.isnan(rsi)]
        assert (valid_rsi >= 0).all()
        assert (valid_rsi <= 100).all()
        
        # For trending up data, RSI should be > 50
        assert valid_rsi[-1] > 50
    
    def test_fast_atr(self):
        """Test fast ATR calculation."""
        n = 20
        high = np.random.uniform(100, 110, n)
        low = np.random.uniform(90, 100, n)
        close = np.random.uniform(95, 105, n)
        
        atr = fast_atr(high, low, close, window=14)
        
        # ATR should be positive
        valid_atr = atr[~np.isnan(atr)]
        assert (valid_atr > 0).all()
        
        # Should have reasonable values
        assert valid_atr.max() < 50  # Shouldn't be too large
    
    def test_fast_bollinger_bands(self):
        """Test fast Bollinger Bands calculation."""
        prices = np.array([50, 51, 49, 52, 48, 53, 47, 54, 46, 55] * 3, dtype=float)
        
        upper, middle, lower = fast_bollinger_bands(prices, window=10, std_dev=2.0)
        
        # Remove NaN values
        valid_idx = ~np.isnan(upper)
        upper_valid = upper[valid_idx]
        middle_valid = middle[valid_idx]
        lower_valid = lower[valid_idx]
        
        # Upper should be above middle, middle above lower
        assert (upper_valid > middle_valid).all()
        assert (middle_valid > lower_valid).all()
        
        # Bands should be reasonable
        assert len(upper_valid) > 0
    
    def test_fast_macd(self):
        """Test fast MACD calculation."""
        prices = np.array(range(1, 51), dtype=float)  # Trending up
        
        macd, signal, histogram = fast_macd(prices, 12, 26, 9)
        
        # Should have some valid values
        assert not np.isnan(macd).all()
        assert not np.isnan(signal).all()
        assert not np.isnan(histogram).all()
        
        # Histogram should be MACD - Signal
        valid_idx = ~(np.isnan(macd) | np.isnan(signal) | np.isnan(histogram))
        if valid_idx.any():
            np.testing.assert_array_almost_equal(
                histogram[valid_idx], 
                macd[valid_idx] - signal[valid_idx],
                decimal=10
            )
    
    def test_optimized_indicators_integration(self, sample_data):
        """Test full optimized indicators integration."""
        df = OptimizedIndicators.add_optimized_indicators(sample_data)
        
        # Check that all expected columns are present
        expected_columns = [
            'MA_Short', 'MA_Long', 'RSI', 'ATR', 'Upper_BB', 'Middle_BB', 'Lower_BB',
            'MACD', 'MACD_signal', 'MACD_hist', 'VWAP', 'OBV', 'SuperTrend',
            'SuperTrend_Direction', 'SuperTrend_Signal', 'Signal', 'Position'
        ]
        
        for col in expected_columns:
            assert col in df.columns, f"Missing column: {col}"
        
        # Check that we have some non-null values
        for col in expected_columns:
            assert not df[col].isna().all(), f"All values are NaN for {col}"
    
    def test_performance_comparison(self, sample_data):
        """Test that optimized indicators produce similar results to standard ones."""
        try:
            from indicators.ta_wrappers import add_all_indicators
            
            # Get results from both implementations
            df_standard = add_all_indicators(sample_data.copy())
            df_optimized = OptimizedIndicators.add_optimized_indicators(sample_data.copy())
            
            # Compare key indicators (allowing for small numerical differences)
            indicators_to_compare = ['MA_Short', 'MA_Long', 'RSI']
            
            for indicator in indicators_to_compare:
                if indicator in df_standard.columns and indicator in df_optimized.columns:
                    # Get valid values from both
                    std_values = df_standard[indicator].dropna()
                    opt_values = df_optimized[indicator].dropna()
                    
                    if len(std_values) > 0 and len(opt_values) > 0:
                        # Compare last few values (most important)
                        min_len = min(len(std_values), len(opt_values))
                        if min_len >= 5:
                            std_last = std_values.iloc[-5:].values
                            opt_last = opt_values.iloc[-5:].values
                            
                            # Allow 1% difference due to implementation differences
                            np.testing.assert_allclose(
                                std_last, opt_last, rtol=0.01,
                                err_msg=f"Mismatch in {indicator}"
                            )
        
        except ImportError:
            # Skip if standard indicators not available
            pytest.skip("Standard indicators not available for comparison")
    
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Empty array
        empty_array = np.array([], dtype=float)
        assert np.isnan(fast_sma(empty_array, 5)).all()
        
        # Window larger than data
        small_array = np.array([1, 2, 3], dtype=float)
        result = fast_sma(small_array, 10)
        assert np.isnan(result).all()
        
        # Array with NaN values
        nan_array = np.array([1, 2, np.nan, 4, 5], dtype=float)
        result = fast_sma(nan_array, 3)
        # Should handle NaN gracefully
        assert len(result) == len(nan_array)
    
    def test_data_types(self, sample_data):
        """Test that output data types are correct."""
        df = OptimizedIndicators.add_optimized_indicators(sample_data)
        
        # All indicator columns should be numeric
        numeric_columns = [
            'MA_Short', 'MA_Long', 'RSI', 'ATR', 'Upper_BB', 'Middle_BB', 'Lower_BB',
            'MACD', 'MACD_signal', 'MACD_hist', 'VWAP', 'OBV', 'SuperTrend'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                assert pd.api.types.is_numeric_dtype(df[col]), f"{col} should be numeric"
    
    def test_signal_generation(self, sample_data):
        """Test trading signal generation."""
        df = OptimizedIndicators.add_optimized_indicators(sample_data)
        
        # Signal should be 0 or 1
        signals = df['Signal'].dropna().unique()
        assert all(s in [0, 1] for s in signals), "Signal should only contain 0 or 1"
        
        # Position should be -1, 0, or 1
        positions = df['Position'].dropna().unique()
        assert all(p in [-1, 0, 1] for p in positions), "Position should be -1, 0, or 1"
    
    def test_memory_efficiency(self):
        """Test memory efficiency with large datasets."""
        # Create large dataset
        n = 10000
        large_data = pd.DataFrame({
            'open': np.random.randn(n) + 50000,
            'high': np.random.randn(n) + 50100,
            'low': np.random.randn(n) + 49900,
            'close': np.random.randn(n) + 50000,
            'volume': np.random.randint(1000, 2000, n)
        })
        
        # Should complete without memory errors
        result = OptimizedIndicators.add_optimized_indicators(large_data)
        
        # Check result is valid
        assert len(result) == n
        assert 'RSI' in result.columns
        assert not result['RSI'].isna().all()


def test_benchmark_function():
    """Test the benchmark function."""
    try:
        from indicators.performance_optimized import benchmark_performance
        
        # Should run without errors and return a speedup ratio
        speedup = benchmark_performance()
        
        # Speedup should be positive
        assert speedup > 0
        
        # Optimized version should be at least as fast (speedup >= 1)
        assert speedup >= 0.5  # Allow some variance in timing
        
    except ImportError:
        pytest.skip("Benchmark dependencies not available")


if __name__ == "__main__":
    pytest.main([__file__])
