import psycopg2
from datetime import datetime, timedelta

conn = psycopg2.connect("dbname=seubanco user=usuario password=senha")
cur = conn.cursor()

# Buscar partidas da próxima semana
start_date = datetime.now()
end_date = start_date + timedelta(days=7)

cur.execute("""
    SELECT m.id, t1.name, t2.name, m.match_date
    FROM matches m
    JOIN teams t1 ON m.home_team_id = t1.id
    JOIN teams t2 ON m.away_team_id = t2.id
    WHERE m.match_date BETWEEN %s AND %s
""", (start_date, end_date))

matches = cur.fetchall()

for match in matches:
    match_id, home, away, date = match
    message = f"Próximo jogo: {home} vs {away} em {date.strftime('%d/%m %H:%M')}"
    # enviar para Discord (chame sua função de webhook aqui)

    # Salvar notificação para não repetir
    cur.execute("INSERT INTO notifications (match_id) VALUES (%s)", (match_id,))

conn.commit()
cur.close()
conn.close()
