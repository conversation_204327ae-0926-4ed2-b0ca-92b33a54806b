"""
Comprehensive Notification and Alert System.
Supports email, SMS, Slack, Discord, Telegram, and webhook notifications.
"""

import smtplib
import requests
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio
import aiohttp

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class NotificationType(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    SUCCESS = "success"


class NotificationChannel(Enum):
    EMAIL = "email"
    SMS = "sms"
    SLACK = "slack"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    WEBHOOK = "webhook"
    PUSH = "push"


@dataclass
class Notification:
    title: str
    message: str
    notification_type: NotificationType
    channels: List[NotificationChannel]
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class NotificationManager:
    """
    Manages all notification channels and alert delivery.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._default_config()
        self.logger = logging.getLogger(__name__)
        self.notification_history: List[Notification] = []
        
    def _default_config(self) -> Dict[str, Any]:
        """Default notification configuration."""
        return {
            'email': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'from_email': '',
                'to_emails': []
            },
            'slack': {
                'enabled': False,
                'webhook_url': '',
                'channel': '#trading-alerts',
                'username': 'TradingBot'
            },
            'discord': {
                'enabled': False,
                'webhook_url': ''
            },
            'telegram': {
                'enabled': False,
                'bot_token': '',
                'chat_ids': []
            },
            'webhook': {
                'enabled': False,
                'urls': []
            },
            'rate_limits': {
                'max_per_minute': 10,
                'max_per_hour': 100
            }
        }
    
    async def send_notification(self, notification: Notification) -> Dict[str, bool]:
        """
        Send notification through specified channels.
        
        Args:
            notification: Notification object
            
        Returns:
            Dict with success status for each channel
        """
        results = {}
        
        for channel in notification.channels:
            try:
                if channel == NotificationChannel.EMAIL:
                    success = await self._send_email(notification)
                elif channel == NotificationChannel.SLACK:
                    success = await self._send_slack(notification)
                elif channel == NotificationChannel.DISCORD:
                    success = await self._send_discord(notification)
                elif channel == NotificationChannel.TELEGRAM:
                    success = await self._send_telegram(notification)
                elif channel == NotificationChannel.WEBHOOK:
                    success = await self._send_webhook(notification)
                else:
                    success = False
                    
                results[channel.value] = success
                
            except Exception as e:
                self.logger.error(f"Failed to send {channel.value} notification: {e}")
                results[channel.value] = False
        
        # Store in history
        self.notification_history.append(notification)
        
        return results
    
    async def _send_email(self, notification: Notification) -> bool:
        """Send email notification."""
        if not self.config['email']['enabled']:
            return False
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config['email']['from_email']
            msg['To'] = ', '.join(self.config['email']['to_emails'])
            msg['Subject'] = f"[{notification.notification_type.value.upper()}] {notification.title}"
            
            # Create HTML body
            html_body = self._create_email_html(notification)
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            server = smtplib.SMTP(
                self.config['email']['smtp_server'],
                self.config['email']['smtp_port']
            )
            server.starttls()
            server.login(
                self.config['email']['username'],
                self.config['email']['password']
            )
            
            text = msg.as_string()
            server.sendmail(
                self.config['email']['from_email'],
                self.config['email']['to_emails'],
                text
            )
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Email sending failed: {e}")
            return False
    
    async def _send_slack(self, notification: Notification) -> bool:
        """Send Slack notification."""
        if not self.config['slack']['enabled']:
            return False
        
        try:
            # Color coding based on notification type
            color_map = {
                NotificationType.INFO: "#36a64f",
                NotificationType.WARNING: "#ff9500",
                NotificationType.ERROR: "#ff0000",
                NotificationType.CRITICAL: "#8B0000",
                NotificationType.SUCCESS: "#00ff00"
            }
            
            payload = {
                "channel": self.config['slack']['channel'],
                "username": self.config['slack']['username'],
                "attachments": [{
                    "color": color_map.get(notification.notification_type, "#36a64f"),
                    "title": notification.title,
                    "text": notification.message,
                    "footer": "Bitcoin Trading System",
                    "ts": int(notification.timestamp.timestamp())
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.config['slack']['webhook_url'],
                    json=payload
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"Slack sending failed: {e}")
            return False
    
    async def _send_discord(self, notification: Notification) -> bool:
        """Send Discord notification."""
        if not self.config['discord']['enabled']:
            return False
        
        try:
            # Color coding for Discord embeds
            color_map = {
                NotificationType.INFO: 0x3498db,
                NotificationType.WARNING: 0xf39c12,
                NotificationType.ERROR: 0xe74c3c,
                NotificationType.CRITICAL: 0x8b0000,
                NotificationType.SUCCESS: 0x2ecc71
            }
            
            payload = {
                "embeds": [{
                    "title": notification.title,
                    "description": notification.message,
                    "color": color_map.get(notification.notification_type, 0x3498db),
                    "footer": {
                        "text": "Bitcoin Trading System"
                    },
                    "timestamp": notification.timestamp.isoformat()
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.config['discord']['webhook_url'],
                    json=payload
                ) as response:
                    return response.status == 204
                    
        except Exception as e:
            self.logger.error(f"Discord sending failed: {e}")
            return False
    
    async def _send_telegram(self, notification: Notification) -> bool:
        """Send Telegram notification."""
        if not self.config['telegram']['enabled']:
            return False
        
        try:
            # Format message for Telegram
            emoji_map = {
                NotificationType.INFO: "ℹ️",
                NotificationType.WARNING: "⚠️",
                NotificationType.ERROR: "❌",
                NotificationType.CRITICAL: "🚨",
                NotificationType.SUCCESS: "✅"
            }
            
            emoji = emoji_map.get(notification.notification_type, "📢")
            message = f"{emoji} *{notification.title}*\n\n{notification.message}"
            
            bot_token = self.config['telegram']['bot_token']
            
            success_count = 0
            for chat_id in self.config['telegram']['chat_ids']:
                try:
                    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                    payload = {
                        "chat_id": chat_id,
                        "text": message,
                        "parse_mode": "Markdown"
                    }
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=payload) as response:
                            if response.status == 200:
                                success_count += 1
                                
                except Exception as e:
                    self.logger.warning(f"Failed to send to Telegram chat {chat_id}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Telegram sending failed: {e}")
            return False
    
    async def _send_webhook(self, notification: Notification) -> bool:
        """Send webhook notification."""
        if not self.config['webhook']['enabled']:
            return False
        
        try:
            payload = {
                "title": notification.title,
                "message": notification.message,
                "type": notification.notification_type.value,
                "timestamp": notification.timestamp.isoformat(),
                "metadata": notification.metadata or {}
            }
            
            success_count = 0
            for url in self.config['webhook']['urls']:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=payload) as response:
                            if response.status in [200, 201, 202]:
                                success_count += 1
                                
                except Exception as e:
                    self.logger.warning(f"Failed to send webhook to {url}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Webhook sending failed: {e}")
            return False
    
    def _create_email_html(self, notification: Notification) -> str:
        """Create HTML email body."""
        color_map = {
            NotificationType.INFO: "#3498db",
            NotificationType.WARNING: "#f39c12",
            NotificationType.ERROR: "#e74c3c",
            NotificationType.CRITICAL: "#8b0000",
            NotificationType.SUCCESS: "#2ecc71"
        }
        
        color = color_map.get(notification.notification_type, "#3498db")
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; border: 1px solid #ddd; border-radius: 8px;">
                <div style="background-color: {color}; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
                    <h2 style="margin: 0;">{notification.title}</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">
                        {notification.notification_type.value.upper()} - {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
                    </p>
                </div>
                <div style="padding: 20px;">
                    <p style="font-size: 16px; line-height: 1.5; margin: 0;">
                        {notification.message.replace('\n', '<br>')}
                    </p>
                </div>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center;">
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">
                        Bitcoin Trading System - Automated Alert
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
    
    # Convenience methods for different alert types
    async def send_trade_alert(self, symbol: str, action: str, price: float, quantity: float):
        """Send trading alert."""
        notification = Notification(
            title=f"Trade Executed: {symbol}",
            message=f"Action: {action}\nSymbol: {symbol}\nPrice: ${price:,.2f}\nQuantity: {quantity}",
            notification_type=NotificationType.SUCCESS,
            channels=[NotificationChannel.SLACK, NotificationChannel.TELEGRAM],
            timestamp=datetime.now(),
            metadata={"symbol": symbol, "action": action, "price": price, "quantity": quantity}
        )
        return await self.send_notification(notification)
    
    async def send_risk_alert(self, message: str, risk_level: str):
        """Send risk management alert."""
        notification_type = NotificationType.CRITICAL if risk_level == "high" else NotificationType.WARNING
        
        notification = Notification(
            title=f"Risk Alert - {risk_level.upper()}",
            message=message,
            notification_type=notification_type,
            channels=[NotificationChannel.EMAIL, NotificationChannel.SLACK, NotificationChannel.DISCORD],
            timestamp=datetime.now(),
            metadata={"risk_level": risk_level}
        )
        return await self.send_notification(notification)
    
    async def send_system_alert(self, title: str, message: str, alert_type: str = "info"):
        """Send system status alert."""
        type_map = {
            "info": NotificationType.INFO,
            "warning": NotificationType.WARNING,
            "error": NotificationType.ERROR,
            "critical": NotificationType.CRITICAL,
            "success": NotificationType.SUCCESS
        }
        
        notification = Notification(
            title=title,
            message=message,
            notification_type=type_map.get(alert_type, NotificationType.INFO),
            channels=[NotificationChannel.SLACK, NotificationChannel.TELEGRAM],
            timestamp=datetime.now()
        )
        return await self.send_notification(notification)
    
    async def send_performance_report(self, stats: Dict[str, Any]):
        """Send performance report."""
        message = f"""
📊 Daily Performance Report

💰 Total PnL: ${stats.get('total_pnl', 0):,.2f}
📈 Win Rate: {stats.get('win_rate', 0):.1f}%
🔢 Total Trades: {stats.get('total_trades', 0)}
💼 Portfolio Value: ${stats.get('portfolio_value', 0):,.2f}
📉 Max Drawdown: {stats.get('max_drawdown', 0):.2f}%
⚡ Sharpe Ratio: {stats.get('sharpe_ratio', 0):.2f}
        """
        
        notification = Notification(
            title="Daily Performance Report",
            message=message.strip(),
            notification_type=NotificationType.INFO,
            channels=[NotificationChannel.EMAIL, NotificationChannel.SLACK],
            timestamp=datetime.now(),
            metadata=stats
        )
        return await self.send_notification(notification)
    
    def get_notification_history(self, limit: int = 100) -> List[Notification]:
        """Get recent notification history."""
        return self.notification_history[-limit:]
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """Get notification statistics."""
        if not self.notification_history:
            return {}
        
        total = len(self.notification_history)
        by_type = {}
        by_channel = {}
        
        for notification in self.notification_history:
            # Count by type
            type_key = notification.notification_type.value
            by_type[type_key] = by_type.get(type_key, 0) + 1
            
            # Count by channel
            for channel in notification.channels:
                channel_key = channel.value
                by_channel[channel_key] = by_channel.get(channel_key, 0) + 1
        
        return {
            'total_notifications': total,
            'by_type': by_type,
            'by_channel': by_channel,
            'last_notification': self.notification_history[-1].timestamp.isoformat() if total > 0 else None
        }


async def main():
    """Example usage of NotificationManager."""
    # Initialize notification manager
    config = {
        'slack': {
            'enabled': True,
            'webhook_url': 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
            'channel': '#trading-alerts',
            'username': 'TradingBot'
        },
        'telegram': {
            'enabled': True,
            'bot_token': 'YOUR_BOT_TOKEN',
            'chat_ids': ['YOUR_CHAT_ID']
        }
    }
    
    manager = NotificationManager(config)
    
    # Send different types of alerts
    await manager.send_trade_alert("BTCUSDT", "BUY", 45000, 0.1)
    await manager.send_risk_alert("Portfolio drawdown exceeds 10%", "high")
    await manager.send_system_alert("System Started", "Trading engine is now running", "success")
    
    # Send performance report
    stats = {
        'total_pnl': 1250.50,
        'win_rate': 65.5,
        'total_trades': 45,
        'portfolio_value': 105000,
        'max_drawdown': 8.2,
        'sharpe_ratio': 1.85
    }
    await manager.send_performance_report(stats)
    
    # Get statistics
    notification_stats = manager.get_notification_stats()
    print(f"Notification stats: {notification_stats}")


if __name__ == "__main__":
    asyncio.run(main())
