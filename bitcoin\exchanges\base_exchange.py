"""
Base exchange interface for multi-exchange support.
Provides unified API for different cryptocurrency exchanges.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import pandas as pd
from datetime import datetime
from enum import Enum


class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class ExchangeError(Exception):
    """Base exception for exchange-related errors."""
    pass


class InsufficientFundsError(ExchangeError):
    """Raised when account has insufficient funds for order."""
    pass


class InvalidSymbolError(ExchangeError):
    """Raised when trading symbol is not supported."""
    pass


class RateLimitError(ExchangeError):
    """Raised when API rate limit is exceeded."""
    pass


class BaseExchange(ABC):
    """
    Abstract base class for cryptocurrency exchanges.
    All exchange implementations must inherit from this class.
    """
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.testnet = testnet
        self.name = self.__class__.__name__.replace('Exchange', '').lower()
        
    @abstractmethod
    def get_account_balance(self) -> Dict[str, float]:
        """
        Get account balance for all assets.
        
        Returns:
            Dict mapping asset symbols to available balances
        """
        pass
    
    @abstractmethod
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        Get current ticker information for a symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            
        Returns:
            Dict containing price, volume, and other ticker data
        """
        pass
    
    @abstractmethod
    def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, List]:
        """
        Get order book for a symbol.
        
        Args:
            symbol: Trading pair symbol
            limit: Number of orders to return
            
        Returns:
            Dict with 'bids' and 'asks' lists
        """
        pass
    
    @abstractmethod
    def get_klines(self, 
                   symbol: str, 
                   interval: str, 
                   limit: int = 500,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        Get historical kline/candlestick data.
        
        Args:
            symbol: Trading pair symbol
            interval: Time interval (1m, 5m, 1h, 1d, etc.)
            limit: Number of klines to return
            start_time: Start time for data
            end_time: End time for data
            
        Returns:
            DataFrame with OHLCV data
        """
        pass
    
    @abstractmethod
    def place_order(self,
                    symbol: str,
                    side: OrderSide,
                    order_type: OrderType,
                    quantity: float,
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    time_in_force: str = "GTC") -> Dict[str, Any]:
        """
        Place a trading order.
        
        Args:
            symbol: Trading pair symbol
            side: Order side (buy/sell)
            order_type: Type of order
            quantity: Order quantity
            price: Order price (for limit orders)
            stop_price: Stop price (for stop orders)
            time_in_force: Time in force (GTC, IOC, FOK)
            
        Returns:
            Dict containing order information
        """
        pass
    
    @abstractmethod
    def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """
        Cancel an existing order.
        
        Args:
            symbol: Trading pair symbol
            order_id: Order ID to cancel
            
        Returns:
            Dict containing cancellation result
        """
        pass
    
    @abstractmethod
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """
        Get status of an order.
        
        Args:
            symbol: Trading pair symbol
            order_id: Order ID to check
            
        Returns:
            Dict containing order status and details
        """
        pass
    
    @abstractmethod
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all open orders.
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            List of open orders
        """
        pass
    
    @abstractmethod
    def get_trade_history(self, 
                         symbol: str, 
                         limit: int = 500,
                         start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Get trade history.
        
        Args:
            symbol: Trading pair symbol
            limit: Number of trades to return
            start_time: Start time for trades
            end_time: End time for trades
            
        Returns:
            List of trade records
        """
        pass
    
    @abstractmethod
    def get_exchange_info(self) -> Dict[str, Any]:
        """
        Get exchange information including trading rules and symbols.
        
        Returns:
            Dict containing exchange information
        """
        pass
    
    def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """
        Get trading fees for a symbol.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            Dict with maker and taker fees
        """
        # Default implementation - should be overridden by exchanges
        return {
            'maker': 0.001,  # 0.1%
            'taker': 0.001   # 0.1%
        }
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate if symbol is supported by the exchange.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            True if symbol is valid
        """
        try:
            exchange_info = self.get_exchange_info()
            symbols = [s['symbol'] for s in exchange_info.get('symbols', [])]
            return symbol in symbols
        except:
            return False
    
    def format_quantity(self, symbol: str, quantity: float) -> float:
        """
        Format quantity according to exchange rules.
        
        Args:
            symbol: Trading pair symbol
            quantity: Raw quantity
            
        Returns:
            Formatted quantity
        """
        # Default implementation - should be overridden for precision rules
        return round(quantity, 8)
    
    def format_price(self, symbol: str, price: float) -> float:
        """
        Format price according to exchange rules.
        
        Args:
            symbol: Trading pair symbol
            price: Raw price
            
        Returns:
            Formatted price
        """
        # Default implementation - should be overridden for precision rules
        return round(price, 8)
    
    def calculate_order_value(self, symbol: str, quantity: float, price: float) -> float:
        """
        Calculate total order value including fees.
        
        Args:
            symbol: Trading pair symbol
            quantity: Order quantity
            price: Order price
            
        Returns:
            Total order value
        """
        base_value = quantity * price
        fees = self.get_trading_fees(symbol)
        fee_amount = base_value * fees['taker']  # Assume taker fee
        return base_value + fee_amount
    
    def __str__(self) -> str:
        return f"{self.name.title()}Exchange({'Testnet' if self.testnet else 'Mainnet'})"
    
    def __repr__(self) -> str:
        return self.__str__()
