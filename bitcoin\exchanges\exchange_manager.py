"""
Exchange Manager for multi-exchange trading operations.
Provides unified interface for trading across multiple exchanges.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import asyncio
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exchanges.base_exchange import BaseExchange, OrderType, OrderSide, ExchangeError
from exchanges.binance_exchange import BinanceExchange


class ExchangeManager:
    """
    Manages multiple exchanges and provides unified trading interface.
    """
    
    def __init__(self):
        self.exchanges: Dict[str, BaseExchange] = {}
        self.logger = logging.getLogger(__name__)
        
    def add_exchange(self, name: str, exchange: BaseExchange):
        """Add an exchange to the manager."""
        self.exchanges[name] = exchange
        self.logger.info(f"Added exchange: {name}")
    
    def remove_exchange(self, name: str):
        """Remove an exchange from the manager."""
        if name in self.exchanges:
            del self.exchanges[name]
            self.logger.info(f"Removed exchange: {name}")
    
    def get_exchange(self, name: str) -> Optional[BaseExchange]:
        """Get exchange by name."""
        return self.exchanges.get(name)
    
    def list_exchanges(self) -> List[str]:
        """List all available exchanges."""
        return list(self.exchanges.keys())
    
    def get_best_price(self, symbol: str, side: OrderSide) -> Dict[str, Any]:
        """
        Find the best price across all exchanges.
        
        Args:
            symbol: Trading pair symbol
            side: Order side (buy/sell)
            
        Returns:
            Dict with best price information
        """
        best_price = None
        best_exchange = None
        prices = {}
        
        for name, exchange in self.exchanges.items():
            try:
                ticker = exchange.get_ticker(symbol)
                
                if side == OrderSide.BUY:
                    # For buying, we want the lowest ask price
                    price = ticker['ask']
                    if best_price is None or price < best_price:
                        best_price = price
                        best_exchange = name
                else:
                    # For selling, we want the highest bid price
                    price = ticker['bid']
                    if best_price is None or price > best_price:
                        best_price = price
                        best_exchange = name
                
                prices[name] = {
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'spread': ticker['ask'] - ticker['bid']
                }
                
            except Exception as e:
                self.logger.warning(f"Failed to get price from {name}: {e}")
                continue
        
        return {
            'best_exchange': best_exchange,
            'best_price': best_price,
            'side': side.value,
            'all_prices': prices,
            'symbol': symbol
        }
    
    def get_arbitrage_opportunities(self, symbol: str, min_profit_pct: float = 0.1) -> List[Dict[str, Any]]:
        """
        Find arbitrage opportunities across exchanges.
        
        Args:
            symbol: Trading pair symbol
            min_profit_pct: Minimum profit percentage to consider
            
        Returns:
            List of arbitrage opportunities
        """
        opportunities = []
        prices = {}
        
        # Get prices from all exchanges
        for name, exchange in self.exchanges.items():
            try:
                ticker = exchange.get_ticker(symbol)
                prices[name] = {
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'exchange': exchange
                }
            except Exception as e:
                self.logger.warning(f"Failed to get price from {name}: {e}")
                continue
        
        # Find arbitrage opportunities
        for buy_exchange, buy_data in prices.items():
            for sell_exchange, sell_data in prices.items():
                if buy_exchange == sell_exchange:
                    continue
                
                buy_price = buy_data['ask']  # Price to buy
                sell_price = sell_data['bid']  # Price to sell
                
                if sell_price > buy_price:
                    profit_pct = ((sell_price - buy_price) / buy_price) * 100
                    
                    if profit_pct >= min_profit_pct:
                        # Get trading fees
                        buy_fees = buy_data['exchange'].get_trading_fees(symbol)
                        sell_fees = sell_data['exchange'].get_trading_fees(symbol)
                        
                        # Calculate net profit after fees
                        total_fees_pct = buy_fees['taker'] + sell_fees['taker']
                        net_profit_pct = profit_pct - (total_fees_pct * 100)
                        
                        if net_profit_pct > 0:
                            opportunities.append({
                                'buy_exchange': buy_exchange,
                                'sell_exchange': sell_exchange,
                                'buy_price': buy_price,
                                'sell_price': sell_price,
                                'gross_profit_pct': profit_pct,
                                'net_profit_pct': net_profit_pct,
                                'fees_pct': total_fees_pct * 100,
                                'symbol': symbol
                            })
        
        # Sort by net profit
        opportunities.sort(key=lambda x: x['net_profit_pct'], reverse=True)
        return opportunities
    
    def get_aggregated_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """
        Get aggregated order book from all exchanges.
        
        Args:
            symbol: Trading pair symbol
            limit: Number of orders per exchange
            
        Returns:
            Aggregated order book
        """
        all_bids = []
        all_asks = []
        
        for name, exchange in self.exchanges.items():
            try:
                orderbook = exchange.get_orderbook(symbol, limit)
                
                # Add exchange info to orders
                for price, qty in orderbook['bids']:
                    all_bids.append([price, qty, name])
                
                for price, qty in orderbook['asks']:
                    all_asks.append([price, qty, name])
                    
            except Exception as e:
                self.logger.warning(f"Failed to get orderbook from {name}: {e}")
                continue
        
        # Sort bids (highest first) and asks (lowest first)
        all_bids.sort(key=lambda x: x[0], reverse=True)
        all_asks.sort(key=lambda x: x[0])
        
        return {
            'bids': all_bids[:limit],
            'asks': all_asks[:limit],
            'symbol': symbol,
            'exchanges': list(self.exchanges.keys())
        }
    
    def execute_arbitrage(self, 
                         opportunity: Dict[str, Any], 
                         quantity: float) -> Dict[str, Any]:
        """
        Execute arbitrage trade across exchanges.
        
        Args:
            opportunity: Arbitrage opportunity from get_arbitrage_opportunities
            quantity: Quantity to trade
            
        Returns:
            Execution results
        """
        buy_exchange_name = opportunity['buy_exchange']
        sell_exchange_name = opportunity['sell_exchange']
        symbol = opportunity['symbol']
        
        buy_exchange = self.exchanges[buy_exchange_name]
        sell_exchange = self.exchanges[sell_exchange_name]
        
        results = {
            'success': False,
            'buy_order': None,
            'sell_order': None,
            'error': None
        }
        
        try:
            # Execute buy order
            buy_order = buy_exchange.place_order(
                symbol=symbol,
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=quantity
            )
            results['buy_order'] = buy_order
            
            # Execute sell order
            sell_order = sell_exchange.place_order(
                symbol=symbol,
                side=OrderSide.SELL,
                order_type=OrderType.MARKET,
                quantity=quantity
            )
            results['sell_order'] = sell_order
            
            results['success'] = True
            self.logger.info(f"Arbitrage executed: {buy_exchange_name} -> {sell_exchange_name}")
            
        except Exception as e:
            results['error'] = str(e)
            self.logger.error(f"Arbitrage execution failed: {e}")
            
            # Try to cancel any partial orders
            if results['buy_order']:
                try:
                    buy_exchange.cancel_order(symbol, results['buy_order']['order_id'])
                except:
                    pass
        
        return results
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary across all exchanges."""
        total_balances = {}
        exchange_balances = {}
        
        for name, exchange in self.exchanges.items():
            try:
                balances = exchange.get_account_balance()
                exchange_balances[name] = balances
                
                # Aggregate totals
                for asset, balance_info in balances.items():
                    if asset not in total_balances:
                        total_balances[asset] = {
                            'total': 0,
                            'free': 0,
                            'locked': 0,
                            'exchanges': {}
                        }
                    
                    total_balances[asset]['total'] += balance_info['total']
                    total_balances[asset]['free'] += balance_info['free']
                    total_balances[asset]['locked'] += balance_info['locked']
                    total_balances[asset]['exchanges'][name] = balance_info
                    
            except Exception as e:
                self.logger.warning(f"Failed to get balance from {name}: {e}")
                exchange_balances[name] = {}
        
        return {
            'total_balances': total_balances,
            'exchange_balances': exchange_balances,
            'exchanges': list(self.exchanges.keys())
        }
    
    def monitor_arbitrage(self, 
                         symbols: List[str], 
                         min_profit_pct: float = 0.1,
                         interval: int = 30) -> None:
        """
        Monitor arbitrage opportunities continuously.
        
        Args:
            symbols: List of symbols to monitor
            min_profit_pct: Minimum profit percentage
            interval: Check interval in seconds
        """
        self.logger.info(f"Starting arbitrage monitoring for {symbols}")
        
        while True:
            try:
                for symbol in symbols:
                    opportunities = self.get_arbitrage_opportunities(symbol, min_profit_pct)
                    
                    if opportunities:
                        self.logger.info(f"Found {len(opportunities)} arbitrage opportunities for {symbol}")
                        
                        for opp in opportunities[:3]:  # Show top 3
                            self.logger.info(
                                f"  {opp['buy_exchange']} -> {opp['sell_exchange']}: "
                                f"{opp['net_profit_pct']:.2f}% profit"
                            )
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("Arbitrage monitoring stopped")
                break
            except Exception as e:
                self.logger.error(f"Error in arbitrage monitoring: {e}")
                time.sleep(interval)


def main():
    """Example usage of ExchangeManager."""
    # Initialize manager
    manager = ExchangeManager()
    
    # Add exchanges (with test credentials)
    binance = BinanceExchange(
        api_key="test_api_key",
        secret_key="test_secret_key",
        testnet=True
    )
    manager.add_exchange("binance", binance)
    
    # You could add more exchanges here
    # manager.add_exchange("coinbase", coinbase_exchange)
    # manager.add_exchange("kraken", kraken_exchange)
    
    try:
        # Get best price
        best_price = manager.get_best_price("BTCUSDT", OrderSide.BUY)
        print(f"Best price to buy BTC: {best_price}")
        
        # Check arbitrage opportunities
        opportunities = manager.get_arbitrage_opportunities("BTCUSDT", min_profit_pct=0.1)
        print(f"Found {len(opportunities)} arbitrage opportunities")
        
        # Get portfolio summary
        portfolio = manager.get_portfolio_summary()
        print(f"Portfolio across {len(portfolio['exchanges'])} exchanges")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
