#!/bin/bash

# Bitcoin Trading System Deployment Script
# Supports local, Docker, and Kubernetes deployments

set -e

# Configuration
PROJECT_NAME="bitcoin-trading"
DOCKER_IMAGE="ghcr.io/your-username/bitcoin-trading"
NAMESPACE="trading"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_warning "Docker Compose not found, trying docker compose..."
        if ! docker compose version &> /dev/null; then
            log_error "Docker Compose is not available"
            exit 1
        fi
        DOCKER_COMPOSE="docker compose"
    else
        DOCKER_COMPOSE="docker-compose"
    fi
    
    log_success "Prerequisites check passed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    # Build multi-platform image
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --tag ${DOCKER_IMAGE}:latest \
        --tag ${DOCKER_IMAGE}:$(date +%Y%m%d-%H%M%S) \
        --push \
        .
    
    log_success "Docker image built and pushed"
}

# Deploy locally with Docker Compose
deploy_local() {
    log_info "Deploying locally with Docker Compose..."
    
    # Create necessary directories
    mkdir -p data logs reports nginx/ssl
    
    # Generate self-signed SSL certificates if they don't exist
    if [ ! -f nginx/ssl/cert.pem ]; then
        log_info "Generating self-signed SSL certificates..."
        openssl req -x509 -newkey rsa:4096 -keyout nginx/ssl/key.pem -out nginx/ssl/cert.pem -days 365 -nodes \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    fi
    
    # Start services
    ${DOCKER_COMPOSE} up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Health check
    if curl -f http://localhost:8501/healthz > /dev/null 2>&1; then
        log_success "Local deployment successful!"
        log_info "Dashboard: http://localhost:8501"
        log_info "API: http://localhost:8000"
        log_info "Grafana: http://localhost:3000"
        log_info "Prometheus: http://localhost:9090"
    else
        log_error "Health check failed"
        ${DOCKER_COMPOSE} logs
        exit 1
    fi
}

# Deploy to Kubernetes
deploy_k8s() {
    log_info "Deploying to Kubernetes..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Create namespace
    kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply secrets (you should create these manually with real values)
    log_warning "Make sure to create secrets manually:"
    log_warning "kubectl create secret generic trading-secrets -n ${NAMESPACE} \\"
    log_warning "  --from-literal=database-url='postgresql://...' \\"
    log_warning "  --from-literal=binance-api-key='your-api-key' \\"
    log_warning "  --from-literal=binance-secret-key='your-secret-key'"
    
    # Apply Kubernetes manifests
    kubectl apply -f k8s/ -n ${NAMESPACE}
    
    # Wait for deployment
    kubectl rollout status deployment/bitcoin-trading-app -n ${NAMESPACE}
    
    # Get service URL
    SERVICE_URL=$(kubectl get ingress bitcoin-trading-ingress -n ${NAMESPACE} -o jsonpath='{.spec.rules[0].host}')
    
    log_success "Kubernetes deployment successful!"
    log_info "Service URL: https://${SERVICE_URL}"
}

# Deploy to cloud (AWS EKS example)
deploy_cloud() {
    log_info "Deploying to cloud (AWS EKS)..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check eksctl
    if ! command -v eksctl &> /dev/null; then
        log_error "eksctl is not installed"
        exit 1
    fi
    
    # Create EKS cluster (if it doesn't exist)
    CLUSTER_NAME="${PROJECT_NAME}-cluster"
    if ! eksctl get cluster ${CLUSTER_NAME} &> /dev/null; then
        log_info "Creating EKS cluster..."
        eksctl create cluster \
            --name ${CLUSTER_NAME} \
            --region us-west-2 \
            --nodegroup-name ${PROJECT_NAME}-nodes \
            --node-type t3.medium \
            --nodes 3 \
            --nodes-min 1 \
            --nodes-max 5 \
            --managed
    fi
    
    # Update kubeconfig
    aws eks update-kubeconfig --region us-west-2 --name ${CLUSTER_NAME}
    
    # Deploy to EKS
    deploy_k8s
    
    log_success "Cloud deployment successful!"
}

# Rollback deployment
rollback() {
    log_info "Rolling back deployment..."
    
    if [ "$DEPLOYMENT_TYPE" = "k8s" ] || [ "$DEPLOYMENT_TYPE" = "cloud" ]; then
        kubectl rollout undo deployment/bitcoin-trading-app -n ${NAMESPACE}
        kubectl rollout status deployment/bitcoin-trading-app -n ${NAMESPACE}
    else
        # For local deployment, restart with previous image
        ${DOCKER_COMPOSE} down
        ${DOCKER_COMPOSE} up -d
    fi
    
    log_success "Rollback completed"
}

# Cleanup deployment
cleanup() {
    log_info "Cleaning up deployment..."
    
    case $DEPLOYMENT_TYPE in
        "local")
            ${DOCKER_COMPOSE} down -v
            docker system prune -f
            ;;
        "k8s")
            kubectl delete namespace ${NAMESPACE}
            ;;
        "cloud")
            kubectl delete namespace ${NAMESPACE}
            # Optionally delete EKS cluster
            # eksctl delete cluster ${PROJECT_NAME}-cluster
            ;;
    esac
    
    log_success "Cleanup completed"
}

# Monitor deployment
monitor() {
    log_info "Monitoring deployment..."
    
    case $DEPLOYMENT_TYPE in
        "local")
            ${DOCKER_COMPOSE} logs -f
            ;;
        "k8s"|"cloud")
            kubectl logs -f deployment/bitcoin-trading-app -n ${NAMESPACE}
            ;;
    esac
}

# Main deployment function
main() {
    DEPLOYMENT_TYPE=${1:-"local"}
    ACTION=${2:-"deploy"}
    
    log_info "Bitcoin Trading System Deployment"
    log_info "Deployment Type: $DEPLOYMENT_TYPE"
    log_info "Action: $ACTION"
    
    case $ACTION in
        "deploy")
            check_prerequisites
            
            case $DEPLOYMENT_TYPE in
                "local")
                    deploy_local
                    ;;
                "k8s")
                    build_image
                    deploy_k8s
                    ;;
                "cloud")
                    build_image
                    deploy_cloud
                    ;;
                *)
                    log_error "Invalid deployment type: $DEPLOYMENT_TYPE"
                    echo "Usage: $0 [local|k8s|cloud] [deploy|rollback|cleanup|monitor]"
                    exit 1
                    ;;
            esac
            ;;
        "rollback")
            rollback
            ;;
        "cleanup")
            cleanup
            ;;
        "monitor")
            monitor
            ;;
        *)
            log_error "Invalid action: $ACTION"
            echo "Usage: $0 [local|k8s|cloud] [deploy|rollback|cleanup|monitor]"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
